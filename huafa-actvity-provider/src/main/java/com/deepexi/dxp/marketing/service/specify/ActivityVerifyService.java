package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.MyVerifyQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.VerifyPageQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityVerifyRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyInResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ExcelExportActVerifyResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.OrderDetailResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.VerifyOrderResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.VerifyOrderPageQuery;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityVerifyDO;
import com.deepexi.util.pageHelper.PageBean;

import java.util.Date;
import java.util.List;

/**
 * 核销管理
 */
public interface ActivityVerifyService {

    /**
     * 核销管理列表
     * @param query
     * @return
     */
    PageBean<ActivityVerifyInResponseDTO> findPage(VerifyPageQuery query);


    /**
     * 核销详情
     * @param id
     * @return
     */
    ActivityVerifyInResponseDTO getDetail(Long id);

    /**
     * 核销
     * @param requestDTO
     * @return
     */
    Boolean wiped(ActivityVerifyRequestDTO requestDTO);

    boolean doVerify(ActivityVerifyRequestDTO requestDTO, ActivityVerifyDO activityVerifyDO);

    /**
     * 驳回
     * @param requestDTO
     * @return
     */
    Boolean reject(ActivityVerifyRequestDTO requestDTO);

    /**
     *退款
     * @param requestDTO
     * @return
     */
    Boolean refunds(ActivityVerifyRequestDTO requestDTO);

    /**
     * 个人礼品
     * @param query
     * @return
     */
    PageBean<ActivityVerifyInResponseDTO> myList(MyVerifyQuery query);

    /**
     * 核销退款业务处理
     * @param oldPayOrderNo
     * @param status
     * @return
     */
    Boolean refundsProcess(String oldPayOrderNo, String wxOrderNo, String status, Date refundsTime);

    /**
     * 获取二维码base64
     * @param code
     * @return
     */
    String getQrCode(String code);

    /**
     * 所有已过期核销记录
     * @return
     */
    List<ActivityVerifyInResponseDTO> findOverList();

    /**
     * 将已过期核销记录设置为已过期
     * @param activityVerifyDO
     */
    void activityVerifyToOver(ActivityVerifyInResponseDTO activityVerifyDO);

    /**
     * 手动发送红包激励
     * @param verifyId
     * @return
     */
    Boolean sendIncentiveManual(Long verifyId);

    /**
     * 导出核销数据
     * @param query
     * @return
     */
    ExcelExportActVerifyResponseDTO getExportVerifyExcelData(VerifyPageQuery query);

    ActivityVerifyDO getByOrderId(Long orderId);

    PageBean<VerifyOrderResponseDTO> orderList(VerifyOrderPageQuery query);

    OrderDetailResponseDTO orderDetail(String code);
}
