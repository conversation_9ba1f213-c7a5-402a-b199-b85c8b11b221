package com.deepexi.dxp.marketing.controller.specify.middleapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CustomerFeedbackQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.CustomerFeedbackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityFormFeedbackResponseVO;
import com.deepexi.dxp.marketing.domain.marketing.response.ExcelExportActPartakeLogResponseDTO;
import com.deepexi.dxp.marketing.service.specify.CustomerFeedbackService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;

/**
 * 用户反馈相关接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/middle-api/v1/customerFeedback")
@Api(value = "middle-用户反馈",tags = {"middle-用户反馈接口"})
public class MiddleCustomerFeedbackController {

    @Resource
    public CustomerFeedbackService customerFeedbackService;

    @PostMapping("/save")
    @ApiOperation(value = "用户反馈信息保存", notes = "用户反馈信息保存")
    public Data<Boolean> save(@RequestBody @Valid CustomerFeedbackRequestDTO requestDTO) {
        if (Objects.isNull(requestDTO)){
            throw new ApplicationException("入参对象不能为空");
        }
        return new Data<>(customerFeedbackService.save(requestDTO));
    }

    @PostMapping("/pageList")
    @ApiOperation(value = "用户反馈信息分页查询", notes = "用户反馈信息分页查询")
    public Data<PageBean<ActivityFormFeedbackResponseVO>> pageList(@RequestBody CustomerFeedbackQuery query) {
        if (Objects.isNull(query)){
            throw new ApplicationException("入参对象不能为空");
        }
        query.setUserId(null);
        query.setCreatedBy(null);
        return new Data<>(customerFeedbackService.pageList(query));
    }

    @GetMapping("/detail")
    @ApiOperation(value = "用户反馈信息——详情", notes = "用户反馈信息——详情")
    public Data<ActivityFormFeedbackResponseVO> detail(@RequestParam Long id) {
        return new Data<>(customerFeedbackService.detail(id));
    }

    @ApiOperation(value="反馈列表导出", notes = "反馈列表导出")
    @PostMapping("/exportFormFeedbackExcel")
    public ExcelExportActPartakeLogResponseDTO exportFormFeedbackExcel(@RequestBody  CustomerFeedbackQuery query){
        if(query != null){
            query.setUserId(null);
            query.setCreatedBy(null);
        }
       return  customerFeedbackService.exportFormFeedbackExcel(query);
    }
}
