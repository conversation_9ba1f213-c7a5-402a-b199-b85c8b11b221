package com.deepexi.dxp.marketing.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.deepexi.dxp.marketing.constant.PromotionActivityConstant;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.EnrollmentInfoVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.FeedbackInfoVO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityPartakeLogResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ExcelExportActPartakeLogResponseDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.resource.ResourceGrantWayEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFissionLogDO;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.StringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description excel导出
 * @date 2020/4/26
 */
@Data
public class GetExcelExportActPartakeLogUtil {

    private static GetExcelExportActPartakeLogUtil instance = null;

    public static GetExcelExportActPartakeLogUtil getInstance(){
        if (null == instance) {
            synchronized (GetExcelExportActPartakeLogUtil.class) {
                if (null == instance) {
                    instance = new GetExcelExportActPartakeLogUtil();
                }
            }
        }
        return instance;
    }

    //字体大小
    private int fontSize = 14;
    //行高
    private int rowHeight = 30;
    //列宽
    private int columWidth = 200;

    /**
     * 导出抽奖活动参与记录
     */
    public ExcelExportActPartakeLogResponseDTO exportLuckyDrawActInfo(List<ActivityPartakeLogResponseDTO> content, Long paTemplateId ,List<EnrollmentInfoVO> feedbackInfo) throws IOException {

        List<Map<String, String>> dataList = new ArrayList<>();

        String title = StrategyGroupEnum.getById(String.valueOf(paTemplateId)).getValue();
        String fileName = title + "-参与记录";

        String[] heardList1 = new String[]{"序号","昵称","手机号","抽奖时间","中奖结果","核销码","领取时间","领取方式"};
        String[] headerKey1 = new String[]{"","nickName","phone","createTime","resourceName","code","getTime","grantWay"};

        //重新生成headList headerkye
        Map<String, String[]> heardMap = getHeardMap(heardList1, headerKey1, feedbackInfo);
        String[] heardList = heardMap.get("heardList");
        String[] headerKey = heardMap.get("headerKey");

        for (int i = 0; i < content.size(); i++) {
            Map<String, String> data = Maps.newHashMap();
            ActivityPartakeLogResponseDTO dto = content.get(i);
            data.put(headerKey[0], (i+1) + "");
            data.put(headerKey[1], dto.getNickName());
            data.put(headerKey[2], PhoneEncryUtils.encode(dto.getPhone()));
            data.put(headerKey[3], DateUtils.format(dto.getCreatedTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
            data.put(headerKey[4], Objects.equals(dto.getPrizeResult(), PromotionActivityConstant.THANK_PARTICIPATION)?PromotionActivityConstant.NOT_WINNING:dto.getPrizeResult());
            data.put(headerKey[5], dto.getCode());
            data.put(headerKey[6], dto.getGetTime());
            String grantWay = "";
            if (!Objects.equals(dto.getPrizeResult(), PromotionActivityConstant.THANK_PARTICIPATION) && StringUtil.isNotBlank(dto.getGrantWay())) {
                grantWay = ResourceGrantWayEnum.getValueById(Integer.parseInt(dto.getGrantWay()));
            }
            data.put(headerKey[7], grantWay);

            if(Objects.nonNull(dto.getFeedback())){
//                FeedbackInfoVO feedbackInfoVO = (FeedbackInfoVO)MapBeanUtil.map2Object(dto.getFeedback().getLimits(), FeedbackInfoVO.class);
//                feedbackInfoVO.setCardText(JSON.toJSONString(dto.getFeedback().getLimits().get("cardText")));
                //"姓名","电话","地区","详细地址","身份证","港澳通行证","其他字段"
//                data.put(headerKey[8], feedbackInfoVO.getUserName());
               /* data.put(headerKey[12], feedbackInfoVO.getPhone());
                data.put(headerKey[13], feedbackInfoVO.getCityName());
                data.put(headerKey[14], feedbackInfoVO.getHouseNum());
                data.put(headerKey[15], feedbackInfoVO.getIdCard());
                data.put(headerKey[16], feedbackInfoVO.getHkmCard());
                data.put(headerKey[17], feedbackInfoVO.getCardText());*/
                Map<String, Object> limits = dto.getFeedback().getLimits();
                JSONArray jsonArray = (JSONArray)limits.get("feedbackInfo");
                for (int k = 0; k < jsonArray.size(); k++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(k);
                    String inputValue = jsonObject.getString("inputValue");
                    /*if (jsonObject.getInteger("type").equals(1)){
                        inputValue = StringUtil.isNotEmpty(inputValue)?PhoneEncryUtils.encode(inputValue):"";
                    }*/
                    data.put(headerKey[8+k],inputValue);
                }
            }

            dataList.add(data);
        }
        return ExcelExportActPartakeLogResponseDTO
                .builder()
                .title(title)
                .fileName(fileName)
                .headerKey(headerKey)
                .heardList(heardList)
                .dataList(dataList)
                .build();
    }

    /**
     * 导出优惠券活动参与记录
     */
    public ExcelExportActPartakeLogResponseDTO exportCouponActInfo(List<ActivityPartakeLogResponseDTO> content, Long paTemplateId,List<EnrollmentInfoVO> feedbackInfo) throws IOException {

        List<Map<String, String>> dataList = new ArrayList<>();

        String title = StrategyGroupEnum.getById(String.valueOf(paTemplateId)).getValue();
        String fileName = title + "-参与记录";

        String[] heardList1 = new String[]{"序号","订单号","昵称","手机号","资源类型","资源名称","资源明细","领取方式","领取时间/支付时间","支付金额","发放方式"};
        String[] headerKey1 = new String[]{"","orderNo","nickName","phone","resourceType","resourceName","resourceDetail","receiveType","getTime","payMoney","grantWay"};

        //重新生成headList headerkye
        Map<String, String[]> heardMap = getHeardMap(heardList1, headerKey1, feedbackInfo);
        String[] heardList = heardMap.get("heardList");
        String[] headerKey = heardMap.get("headerKey");

        //生成表格数据
        for (int i = 0; i < content.size(); i++) {
            Map<String, String> data = Maps.newHashMap();
            ActivityPartakeLogResponseDTO dto = content.get(i);
            //PromotionHisResourceDO promotionHisResourceDO = finalResourceMap.get(dto.getResourceId());
            data.put(headerKey[0], (i+1) + "");
            data.put(headerKey[1], dto.getOrderNo());
            data.put(headerKey[2], dto.getNickName());
            data.put(headerKey[3], PhoneEncryUtils.encode(dto.getPhone()));
            data.put(headerKey[4], PromotionResourceTypeEnum.getValueById(dto.getResourceHisDetailResponseDTO().getType()));
            data.put(headerKey[5], dto.getResourceHisDetailResponseDTO().getName());
            StringBuffer sb = new StringBuffer();
            sb.append("细分类别:").append(PromotionResourceCouponCategoryEnum.getValueById(dto.getResourceHisDetailResponseDTO().getCouponCategory())).append("\t\n");

            BigDecimal couponValue = dto.getResourceHisDetailResponseDTO().getCouponValue();
            if(PromotionResourceTypeEnum.COUPON.getId().equals(dto.getType())){//卡券
                //普通券、房源券
                if(!PromotionResourceCouponCategoryEnum.COMMODITY_COUPON.getId().equals(dto.getResourceHisDetailResponseDTO().getCouponCategory())){
                    sb.append("卡券类型:").append(PromotionResourceCouponTypeEnum.getValueById(dto.getResourceHisDetailResponseDTO().getCouponType())).append("\t\n");
                    if(dto.getResourceHisDetailResponseDTO().getCouponType().equals(PromotionResourceCouponTypeEnum.DISCOUNT_COUPON.getId())){
                        sb.append("卡券面值:").append(couponValue.compareTo(BigDecimal.ONE) < 0 ? couponValue : couponValue.compareTo(couponValue.setScale( 0, BigDecimal.ROUND_DOWN )) == 0 ? couponValue.intValue():couponValue).append("折\t\n");
                    }else if(dto.getResourceHisDetailResponseDTO().getCouponType().equals(PromotionResourceCouponTypeEnum.VOUCHER.getId())){
                        sb.append("卡券面值:").append(couponValue.compareTo(BigDecimal.ONE) < 0 ? couponValue : couponValue.compareTo(couponValue.setScale( 0, BigDecimal.ROUND_DOWN )) == 0 ? couponValue.intValue():couponValue).append("元\t\n");
                    }
                }else{//礼品券
                    sb.append("礼品原价:").append(dto.getResourceHisDetailResponseDTO().getCostPrice()).append("元\t\n");
                }
            }else{//第三方接入
                sb.append(ThirdCategoryEnum.PHONE_FEE.getId().equals(dto.getResourceHisDetailResponseDTO().getThirdCategory()) ? "话费面值:" : "红包面值:").append(couponValue.compareTo(BigDecimal.ONE) < 0 ? couponValue : couponValue.compareTo(couponValue.setScale( 0, BigDecimal.ROUND_DOWN )) == 0 ? couponValue.intValue():couponValue).append("元\t\n");
            }

            //sb.append("房源名称:").append(dto.getResourceHisDetailResponseDTO().getHouseName());
            data.put(headerKey[6], sb.toString());
            String valueById = ReceiveModeEnum.getValueById(dto.getResourceHisDetailResponseDTO().getReceiveMode());
            data.put(headerKey[7], valueById);
            if(ReceiveModeEnum.FREE.getId().equals(dto.getResourceHisDetailResponseDTO().getReceiveMode())){
                data.put(headerKey[8], dto.getGetTime() != null ?dto.getGetTime():"");
            }else{
                data.put(headerKey[8], dto.getPayTime() != null ?DateUtils.format(dto.getPayTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT):"");
            }

            data.put(headerKey[9], dto.getPayMoney() != null ? String.valueOf(dto.getPayMoney()) :"0.00");
            data.put(headerKey[10], ResourceGrantWayEnum.getValueById(dto.getResourceHisDetailResponseDTO().getGrantWay()));

            //登记信息
            if(Objects.nonNull(dto.getFeedback())){
                FeedbackInfoVO feedbackInfoVO = (FeedbackInfoVO)MapBeanUtil.map2Object(dto.getFeedback().getLimits(), FeedbackInfoVO.class);
                feedbackInfoVO.setCardText(JSON.toJSONString(dto.getFeedback().getLimits().get("cardText")));
                //"姓名","电话","地区","详细地址","身份证","港澳通行证","其他字段"

                Map<String, Object> limits = dto.getFeedback().getLimits();
                JSONArray jsonArray = (JSONArray)limits.get("feedbackInfo");
                for (int k = 0; k < jsonArray.size(); k++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(k);
                    String inputValue = jsonObject.getString("inputValue");
                    /*if (jsonObject.getInteger("type").equals(1)){
                        inputValue = StringUtil.isNotEmpty(inputValue)?PhoneEncryUtils.encode(inputValue):"";
                    }*/
                    data.put(headerKey[11+k],inputValue);
                }
            }

            dataList.add(data);
        }
        return ExcelExportActPartakeLogResponseDTO
                .builder()
                .title(title)
                .fileName(fileName)
                .headerKey(headerKey)
                .heardList(heardList)
                .dataList(dataList)
                .build();
    }

    private Map<String,String[]> getHeardMap(String[] heardList1,String[] headerKey1,List<EnrollmentInfoVO> feedbackInfo){
        Map<String,String[]> map = Maps.newHashMap();
        String[] heardList = new String[heardList1.length+feedbackInfo.size()];
        String[] headerKey = new String[headerKey1.length+feedbackInfo.size()];
        System.arraycopy(heardList1,0,heardList,0,heardList1.length);
        System.arraycopy(headerKey1,0,headerKey,0,headerKey1.length);
        if(CollectionUtil.isNotEmpty(feedbackInfo)) {
            for (int i = 0; i < feedbackInfo.size(); i++) {
                EnrollmentInfoVO enrollmentInfoVO = feedbackInfo.get(i);
                int j = heardList1.length + i;
                heardList[j] = enrollmentInfoVO.getValue();
                headerKey[j] = "param" + (i + 1);
            }
        }
        map.put("heardList",heardList);
        map.put("headerKey",headerKey);
        return map;
    }

    /**
     * 导出秒杀活动参与记录
     */
    public ExcelExportActPartakeLogResponseDTO exportSeckillActInfo(List<ActivityPartakeLogResponseDTO> content, Long paTemplateId,List<EnrollmentInfoVO> feedbackInfo) throws IOException {

        List<Map<String, String>> dataList = new ArrayList<>();

        String title = StrategyGroupEnum.getById(String.valueOf(paTemplateId)).getValue();
        String fileName = title + "-参与记录";

        String[] heardList1 = new String[]{"序号","订单号","昵称","手机号","资源类型","资源名称","资源明细","支付时间","支付金额","发放方式"};
        String[] headerKey1 = new String[]{"","orderNo","nickName","phone","resourceType","resourceName","resourceDetail","getTime","payMoney","grantWay"};

        //重新生成headList headerkye
        Map<String, String[]> heardMap = getHeardMap(heardList1, headerKey1, feedbackInfo);
        String[] heardList = heardMap.get("heardList");
        String[] headerKey = heardMap.get("headerKey");

        for (int i = 0; i < content.size(); i++) {
            Map<String, String> data = Maps.newHashMap();
            ActivityPartakeLogResponseDTO dto = content.get(i);
            //PromotionHisResourceDO promotionHisResourceDO = finalResourceMap.get(dto.getResourceId());
            data.put(headerKey[0], (i+1) + "");
            data.put(headerKey[1], dto.getOrderNo());
            data.put(headerKey[2], dto.getNickName());
            data.put(headerKey[3], PhoneEncryUtils.encode(dto.getPhone()));
            data.put(headerKey[4], PromotionResourceTypeEnum.getValueById(dto.getResourceHisDetailResponseDTO().getType()));
            data.put(headerKey[5], dto.getResourceHisDetailResponseDTO().getName());
            StringBuffer sb = new StringBuffer();
            sb.append("细分类别:").append(PromotionResourceCouponCategoryEnum.getValueById(dto.getResourceHisDetailResponseDTO().getCouponCategory())).append("\t\n");
            BigDecimal couponValue = dto.getResourceHisDetailResponseDTO().getCouponValue();
            if(PromotionResourceTypeEnum.COUPON.getId().equals(dto.getType())){//卡券
                //普通券、房源券
                if(!PromotionResourceCouponCategoryEnum.COMMODITY_COUPON.getId().equals(dto.getResourceHisDetailResponseDTO().getCouponCategory())){
                    sb.append("卡券类型:").append(PromotionResourceCouponTypeEnum.getValueById(dto.getResourceHisDetailResponseDTO().getCouponType())).append("\t\n");
                    if(dto.getResourceHisDetailResponseDTO().getCouponType().equals(PromotionResourceCouponTypeEnum.DISCOUNT_COUPON.getId())){
                        sb.append("卡券面值:").append(couponValue.compareTo(BigDecimal.ONE) < 0 ? couponValue : couponValue.compareTo(couponValue.setScale( 0, BigDecimal.ROUND_DOWN )) == 0 ? couponValue.intValue():couponValue).append("折\t\n");
                    }else if(dto.getResourceHisDetailResponseDTO().getCouponType().equals(PromotionResourceCouponTypeEnum.VOUCHER.getId())){
                        sb.append("卡券面值:").append(couponValue.compareTo(BigDecimal.ONE) < 0 ? couponValue : couponValue.compareTo(couponValue.setScale( 0, BigDecimal.ROUND_DOWN )) == 0 ? couponValue.intValue():couponValue).append("元\t\n");
                    }
                }else{//礼品券
                    sb.append("礼品原价:").append(dto.getResourceHisDetailResponseDTO().getCostPrice()).append("元\t\n");
                }
            }else{//第三方接入
                sb.append(ThirdCategoryEnum.PHONE_FEE.getId().equals(dto.getResourceHisDetailResponseDTO().getThirdCategory()) ? "话费面值:" : "红包面值:").append(couponValue.compareTo(BigDecimal.ONE) < 0 ? couponValue : couponValue.compareTo(couponValue.setScale( 0, BigDecimal.ROUND_DOWN )) == 0 ? couponValue.intValue():couponValue).append("元\t\n");
            }
//            sb.append("卡券类型:").append(PromotionResourceCouponTypeEnum.getValueById(dto.getResourceHisDetailResponseDTO().getCouponType())).append("\t\n");
//            if(dto.getResourceHisDetailResponseDTO().getCouponType().equals(PromotionResourceCouponTypeEnum.DISCOUNT_COUPON)){
//                sb.append("卡券面值:").append(dto.getResourceHisDetailResponseDTO().getCouponValue()).append("折\t\n");
//            }else if(dto.getResourceHisDetailResponseDTO().getCouponType().equals(PromotionResourceCouponTypeEnum.VOUCHER)){
//                sb.append("卡券面值:").append(dto.getResourceHisDetailResponseDTO().getCouponValue()).append("元\t\n");
//            }
            //sb.append("房源名称:").append(dto.getResourceHisDetailResponseDTO().getHouseName());
            data.put(headerKey[6], sb.toString());
            //data.put(headerKey[7], ReceiveModeEnum.getValueById(dto.getResourceHisDetailResponseDTO().getReceiveMode()));
            if(ReceiveModeEnum.FREE.getId().equals(dto.getResourceHisDetailResponseDTO().getReceiveMode())){
                data.put(headerKey[7], dto.getGetTime() != null ?dto.getGetTime():"");
            }else{
                data.put(headerKey[7], dto.getPayTime() != null ?DateUtils.format(dto.getPayTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT):"");
            }

            data.put(headerKey[8], dto.getPayMoney() != null ? String.valueOf(dto.getPayMoney()) :"");
            data.put(headerKey[9], ResourceGrantWayEnum.getValueById(dto.getResourceHisDetailResponseDTO().getGrantWay()));

            if(Objects.nonNull(dto.getFeedback())){
                FeedbackInfoVO feedbackInfoVO = (FeedbackInfoVO)MapBeanUtil.map2Object(dto.getFeedback().getLimits(), FeedbackInfoVO.class);
                feedbackInfoVO.setCardText(JSON.toJSONString(dto.getFeedback().getLimits().get("cardText")));
                //"姓名","电话","地区","详细地址","身份证","港澳通行证","其他字段"
                //data.put(headerKey[10], feedbackInfoVO.getUserName());
                Map<String, Object> limits = dto.getFeedback().getLimits();
                JSONArray jsonArray = (JSONArray)limits.get("feedbackInfo");
                for (int k = 0; k < jsonArray.size(); k++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(k);
                    /*if(jsonObject.getInteger("type").equals(1)){
                        data.put(headerKey[10+k],PhoneEncryUtils.encode(jsonObject.getString("inputValue")));
                    }else{*/
                        data.put(headerKey[10+k],jsonObject.getString("inputValue"));
                   // }
                }
        /*        data.put(headerKey[12], feedbackInfoVO.getPhone());
                data.put(headerKey[13], feedbackInfoVO.getCityName());
                data.put(headerKey[14], feedbackInfoVO.getHouseNum());
                data.put(headerKey[15], feedbackInfoVO.getIdCard());
                data.put(headerKey[16], feedbackInfoVO.getHkmCard());
                data.put(headerKey[17], feedbackInfoVO.getCardText());*/
            }

            dataList.add(data);
        }

        return ExcelExportActPartakeLogResponseDTO
                .builder()
                .title(title)
                .fileName(fileName)
                .headerKey(headerKey)
                .heardList(heardList)
                .dataList(dataList)
                .build();
    }

    /**
     * 开始导出数据信息
     */
    public byte[] exportExcel(HttpServletResponse response, String fileName, String title, String[] headKey, String[] headList, List<Map<String, String>> data, String sheetName) throws IOException {
        //检查参数配置信息
        checkConfig(headKey,headList,sheetName);
        //创建工作簿
        HSSFWorkbook wb = new HSSFWorkbook();
        //创建工作表
        HSSFSheet wbSheet = wb.createSheet(sheetName);
        //设置默认行宽
        wbSheet.setDefaultColumnWidth(20);

        // 标题样式（加粗，垂直居中）
        HSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        HSSFFont fontStyle = wb.createFont();
        fontStyle.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        fontStyle.setBold(true);   //加粗
        fontStyle.setFontHeightInPoints((short)16);  //设置标题字体大小
        cellStyle.setFont(fontStyle);

        //在第0行创建rows  (表标题)
//        HSSFRow title = wbSheet.createRow((int) 0);
//        title.setHeightInPoints(30);//行高
//        HSSFCell cellValue = title.createCell(0);
//        cellValue.setCellValue(this.title);
//        cellValue.setCellStyle(cellStyle);
//        wbSheet.addMergedRegion(new CellRangeAddress(0,0,0,(this.heardList.length-1)));
        //设置表头样式，表头居中
        HSSFCellStyle style = wb.createCellStyle();
        //设置单元格样式
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        //设置字体
        HSSFFont font = wb.createFont();
        font.setFontHeightInPoints((short) this.fontSize);
        style.setFont(font);
        //在第1行创建rows
        HSSFRow row = wbSheet.createRow((int) 0);
        //设置列头元素
        HSSFCell cellHead = null;
        for (int i = 0; i < headList.length; i++) {
            cellHead = row.createCell(i);
            cellHead.setCellValue(headList[i]);
            cellHead.setCellStyle(style);
        }

        //设置每格数据的样式 （字体红色）
        HSSFCellStyle cellParamStyle = wb.createCellStyle();
        HSSFFont ParamFontStyle = wb.createFont();
        cellParamStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellParamStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        ParamFontStyle.setColor(HSSFColor.DARK_RED.index);   //设置字体颜色 (红色)
        ParamFontStyle.setFontHeightInPoints((short) this.fontSize);
        cellParamStyle.setFont(ParamFontStyle);
        //设置每格数据的样式2（字体蓝色）
        HSSFCellStyle cellParamStyle2 = wb.createCellStyle();
        cellParamStyle2.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellParamStyle2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        HSSFFont ParamFontStyle2 = wb.createFont();
        ParamFontStyle2.setColor(HSSFColor.BLUE.index);   //设置字体颜色 (蓝色)
        ParamFontStyle2.setFontHeightInPoints((short) this.fontSize);
        cellParamStyle2.setFont(ParamFontStyle2);
        //开始写入实体数据信息
        int a = 1;
        for (int i = 0; i < data.size(); i++) {
            HSSFRow roww = wbSheet.createRow((int) a);
            Map<String, String> map = data.get(i);
            HSSFCell cell = null;
            for (int j = 0; j < headKey.length; j++) {
                cell = roww.createCell(j);
                cell.setCellStyle(style);
                Object valueObject = map.get(headKey[j]);
                String value = null;
                if (valueObject == null) {
                    valueObject = "";
                }
                if (valueObject instanceof String) {
                    //取出的数据是字符串直接赋值
                    value = (String) map.get(headKey[j]);
                } else if (valueObject instanceof Integer) {
                    //取出的数据是Integer
                    value = String.valueOf(((Integer) (valueObject)).floatValue());
                } else if (valueObject instanceof BigDecimal) {
                    //取出的数据是BigDecimal
                    value = String.valueOf(((BigDecimal) (valueObject)).floatValue());
                } else {
                    value = valueObject.toString();
                }
                //设置单个单元格的字体颜色
                cell.setCellStyle(style);
                cell.setCellValue(Strings.isNullOrEmpty(value) ? "" : value);
            }
            a++;
        }

        //导出数据
        try {
            //设置Http响应头告诉浏览器下载这个附件
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;Filename=" + URLEncoder.encode(fileName, "UTF-8")  + ".xls");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.close();
            return wb.getBytes();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new IOException("导出Excel出现异常，异常信息：" + ex.getMessage());
        }

    }

    /**
     * 检查数据配置问题
     *
     * @throws IOException 抛出数据异常类
     */
    protected void checkConfig(String[] headKey, String[] headList, String sheetName) throws IOException {
        if (headKey == null || headList.length == 0) {
            throw new IOException("列名数组不能为空或者为NULL");
        }

        if (fontSize < 0 || rowHeight < 0 || columWidth < 0) {
            throw new IOException("字体、宽度或者高度不能为负值");
        }

        if (Strings.isNullOrEmpty(sheetName)) {
            throw new IOException("工作表表名不能为NULL");
        }
    }

    /**
     * 助力导出
     * @param content
     * @param paTemplateId
     * @param feedbackInfo
     * @throws IOException
     */
    public ExcelExportActPartakeLogResponseDTO exportAssistActInfo(List<ActivityPartakeLogResponseDTO> content, Long paTemplateId,List<EnrollmentInfoVO> feedbackInfo) throws IOException {
        List<Map<String, String>> dataList = new ArrayList<>();

        String title = StrategyGroupEnum.getById(String.valueOf(paTemplateId)).getValue();
        String fileName = title + "-参与记录";

        String[] heardList1 = new String[]{"序号","昵称","手机号","所需助力人数","当前已助力人数","剩余助力时长","助力状态","发起时间","获得礼品"};
        String[] headerKey1 = new String[]{"serialNum","nickName","phone","needFissonCount","currentFissonCount","fissonEndTime","fissonStatus","issuanceTime","resourceName"};

        //重新生成headList headerkye
        Map<String, String[]> heardMap = getHeardMap(heardList1, headerKey1, feedbackInfo);
        String[] heardList = heardMap.get("heardList");
        String[] headerKey = heardMap.get("headerKey");

        //生成表格数据
        for (int i = 0; i < content.size(); i++) {
            Map<String, String> data = Maps.newHashMap();
            ActivityPartakeLogResponseDTO dto = content.get(i);
            data.put(headerKey[0], dto.getSerialNum() + "");
            data.put(headerKey[1], dto.getNickName());
            data.put(headerKey[2], PhoneEncryUtils.encode(dto.getPhone()));
            data.put(headerKey[3], dto.getNeedFissonCount() == null ? "" : String.valueOf(dto.getNeedFissonCount()));
            data.put(headerKey[4], dto.getCurrentFissonCount() == null ? "" : String.valueOf(dto.getCurrentFissonCount()));
            String fissonEndTime = DateTimeUtils.calculateIntervalTime(DateTime.now(), dto.getFissonEndTime());
            data.put(headerKey[5], StringUtil.isNotEmpty(fissonEndTime) ? fissonEndTime : "0小时0分0秒");
            data.put(headerKey[6], AssistStatusEnum.getValueById(dto.getFissonStatus()));
            data.put(headerKey[7], DateUtils.format(dto.getCreatedTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
            data.put(headerKey[8], StringUtils.isNotEmpty(dto.getResourceNames())?dto.getResourceNames():"-");
            //登记信息
            if(Objects.nonNull(dto.getFeedback())){
                FeedbackInfoVO feedbackInfoVO = (FeedbackInfoVO)MapBeanUtil.map2Object(dto.getFeedback().getLimits(), FeedbackInfoVO.class);
                feedbackInfoVO.setCardText(JSON.toJSONString(dto.getFeedback().getLimits().get("cardText")));
                Map<String, Object> limits = dto.getFeedback().getLimits();
                JSONArray jsonArray = (JSONArray)limits.get("feedbackInfo");
                for (int k = 0; k < jsonArray.size(); k++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(k);
                    String inputValue = jsonObject.getString("inputValue");
                    /*if (jsonObject.getInteger("type").equals(1)){
                        inputValue = StringUtil.isNotEmpty(inputValue)?PhoneEncryUtils.encode(inputValue):"";
                    }*/
                    data.put(headerKey[9+k],inputValue);
                }
            }
            dataList.add(data);
        }

        return ExcelExportActPartakeLogResponseDTO
                .builder()
                .title(title)
                .fileName(fileName)
                .headerKey(headerKey)
                .heardList(heardList)
                .dataList(dataList)
                .build();
    }

    /**
     * 昵称、手机号、资源名称、礼品原价、当前砍至、剩余砍价时长、砍价状态、发起时间以及登记明细，
     * 用户的登记明细中登记栏目为单元格标题，登记详情为单元格具体信息，以此类推在原导出字段最后进行逐列增加
     * @param content
     * @param paTemplateId
     * @param feedbackInfo
     * @throws IOException
     */
    public ExcelExportActPartakeLogResponseDTO exportReducePriceActInfo(List<ActivityPartakeLogResponseDTO> content, Long paTemplateId, List<EnrollmentInfoVO> feedbackInfo) throws IOException {
        List<Map<String, String>> dataList = new ArrayList<>();

        String title = StrategyGroupEnum.getById(String.valueOf(paTemplateId)).getValue();
        String fileName = title + "-参与记录";

        String[] heardList1 = new String[]{"编号","昵称","手机号","资源名称","礼品原价","当前砍至","剩余砍价时长","砍价状态","发起时间"};
        String[] headerKey1 = new String[]{"serialNum","nickName","phone","resourceName","costPrice","currentFissionPrice","fissionEndTime","fissionStatus","issuanceTime"};

        //重新生成headList headerkye
        Map<String, String[]> heardMap = getHeardMap(heardList1, headerKey1, feedbackInfo);
        String[] heardList = heardMap.get("heardList");
        String[] headerKey = heardMap.get("headerKey");

        //生成表格数据
        for (int i = 0; i < content.size(); i++) {
            Map<String, String> data = Maps.newHashMap();
            ActivityPartakeLogResponseDTO dto = content.get(i);
            data.put(headerKey[0], dto.getSerialNum() + "");
            data.put(headerKey[1], dto.getNickName());
            data.put(headerKey[2], PhoneEncryUtils.encode(dto.getPhone()));
            data.put(headerKey[3], dto.getResourceHisDetailResponseDTO().getName());
            data.put(headerKey[4], String.valueOf(dto.getResourceHisDetailResponseDTO().getPurchasePrice()));
            data.put(headerKey[5], dto.getCurrentFissonPrice() == null ? "" : String.valueOf(dto.getResourceHisDetailResponseDTO().getPurchasePrice().subtract(dto.getCurrentFissonPrice())));
            String fissionEndTime = DateTimeUtils.calculateIntervalTime(DateTime.now(), dto.getFissonEndTime());
            data.put(headerKey[6], StringUtil.isNotEmpty(fissionEndTime) ? fissionEndTime : "0小时0分0秒");
            data.put(headerKey[7], this.bargainStatusConversion(dto.getFissonStatus()));
            data.put(headerKey[8], DateUtils.format(dto.getCreatedTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));

            //登记信息
            if(Objects.nonNull(dto.getFeedback())){
                Map<String, Object> limits = dto.getFeedback().getLimits();
                JSONArray jsonArray = (JSONArray)limits.get("feedbackInfo");
                for (int k = 0; k < jsonArray.size(); k++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(k);
                    String inputValue = jsonObject.getString("inputValue");
                    /*if (jsonObject.getInteger("type").equals(1)){
                        inputValue = StringUtil.isNotEmpty(inputValue)?PhoneEncryUtils.encode(inputValue):"";
                    }*/
                    data.put(headerKey[9+k],inputValue);
                }
            }
            dataList.add(data);
        }

        return ExcelExportActPartakeLogResponseDTO
                .builder()
                .title(title)
                .fileName(fileName)
                .headerKey(headerKey)
                .heardList(heardList)
                .dataList(dataList)
                .build();
    }

    /**
     * 昵称、手机号、资源名称、礼品原价、当前砍至、剩余砍价时长、砍价状态、发起时间以及登记明细，
     * 用户的登记明细中登记栏目为单元格标题，登记详情为单元格具体信息，以此类推在原导出字段最后进行逐列增加
     * @param content
     * @param paTemplateId
     * @param feedbackInfo
     * @throws IOException
     */
    public ExcelExportActPartakeLogResponseDTO exportcardCollectingActInfo(List<ActivityPartakeLogResponseDTO> content, Long paTemplateId, List<EnrollmentInfoVO> feedbackInfo) throws IOException {
        List<Map<String, String>> dataList = new ArrayList<>();

        String title = StrategyGroupEnum.getById(String.valueOf(paTemplateId)).getValue();
        String fileName = title + "-参与记录";

        String[] heardList1 = new String[]{"昵称","手机号","邀请好友获得卡片数","获得礼品"};
        String[] headerKey1 = new String[]{"nickName","phone","currentFissonCount","resourceName"};

        //重新生成headList headerkye
        Map<String, String[]> heardMap = getHeardMap(heardList1, headerKey1, feedbackInfo);
        String[] heardList = heardMap.get("heardList");
        String[] headerKey = heardMap.get("headerKey");

        //生成表格数据
        for (int i = 0; i < content.size(); i++) {
            Map<String, String> data = Maps.newHashMap();
            ActivityPartakeLogResponseDTO dto = content.get(i);
            data.put(headerKey[0], dto.getNickName());
            data.put(headerKey[1], PhoneEncryUtils.encode(dto.getPhone()));
            data.put(headerKey[2], dto.getCurrentFissonCount() == null ? "" : String.valueOf(dto.getCurrentFissonCount()));
            data.put(headerKey[3], StringUtils.isNotEmpty(dto.getResourceNames())?dto.getResourceNames():"-");
            //登记信息
            if(Objects.nonNull(dto.getFeedback())){
                FeedbackInfoVO feedbackInfoVO = (FeedbackInfoVO)MapBeanUtil.map2Object(dto.getFeedback().getLimits(), FeedbackInfoVO.class);
                feedbackInfoVO.setCardText(JSON.toJSONString(dto.getFeedback().getLimits().get("cardText")));
                Map<String, Object> limits = dto.getFeedback().getLimits();
                JSONArray jsonArray = (JSONArray)limits.get("feedbackInfo");
                for (int k = 0; k < jsonArray.size(); k++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(k);
                    String inputValue = jsonObject.getString("inputValue");
                    data.put(headerKey[4+k],inputValue);
                }
            }
            dataList.add(data);
        }

        return ExcelExportActPartakeLogResponseDTO
                .builder()
                .title(title)
                .fileName(fileName)
                .headerKey(headerKey)
                .heardList(heardList)
                .dataList(dataList)
                .build();
    }

    /**
     * 砍价状态转换
     * @param fissionStatus
     * @return
     */
    private String bargainStatusConversion(Integer fissionStatus) {
        String bargainStatus = "";
        if (fissionStatus == null){
            bargainStatus = "";
        }else if (fissionStatus.equals(FissonStatusEnum.PROCESSING.getId())){
            bargainStatus = "砍价中";
        }else if (fissionStatus.equals(FissonStatusEnum.SUCCESS.getId())){
            bargainStatus = "砍价成功";
        }else if (fissionStatus.equals(FissonStatusEnum.FAILURE.getId())){
            bargainStatus = "砍价失败";
        }
        return bargainStatus;
    }

    public ExcelExportActPartakeLogResponseDTO exportSignActInfo(List<ActivityPartakeLogResponseDTO> content, Long paTemplateId, List<EnrollmentInfoVO> feedbackInfo) {
        List<Map<String, String>> dataList = new ArrayList<>();

        String title = StrategyGroupEnum.getById(String.valueOf(paTemplateId)).getValue();
        String fileName = title + "-参与记录";

        String[] heardList1 = new String[]{"编号","昵称","手机号","总签到天数","获得礼品"};
        String[] headerKey1 = new String[]{"serialNum","nickName","phone","currentFissonCount","resourceNames"};

        //重新生成headList headerkye
        Map<String, String[]> heardMap = getHeardMap(heardList1, headerKey1, feedbackInfo);
        String[] heardList = heardMap.get("heardList");
        String[] headerKey = heardMap.get("headerKey");

        //生成表格数据
        for (int i = 0; i < content.size(); i++) {
            Map<String, String> data = Maps.newHashMap();
            int dataIdx = 0;
            ActivityPartakeLogResponseDTO dto = content.get(i);
            data.put(headerKey[dataIdx++], dto.getSerialNum() + "");
            data.put(headerKey[dataIdx++], dto.getNickName());
            data.put(headerKey[dataIdx++], PhoneEncryUtils.encode(dto.getPhone()));
            data.put(headerKey[dataIdx++], dto.getCurrentFissonCount() == null ? "" : String.valueOf(dto.getCurrentFissonCount()));
            data.put(headerKey[dataIdx++], StringUtils.isNotEmpty(dto.getResourceNames())?dto.getResourceNames():"-");
            //登记信息
            if(Objects.nonNull(dto.getFeedback())){
                FeedbackInfoVO feedbackInfoVO = (FeedbackInfoVO)MapBeanUtil.map2Object(dto.getFeedback().getLimits(), FeedbackInfoVO.class);
                feedbackInfoVO.setCardText(JSON.toJSONString(dto.getFeedback().getLimits().get("cardText")));
                Map<String, Object> limits = dto.getFeedback().getLimits();
                JSONArray jsonArray = (JSONArray)limits.get("feedbackInfo");
                for (int k = 0; k < jsonArray.size(); k++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(k);
                    String inputValue = jsonObject.getString("inputValue");
                    data.put(headerKey[dataIdx++],inputValue);
                }
            }
            dataList.add(data);
        }

        return ExcelExportActPartakeLogResponseDTO
                .builder()
                .title(title)
                .fileName(fileName)
                .headerKey(headerKey)
                .heardList(heardList)
                .dataList(dataList)
                .build();
    }

    public ExcelExportActPartakeLogResponseDTO exportLocationSignActInfo(List<ActivityPartakeLogResponseDTO> content, Long paTemplateId, List<EnrollmentInfoVO> feedbackInfo, List<ActivityFissionLogDO> fissionLogDOS) {
        List<Map<String, String>> dataList = new ArrayList<>();

        String title = StrategyGroupEnum.getById(String.valueOf(paTemplateId)).getValue();
        String fileName = title + "-参与记录";

        String[] heardList1 = new String[]{"昵称","手机号","打卡成功地点数","打卡明细"};
        String[] headerKey1 = new String[]{"nickName","phone","currentFissonCount","locations"};

        //重新生成headList headerkye
        Map<String, String[]> heardMap = getHeardMap(heardList1, headerKey1, feedbackInfo);
        String[] heardList = heardMap.get("heardList");
        String[] headerKey = heardMap.get("headerKey");

        //用户分组后地点拼接（地点 时间），一行一个地点
        Map<Long, String> userLocationsMap = fissionLogDOS.stream().collect(Collectors.groupingBy(ActivityFissionLogDO::getPartakeLogId, Collectors.mapping(activityFissionLogDO -> activityFissionLogDO.getAvatar() + " " + DateUtil.formatDateTime(activityFissionLogDO.getCreatedTime()), Collectors.joining("\t\n"))));
        //生成表格数据
        for (int i = 0; i < content.size(); i++) {
            Map<String, String> data = Maps.newHashMap();
            int dataIdx = 0;
            ActivityPartakeLogResponseDTO dto = content.get(i);
            data.put(headerKey[dataIdx++], dto.getNickName());
            data.put(headerKey[dataIdx++], PhoneEncryUtils.encode(dto.getPhone()));
            data.put(headerKey[dataIdx++], dto.getCurrentFissonCount() == null ? "" : String.valueOf(dto.getCurrentFissonCount()));
            data.put(headerKey[dataIdx++], userLocationsMap.getOrDefault(dto.getId(),"-"));
            //登记信息
            if(Objects.nonNull(dto.getFeedback())){
                FeedbackInfoVO feedbackInfoVO = (FeedbackInfoVO)MapBeanUtil.map2Object(dto.getFeedback().getLimits(), FeedbackInfoVO.class);
                feedbackInfoVO.setCardText(JSON.toJSONString(dto.getFeedback().getLimits().get("cardText")));
                Map<String, Object> limits = dto.getFeedback().getLimits();
                JSONArray jsonArray = (JSONArray)limits.get("feedbackInfo");
                for (int k = 0; k < jsonArray.size(); k++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(k);
                    String inputValue = jsonObject.getString("inputValue");
                    data.put(headerKey[dataIdx++],inputValue);
                }
            }
            dataList.add(data);
        }

        return ExcelExportActPartakeLogResponseDTO
                .builder()
                .title(title)
                .fileName(fileName)
                .headerKey(headerKey)
                .heardList(heardList)
                .dataList(dataList)
                .build();
    }
}
