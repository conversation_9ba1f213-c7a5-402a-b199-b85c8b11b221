package com.deepexi.dxp.marketing.utils;

import com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyInResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ExcelExportActVerifyResponseDTO;
import com.deepexi.dxp.marketing.enums.specify.RefundsStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.VerifyRefundStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.VerifyStatusEnum;
import com.deepexi.util.DateUtils;
import com.google.common.collect.Maps;
import lombok.Data;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description excel导出
 * @date 2020/4/26
 */
@Data
public class GetExcelExportActVerifyUtil {

    private static GetExcelExportActVerifyUtil instance = null;

    public static GetExcelExportActVerifyUtil getInstance(){
        if (null == instance) {
            synchronized (GetExcelExportActVerifyUtil.class) {
                if (null == instance) {
                    instance = new GetExcelExportActVerifyUtil();
                }
            }
        }
        return instance;
    }

    //字体大小
    private int fontSize = 14;
    //行高
    private int rowHeight = 30;
    //列宽
    private int columWidth = 200;

    /**
     * 导出核销记录
     */
    public ExcelExportActVerifyResponseDTO exportVerifyInfo(List<ActivityVerifyInResponseDTO> content) throws IOException {

        List<Map<String, String>> dataList = new ArrayList<>();

        String fileName = "活动核销记录";

        String[] heardList1 = new String[]{"序号","昵称","手机号","活动名称","资源名称","是否付费","有效期","领取/支付时间","核销码","核销状态","退款状态","核销人","核销项目","核销时间","退款人","退款时间","微信订单号","支付金额"};
        String[] headerKey1 = new String[]{"","nickName","phone","activityName","name","isPay","vaildTime","payTime","code","verifyStatus","refundStatus","verifyBy","projectName","verifyTime","refundBy","refundTime","wxOrderNo","payMoney"};

        //重新生成headList headerkye
        Map<String, String[]> heardMap = getHeardMap(heardList1, headerKey1);
        String[] heardList = heardMap.get("heardList");
        String[] headerKey = heardMap.get("headerKey");

        for (int i = 0; i < content.size(); i++) {
            Map<String, String> data = Maps.newHashMap();
            ActivityVerifyInResponseDTO dto = content.get(i);
            data.put(headerKey[0], (i+1) + "");
            data.put(headerKey[1], dto.getNickName());
            data.put(headerKey[2], dto.getPhone());
            data.put(headerKey[3], dto.getActivityName());
//            data.put(headerKey[4], DateUtils.format(dto.getCreatedTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
            data.put(headerKey[4], dto.getName());
            data.put(headerKey[5], dto.getIsPay() == 1? "是" : "否");

            if(dto.getValidTimeType().equals(0)){
                data.put(headerKey[6], "不限制");
            }else if(dto.getValidTimeType().equals(1)){
                data.put(headerKey[6], DateUtils.format(dto.getValidStartTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT)+"-"+DateUtils.format(dto.getValidEndTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
            }else if(dto.getValidTimeType().equals(2)){
                data.put(headerKey[6], "自获得"+dto.getValidDay()+"天有效");
            }
            data.put(headerKey[7], DateUtils.format(dto.getPayTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
            data.put(headerKey[8], dto.getCode());

            String verifyStatus = "";
            switch (dto.getVerifyStatus()){
                case 0:
                    verifyStatus = VerifyStatusEnum.NO_VERIFY.getValue();
                    break;
                case 1:
                    verifyStatus = VerifyStatusEnum.VERIFY_ED.getValue();
                    break;
                case 2:
                    verifyStatus = VerifyStatusEnum.EXPIRED.getValue();
                    break;
                case 3:
                    verifyStatus = VerifyStatusEnum.INVALID.getValue();
                    break;
            }
            data.put(headerKey[9], verifyStatus);

            String refundStatus = "";
            switch (dto.getRefundStatus()){
                case 0:
                    refundStatus = VerifyRefundStatusEnum.NO_REFUND.getValue();
                    break;
                case 1:
                    refundStatus = VerifyRefundStatusEnum.REFUNDED.getValue();
                    break;
            }

            data.put(headerKey[10], refundStatus);
            data.put(headerKey[11], dto.getVerifyBy());
            data.put(headerKey[12], dto.getProjectName());
            data.put(headerKey[13], dto.getVerifyTime()==null?"":DateUtils.format(dto.getVerifyTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
            data.put(headerKey[14], dto.getRefundBy());
            data.put(headerKey[15], dto.getRefundTime()==null?"":DateUtils.format(dto.getRefundTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
            data.put(headerKey[16], dto.getWxOrderNo());
            data.put(headerKey[17], dto.getPayMoney()==null?"" : dto.getPayMoney()+"");

            dataList.add(data);
        }
        return ExcelExportActVerifyResponseDTO
                .builder()
                .title(fileName)
                .fileName(fileName)
                .headerKey(headerKey)
                .heardList(heardList)
                .dataList(dataList)
                .sheetName("核销记录")
                .build();
    }

    private Map<String,String[]> getHeardMap(String[] heardList1,String[] headerKey1){
        Map<String,String[]> map = Maps.newHashMap();
        String[] heardList = new String[heardList1.length];
        String[] headerKey = new String[headerKey1.length];
        System.arraycopy(heardList1,0,heardList,0,heardList1.length);
        System.arraycopy(headerKey1,0,headerKey,0,headerKey1.length);

        map.put("heardList",heardList);
        map.put("headerKey",headerKey);
        return map;
    }

}
