package com.deepexi.dxp.marketing.service.specify.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cnhuafas.common.service.GatewayService;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.constant.NewMarketingConstant;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.constant.TradeConstants;
import com.deepexi.dxp.marketing.converter.ActivityAnalysisConverter;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityAnalysisQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityTrendsQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.EventsReportQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiQuery;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.specify.ActivityAnalysisDateTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.MarketingKpiTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.OverviewOfIndicatorsEnum;
import com.deepexi.dxp.marketing.enums.specify.SensorsEventEnum;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.service.specify.*;
import com.deepexi.dxp.marketing.utils.ComputeUtil;
import com.deepexi.dxp.marketing.utils.DateTimeUtils;
import com.deepexi.dxp.marketing.utils.PhoneEncryUtils;
import com.deepexi.dxp.marketing.utils.PinyinUtils;
import com.deepexi.dxp.middle.marketing.domain.dto.*;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityPartakeLogDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityParticipationDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityParticipationDO;
import com.deepexi.dxp.middle.promotion.util.PageUtil;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityAnalysisServiceImpl implements ActivityAnalysisService {

    @Autowired
    private PromotionActivityDAO promotionActivityDAO;

    @Autowired
    private MarketingKpiRouteMapService marketingKpiRouteMapService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private MarketingKpiService marketingKpiService;

    @Resource
    private SensorsService sensorsService;

    @Autowired
    private PromotionActivityService promotionActivityService;

    @Resource
    private GatewayService gatewayService;

    @Autowired
    private ActivityParticipationDAO activityParticipationDAO;

    @Autowired
    private ActivityPartakeLogDAO activityPartakeLogDAO;

    @Override
    public PageBean<ActivityAnalysisResponseDTO> analysisList(ActivityAnalysisQuery query) throws Exception {
        List<Long> activityIds = promotionActivityService.getActivityIdsByUserId(HuafaRuntimeEnv.getUserId());
        if(CollectionUtil.isEmpty(activityIds)){
            return new PageBean<ActivityAnalysisResponseDTO>();
        }
        query.setActivityIdList(activityIds);
        PageBean<ActivityAnalysisResponseDTO> activityAnalysisResponsePageBean = promotionActivityDAO.analysisList(query);
        List<ActivityAnalysisResponseDTO> content = activityAnalysisResponsePageBean.getContent();

        //获取神策数据
        this.getSensorsInfo(content);
        //获取到访人数和成交人数
        //this.getAccessAndDealInfo(content);

        return activityAnalysisResponsePageBean;
    }

    private void getSensorsInfoByActivity(ActivityAnalysisResponseDTO analysis) {
            String activityId = "'"+analysis.getId()+"'";

            List<ActivityAnalysisResponseDTO> sensorsActivityAnalysis = sensorsService.sensorsActivityAnalysisStatisticsJoinNumber(activityId);
            Map<Long, ActivityAnalysisResponseDTO> analysisMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(sensorsActivityAnalysis)){
                analysisMap = sensorsActivityAnalysis.stream().collect(Collectors.toMap(ActivityAnalysisResponseDTO::getId, v -> v));
            }

            Map<Long, ActivityAnalysisResponseDTO> finalAnalysisMap = analysisMap;
                ActivityAnalysisResponseDTO activityAnalysisResponse = finalAnalysisMap.get(analysis.getId());
                if(activityAnalysisResponse != null){
                    analysis.setJoinPeopleNumber(activityAnalysisResponse.getJoinPeopleNumber());
                }
    }

    private void getSensorsInfo(List<ActivityAnalysisResponseDTO> content) {
        if (CollectionUtil.isNotEmpty(content)){
            String activityId = content.stream().map(e->("'"+e.getId()+"'")).collect(Collectors.joining(TradeConstants.DOT));

            List<ActivityAnalysisResponseDTO> sensorsActivityAnalysis = sensorsService.sensorsActivityAnalysisStatistics(activityId);
            Map<Long, ActivityAnalysisResponseDTO> analysisMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(sensorsActivityAnalysis)){
                analysisMap = sensorsActivityAnalysis.stream().collect(Collectors.toMap(ActivityAnalysisResponseDTO::getId, v -> v));
            }

            Map<Long, ActivityAnalysisResponseDTO> finalAnalysisMap = analysisMap;
            content.forEach(analysis->{
                Long accessPeopleNumber = 0L;
                Long accessNumber = 0L;
                Long shareNumber =  0L;
                Long sharePeople = 0L;
                Long joinNumber = 0L;
                Long joinPeopleNumber = 0L;
                Long lzPeopleNumber = 0L;
                ActivityAnalysisResponseDTO activityAnalysisResponse = finalAnalysisMap.get(analysis.getId());
                if(activityAnalysisResponse != null){
                    accessPeopleNumber = activityAnalysisResponse.getAccessPeopleNumber();
                    accessNumber = activityAnalysisResponse.getAccessNumber();
                    sharePeople = activityAnalysisResponse.getSharePeopleNumber();
                    shareNumber = activityAnalysisResponse.getShareNumber();
                    joinNumber = activityAnalysisResponse.getJoinNumber();
                    joinPeopleNumber = activityAnalysisResponse.getJoinPeopleNumber();
                    lzPeopleNumber = activityAnalysisResponse.getLzPeopleNumber();
                }
                analysis.setAccessPeopleNumber(accessPeopleNumber);
                analysis.setShareNumber(shareNumber);
                analysis.setAccessNumber(accessNumber);
                analysis.setSharePeopleNumber(sharePeople);
                analysis.setJoinNumber(joinNumber);
                analysis.setJoinPeopleNumber(joinPeopleNumber);
                analysis.setLzPeopleNumber(lzPeopleNumber);

                //完成信息登记人数/活动参与人数（除表单外，其它活动模板均支持留资率统计）
                if (analysis.getLzPeopleNumber() != null && analysis.getJoinPeopleNumber()!= null && analysis.getJoinPeopleNumber() > 0) {
                    analysis.setLzRate(new BigDecimal(analysis.getLzPeopleNumber())
                            .divide(new BigDecimal(analysis.getJoinPeopleNumber()),4, BigDecimal.ROUND_HALF_UP)
                            .multiply(new BigDecimal(100)));
                }else{
                    analysis.setLzRate(BigDecimal.ZERO);
                }
                analysis.setTemplate(StrategyGroupEnum.getValueById(analysis.getTemplate()));
            });
        }
    }

    /**
     * 对接新营销系统，获取数据分析数据
     * @param content
     */
    private void getAccessAndDealInfo(List<ActivityAnalysisResponseDTO> content) throws Exception {

        for (ActivityAnalysisResponseDTO dto:content){
            PromotionActivityDO actDTO = promotionActivityDAO.selectById(dto.getId());
            JSONObject jo = new JSONObject();
            if(Objects.nonNull(actDTO.getStartTime())){
                jo.put("startTime", DateTimeUtils.getCurrentTime(actDTO.getStartTime()));
            }else{
                jo.put("startTime",DateUtils.getCurrentTime());
            }
            if(Objects.nonNull(actDTO.getEndTime())){
                jo.put("endTime",DateTimeUtils.getCurrentTime(actDTO.getEndTime()));
            }else{
                jo.put("endTime",DateUtils.getCurrentTime());
            }

            QueryWrapper<ActivityParticipationDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ActivityParticipationDO::getActivityId,dto.getId());
            List<ActivityParticipationDO> projectList = activityParticipationDAO.list(queryWrapper);//活动相关项目
            Set<String> projectIdSet =  projectList.stream().map(item->item.getProjectId()).collect(Collectors.toSet());
            jo.put("projectIdSet",projectIdSet);
            log.info("=====================获取新营销系统数据=====================");
            JSONObject accessSlotData = gatewayService.getMarketExtServiceClient().doPost(NewMarketingConstant.ACCESS_COUNT,JSON.toJSONString(jo),new TypeReference<JSONObject>(){});
            if(CommonExceptionCode.SUCCESS.equals(accessSlotData.getString("code"))){
                dto.setVisitPeopleNumber(accessSlotData.getInteger("data"));
            }
            JSONObject dealSlotData = gatewayService.getMarketExtServiceClient().doPost(NewMarketingConstant.DEAL_COUNT,JSON.toJSONString(jo),new TypeReference<JSONObject>(){});
            if(CommonExceptionCode.SUCCESS.equals(dealSlotData.getString("code"))){
                dto.setDealPeopleNumber(dealSlotData.getInteger("data"));
            }

            QueryWrapper<ActivityPartakeLogDO> query = new QueryWrapper();
            query.lambda().eq(ActivityPartakeLogDO::getActivityId,dto.getId());
            List<ActivityPartakeLogDO> partakeLogDOList = activityPartakeLogDAO.list(query);
            Set<String> telPhoneSet = partakeLogDOList.stream().filter(item->item.getPhone()!=null).map(item->item.getPhone()).collect(Collectors.toSet());
            if(telPhoneSet.size()<=0){
                dto.setGoVisitPeopleNumber(0);
                dto.setGoDealPeopleNumber(0);
                continue;
            }
            jo.put("telPhoneSet",telPhoneSet);
            JSONObject accessSlotConverData = gatewayService.getMarketExtServiceClient().doPost(NewMarketingConstant.ACCESS_COUNT,JSON.toJSONString(jo),new TypeReference<JSONObject>(){});
            if(CommonExceptionCode.SUCCESS.equals(accessSlotConverData.getString("code"))){
                dto.setGoVisitPeopleNumber(accessSlotConverData.getInteger("data"));
            }
            JSONObject dealSlotConverData = gatewayService.getMarketExtServiceClient().doPost(NewMarketingConstant.DEAL_COUNT,JSON.toJSONString(jo),new TypeReference<JSONObject>(){});
            if(CommonExceptionCode.SUCCESS.equals(dealSlotConverData.getString("code"))){
                dto.setGoDealPeopleNumber(dealSlotConverData.getInteger("data"));
            }

        }

    }

    /**
     * 今日新增 统计今日新增的用户，如果昨天已有记录今日不在累加
     * @param query
     * @return
     */
    @Override
    public ActivityAnalysisDetailResponseDTO analysisDetail(ActivityAnalysisQuery query) {
        if (query.getId() == null){
            throw new ApplicationException("活动id不能为空");
        }
        ActivityAnalysisDetailResponseDTO detail = new ActivityAnalysisDetailResponseDTO();
        PromotionActivityDO activityDO = promotionActivityDAO.selectById(query.getId());
        if (activityDO == null){
            throw new ApplicationException("活动不存在");
        }
        detail.setActivityName(activityDO.getName());
        detail.setId(activityDO.getId());
        if (activityDO.getStartTime() != null) {
            detail.setStartTime(DateUtils.format(activityDO.getStartTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
            query.setStartTime(detail.getStartTime());
        }
        if (activityDO.getEndTime() != null) {
            detail.setEndTime(DateUtils.format(activityDO.getEndTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
            query.setEndTime(detail.getEndTime());
        }
        detail.setTemplate(StrategyGroupEnum.getValueById(activityDO.getPaTemplateId().toString()));

        //指标相关查询
        CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
            //用于记录指标结果 指标路径可能会需要获取相关信息
            Map<String,IndexPathResponseDTO> indexPathMap = Maps.newHashMap();
            //指标列表
            List<MarketingKpiDTO> marketingKpiList = this.getMarketingKpiMap();
            //指标概览
            detail.setOverviewOfIndicators(this.findOverviewOfIndicators(query,indexPathMap,marketingKpiList,activityDO));
            //指标路径
            detail.setIndexPathResponseDTO(this.findIndexPathResponse(query,indexPathMap,marketingKpiList));
        });

        //渠道分布
        CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
            this.findChannelDistribution(detail, query.getId());
        });
        //城市分布
        CompletableFuture<Void> future3 = CompletableFuture.runAsync(() -> {
            this.findCityDistribution(detail, query.getId());
        });

        //活动趋势
        this.findActivityTrend(detail,query.getId());
        //成交和到访的指标
        this.getAccessAndDealInfoByActivity(detail,activityDO);

        try {
            CompletableFuture.allOf(future1,future2,future3).get();
        } catch (Exception e) {
            log.error("获取活动相关数据异常：",e);
            throw new ApplicationException("获取活动相关数据异常：" + e.getMessage());
        }

        return detail;
    }

    /**
     * 获取活动期间成交和到访数据
     * @param query
     * @return
     */
    @Override
    public ActivityAnalysisDetailResponseDTO getAccessAndDealInfoByActivityId(ActivityAnalysisQuery query) {
        if (query.getId() == null){
            throw new ApplicationException("活动id不能为空");
        }
        ActivityAnalysisDetailResponseDTO detail = new ActivityAnalysisDetailResponseDTO();
        PromotionActivityDO activityDO = promotionActivityDAO.selectById(query.getId());
        if (activityDO == null){
            throw new ApplicationException("活动不存在");
        }
        detail.setActivityName(activityDO.getName());
        detail.setId(activityDO.getId());
        // 设置时间区间
        if(Objects.nonNull(query.getStartTime())){
            detail.setStartTime(query.getStartTime());
            activityDO.setStartTime(DateUtils.parse(query.getStartTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }else if (activityDO.getStartTime() != null) {
            detail.setStartTime(DateUtils.format(activityDO.getStartTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }

        if(Objects.nonNull(query.getEndTime())){
            detail.setEndTime(query.getEndTime());
            activityDO.setEndTime(DateUtils.parse(query.getEndTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }else if (activityDO.getEndTime() != null) {
            detail.setEndTime(DateUtils.format(activityDO.getEndTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }

        //成交和到访的指标
        this.getAccessAndDealInfoByActivity(detail,activityDO);

        return detail;
    }

    private void getAccessAndDealInfoByActivity(ActivityAnalysisDetailResponseDTO dto,PromotionActivityDO actDTO) {
        try{
            JSONObject jo = new JSONObject();
            if(Objects.nonNull(actDTO.getStartTime())){
                jo.put("startTime", DateTimeUtils.getCurrentTime(actDTO.getStartTime()));
            }else{
                jo.put("startTime",DateUtils.getCurrentTime());
            }
            if(Objects.nonNull(actDTO.getEndTime())){
                jo.put("endTime",DateTimeUtils.getCurrentTime(actDTO.getEndTime()));
            }else{
                jo.put("endTime",DateUtils.getCurrentTime());
            }

            QueryWrapper<ActivityParticipationDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().select(ActivityParticipationDO::getProjectId).isNotNull(ActivityParticipationDO::getProjectId).eq(ActivityParticipationDO::getActivityId,dto.getId());
            List<ActivityParticipationDO> projectList = activityParticipationDAO.list(queryWrapper);//活动相关项目
            Set<String> projectIdSet =  projectList.stream().map(item->item.getProjectId()).collect(Collectors.toSet());
            jo.put("projectIdSet",projectIdSet);
            log.info("=====================获取新营销系统数据=====================");
            JSONObject accessSlotData = gatewayService.getMarketExtServiceClient().doPost(NewMarketingConstant.ACCESS_COUNT, JSON.toJSONString(jo), new TypeReference<JSONObject>() {});
            if(CommonExceptionCode.SUCCESS.equals(accessSlotData.getString("code"))){
                dto.setVisitPeopleNumber(accessSlotData.getInteger("data"));
            }
            JSONObject dealSlotData = gatewayService.getMarketExtServiceClient().doPost(NewMarketingConstant.DEAL_COUNT, JSON.toJSONString(jo), new TypeReference<JSONObject>() {});
            if(CommonExceptionCode.SUCCESS.equals(dealSlotData.getString("code"))){
                dto.setDealPeopleNumber(dealSlotData.getInteger("data"));
            }

            QueryWrapper<ActivityPartakeLogDO> query = new QueryWrapper();
            query.lambda().select(ActivityPartakeLogDO::getPhone).eq(ActivityPartakeLogDO::getActivityId,dto.getId()).isNotNull(ActivityPartakeLogDO::getPhone);
            List<ActivityPartakeLogDO> partakeLogDOList = activityPartakeLogDAO.list(query);
            Set<String> telPhoneSet = partakeLogDOList.stream().map(item->item.getPhone()).collect(Collectors.toSet());
            dto.setGoVisitPeopleNumber(0);
            dto.setGoDealPeopleNumber(0);
            if(telPhoneSet.size() == 0){
                return;
            }
            int batchSize = 1000; // 分批处理，每次处理1000个电话号码
            List<String> telPhoneList = new ArrayList<>(telPhoneSet);
            for (int i = 0; i < telPhoneList.size(); i += batchSize) {
                List<String> batchTelPhones = telPhoneList.subList(i, Math.min(i + batchSize, telPhoneList.size()));
                jo.put("telPhoneSet", batchTelPhones);
                JSONObject accessSlotConverData = gatewayService.getMarketExtServiceClient().doPost(NewMarketingConstant.ACCESS_COUNT, JSON.toJSONString(jo), new TypeReference<JSONObject>() {});
                if (CommonExceptionCode.SUCCESS.equals(accessSlotConverData.getString("code"))) {
                    dto.setGoVisitPeopleNumber(dto.getGoVisitPeopleNumber() + accessSlotConverData.getInteger("data"));
                }
                JSONObject dealSlotConverData = gatewayService.getMarketExtServiceClient().doPost(NewMarketingConstant.DEAL_COUNT, JSON.toJSONString(jo), new TypeReference<JSONObject>() {});
                if (CommonExceptionCode.SUCCESS.equals(dealSlotConverData.getString("code"))) {
                    dto.setGoDealPeopleNumber(dto.getGoDealPeopleNumber() + dealSlotConverData.getInteger("data"));
                }
            }
        }catch(Exception e){
            log.error("成交和到访的指标获取异常",e);
            throw new ApplicationException("成交和到访的指标获取异常");
        }
    }

    @Override
    public Long getActPartTotalNumber(Long activityId) {
        if (activityId == null){
            throw new ApplicationException("活动id不能为空");
        }
        ActivityAnalysisResponseDTO activityAnalysisResponseDTO = new ActivityAnalysisResponseDTO();
        activityAnalysisResponseDTO.setId(activityId);
        this.getSensorsInfoByActivity(activityAnalysisResponseDTO);
        return activityAnalysisResponseDTO.getJoinPeopleNumber();
    }

    private List<IndexPathResponseDTO> findIndexPathResponse(ActivityAnalysisQuery query,Map<String,IndexPathResponseDTO> indexPathMap,
                                                             List<MarketingKpiDTO> marketingKpiList
                                                             ) {
        PromotionActivityDO byId = promotionActivityDAO.getById(query.getId());
        Long kpiPath = Optional.ofNullable(byId.getExt()).map(e -> Long.parseLong(e.get("kpiPath").toString())).orElse(null);
        if (kpiPath != null){
            MarketingKpiRouteMapDTO marketingKpiRouteMap = marketingKpiRouteMapService.queryById(kpiPath);
            if (marketingKpiRouteMap != null){
                List<IndexPathResponseDTO> indexPathResponseList = Lists.newArrayList();
                List<MarketingKpiRouteMapNodeDTO> mapNodeList = marketingKpiRouteMap.getMapNodeList();

                Map<Long, MarketingKpiDTO> marketingKpiMapById = Maps.newHashMap();
                Map<String, MarketingKpiDTO> marketingKpiMapByCode = Maps.newHashMap();
                if (CollectionUtil.isNotEmpty(marketingKpiList)){
                    marketingKpiMapById = marketingKpiList.stream().collect(Collectors.toMap(MarketingKpiDTO::getId, v->v));
                    marketingKpiMapByCode = marketingKpiList.stream().collect(Collectors.toMap(MarketingKpiDTO::getCode, v->v));
                }
                Map<Long, MarketingKpiDTO> finalMarketingKpiMap = marketingKpiMapById;
                Map<String, MarketingKpiDTO> finalMarketingKpiMapByCode = marketingKpiMapByCode;
                Map<Long, MarketingKpiDTO> finalMarketingKpiMapById = marketingKpiMapById;
                mapNodeList.forEach(node->{
                    if (CollectionUtil.isNotEmpty(node.getNodeItemsList())){

                        //暂时需求要求 只允许有一个子节点所以get（0）
                        MarketingKpiRouteMapNodeItemsDTO marketingKpiRouteMapNodeItems = node.getNodeItemsList().get(0);
                        MarketingKpiDTO marketingKpiDTO = finalMarketingKpiMap.get(marketingKpiRouteMapNodeItems.getKpiItemsId());
                        if (marketingKpiDTO != null) {
                            marketingKpiRouteMapNodeItems.setKpiCode(marketingKpiDTO.getCode());
                            IndexPathResponseDTO indexPath = this.findIndexPath(query, marketingKpiRouteMapNodeItems.getKpiCode(),
                                    indexPathMap, finalMarketingKpiMapByCode, finalMarketingKpiMapById);
                            if (null != indexPath) {
                                indexPathResponseList.add(indexPath);
                            }
                        }
                    }
                });

                //计算转化率
                if (CollectionUtil.isNotEmpty(indexPathResponseList)) {
                    for (int i = 0; i < indexPathResponseList.size(); i++) {
                        IndexPathResponseDTO indexPathBefor = null;
                        if (i > 0) {
                            indexPathBefor = indexPathResponseList.get(i - 1);
                        }
                        IndexPathResponseDTO indexPathAfter = indexPathResponseList.get(i);

                        BigDecimal conversionRate = BigDecimal.ZERO;
                        if (indexPathBefor != null && indexPathBefor.getTotalNumber() > 0){
                            conversionRate = new BigDecimal(indexPathAfter.getTotalNumber())
                                    .divide(new BigDecimal(indexPathBefor.getTotalNumber()), 4, BigDecimal.ROUND_HALF_UP)
                                    .multiply(new BigDecimal(100));
                        }
                        indexPathAfter.setConversionRate(conversionRate);
                    }
                }
                return indexPathResponseList;
            }
        }


        return Lists.newArrayList();
    }

    /**
     * 活动指标查询详情
     *
     * @param query
     * @param kpiCode
     * @param indexPathMap
     * @return
     * @iterNum
     */
    private IndexPathResponseDTO findIndexPath(ActivityAnalysisQuery query, String kpiCode, Map<String,IndexPathResponseDTO> indexPathMap,
                                               Map<String, MarketingKpiDTO> marketingKpiMapByCode,
                                               Map<Long, MarketingKpiDTO> marketingKpiMapById) {
        IndexPathResponseDTO indexPathResponse;

        MarketingKpiDTO marketingKpiDTO = marketingKpiMapByCode.get(kpiCode);
        if (null == marketingKpiDTO){
            return null;
        }
        if (marketingKpiDTO.getType() == MarketingKpiTypeEnum.PRIMITIVE.getId()) {

            //---------------------原生指标-----------------------------
            indexPathResponse = this.primitiveindexPathResponse(query,kpiCode,indexPathMap,marketingKpiMapByCode);

        }else{
            //---------------------派生指标-----------------------------

            //指标公式
            List<MarketingKpiFormulaDTO> formulaList = marketingKpiDTO.getFormulaList();
            if (CollectionUtil.isEmpty(formulaList)){
                return null;
            }
            formulaList.get(formulaList.size() - 1).setSymbols("");
            //累计数据公式
            StringBuilder totalFormula = new StringBuilder();

            //今日数据公式
            StringBuilder todayNumberFormula = new StringBuilder();

            //昨日数据公式
            StringBuilder yesterdayNumberFormula = new StringBuilder();
            for (MarketingKpiFormulaDTO formula : formulaList) {
                String symbols = formula.getSymbols().replace("x", "*").replace("÷", "/");
                Long quoteKpiId = formula.getQuoteKpiId();
                MarketingKpiDTO marketingKpi = marketingKpiMapById.get(quoteKpiId);
                if(marketingKpi == null){
                    log.error("id为：" + quoteKpiId + " 的指标丢失");
                    continue;
                }
                IndexPathResponseDTO indexPath = primitiveindexPathResponse(query, marketingKpi.getCode(),
                        indexPathMap, marketingKpiMapByCode);

                if (indexPath == null){
                   break;
                }

                Long totalNumber = indexPath.getTotalNumber();
                Long todayNumber = indexPath.getTodayNumber()==null?0:indexPath.getTodayNumber();
                Long yesterdayNumber = indexPath.getYesterdayNumber()==null?0:indexPath.getYesterdayNumber();
                totalFormula.append(totalNumber).append(symbols);
                todayNumberFormula.append(todayNumber).append(symbols);
                yesterdayNumberFormula.append(yesterdayNumber).append(symbols);
            }

            //累计公式计算结果
            BigDecimal totalResult = ComputeUtil.formulaCalculate(totalFormula.toString());

            //今日公式计算结果
            BigDecimal todayResult = ComputeUtil.formulaCalculate(todayNumberFormula.toString());

            //昨日公式计算结果
            BigDecimal yesterdayResult = ComputeUtil.formulaCalculate(yesterdayNumberFormula.toString());

            //组装指标结果
            indexPathResponse = ActivityAnalysisConverter.deriveConverter(marketingKpiDTO,totalResult,todayResult,yesterdayResult);

        }

        return indexPathResponse;
    }



    private IndexPathResponseDTO actNumPathResponse(Long id,String kpiCode,Map<String,IndexPathResponseDTO> indexPathMap,
                                                            Map<String, MarketingKpiDTO> marketingKpiMapByCode) {
        IndexPathResponseDTO indexPathResponse;
        //参与用户数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.JOIN_PEOPLE_NUMBER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.JOIN_PEOPLE_NUMBER.getCode());
            if (indexPathResponse == null) {
                indexPathResponse = this.getActivityParticipationPeopleNumber(id,marketingKpiMapByCode);
                indexPathMap.put(OverviewOfIndicatorsEnum.JOIN_PEOPLE_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }
        //未定义指标
        indexPathResponse = new IndexPathResponseDTO();
        indexPathResponse.setBeforeTodayRatio(BigDecimal.ZERO);
        indexPathResponse.setTodayNumber(0L);
        indexPathResponse.setTotalNumber(0L);
        return indexPathResponse;
    }

    private IndexPathResponseDTO primitiveindexPathResponse(ActivityAnalysisQuery query, String kpiCode, Map<String,IndexPathResponseDTO> indexPathMap,
                                                            Map<String, MarketingKpiDTO> marketingKpiMapByCode) {
        IndexPathResponseDTO indexPathResponse;
        //优化获取逻辑，先一次性把相关的指标进行查询，然后设置到indexPathMap中
        preGetAllIndicator(query,indexPathMap,marketingKpiMapByCode);
        Long id = query.getId();

        //浏览用户数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.WECHAT_APPLET.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.WECHAT_APPLET.getCode());
            if (indexPathResponse == null) {
                indexPathResponse = this.getPageViewVisitorsList(id, marketingKpiMapByCode);
                indexPathMap.put(OverviewOfIndicatorsEnum.WECHAT_APPLET.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }


        //浏览次数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.WECHAT_WEBSITE.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.WECHAT_WEBSITE.getCode());
            if (indexPathResponse == null) {
                indexPathResponse = this.getPageViewVisitorsFrequencyList(id, marketingKpiMapByCode);
                indexPathMap.put(OverviewOfIndicatorsEnum.WECHAT_WEBSITE.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //活动分享次数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.SHARE_NUMBER.getCode())){
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.SHARE_NUMBER.getCode());
            if (indexPathResponse == null) {
                //活动分享次数
                indexPathResponse = this.getShareNumberList(id, marketingKpiMapByCode);
                indexPathMap.put(OverviewOfIndicatorsEnum.SHARE_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //活动分享人数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.SHARE_PEOPLE_NUMBER.getCode())){
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.SHARE_PEOPLE_NUMBER.getCode());
            if (indexPathResponse == null) {
                indexPathResponse = this.getTodayYesterdayPeopleNumberList(id, marketingKpiMapByCode,
                        SensorsEventEnum.PAGE_SHARE.getCode(), OverviewOfIndicatorsEnum.SHARE_PEOPLE_NUMBER, null, null);
                indexPathMap.put(OverviewOfIndicatorsEnum.SHARE_PEOPLE_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //参与用户数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.JOIN_PEOPLE_NUMBER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.JOIN_PEOPLE_NUMBER.getCode());
            if (indexPathResponse == null) {
                indexPathResponse = this.getActivityParticipationPeopleNumber(id,marketingKpiMapByCode);
                indexPathMap.put(OverviewOfIndicatorsEnum.JOIN_PEOPLE_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //活动参与次数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.JOIN_NUMBER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.JOIN_NUMBER.getCode());
            if (indexPathResponse == null) {
                indexPathResponse = this.getActivityParticipationNumber(id,marketingKpiMapByCode);
                indexPathMap.put(OverviewOfIndicatorsEnum.JOIN_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //领取奖品人数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.PRIZE_COLLECTION_PEOPLE_NUMBER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.PRIZE_COLLECTION_PEOPLE_NUMBER.getCode());
            if (indexPathResponse == null) {

                //领奖人数分析
                IndexAnalysisDTO receiveAward = promotionActivityDAO.receiveAwardAnalysis(id);
                //领取奖品人数
                indexPathResponse = ActivityAnalysisConverter.converter(OverviewOfIndicatorsEnum.PRIZE_COLLECTION_PEOPLE_NUMBER
                        , receiveAward.getTotalPeopleNumber()
                        , receiveAward.getTodayPeopleNumber()
                        , receiveAward.getYesterdayPeopleNumber()
                        , marketingKpiMapByCode);
                indexPathMap.put(OverviewOfIndicatorsEnum.PRIZE_COLLECTION_PEOPLE_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }


        //
        // 核销奖品人数 (比如：我在这个活动今天和昨天都领奖了，昨天的未核销，但是今天的核销了，，这时候统计的结果就是  今天没有这个人的领奖人数
        // 但是有这个人的核销人数，因为这个人的今天有核销记录，昨天已经领奖了今天不在统计)
        if (kpiCode.equals(OverviewOfIndicatorsEnum.WRITE_OFF_PEOPLE_NUMBER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.WRITE_OFF_PEOPLE_NUMBER.getCode());
            if (indexPathResponse == null) {
                //核销奖品人数
                IndexAnalysisDTO receiveAward = promotionActivityDAO.writeOffNumberAnalysis(id);
                indexPathResponse = ActivityAnalysisConverter.converter(OverviewOfIndicatorsEnum.WRITE_OFF_PEOPLE_NUMBER
                        , receiveAward.getTotalWriteOffNumber()
                        , receiveAward.getTodayWriteOffNumber()
                        , receiveAward.getYesterdayWriteOffNumber()
                        , marketingKpiMapByCode);
                indexPathMap.put(OverviewOfIndicatorsEnum.WRITE_OFF_PEOPLE_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }


        //订单提交人数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.ORDER_SUB_VOLUME.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.ORDER_SUB_VOLUME.getCode());
            if (indexPathResponse == null) {
                //订单提交量
                indexPathResponse = this.getOrderSubVolumeList(id, marketingKpiMapByCode);
                indexPathMap.put(OverviewOfIndicatorsEnum.ORDER_SUB_VOLUME.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }


        //订单支付人数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.ORDER_PAY_AMOUNT.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.ORDER_PAY_AMOUNT.getCode());
            if (indexPathResponse == null) {
                //订单支付量
                indexPathResponse = this.getOrderPayVolumeList(id, marketingKpiMapByCode);
                indexPathMap.put(OverviewOfIndicatorsEnum.ORDER_PAY_AMOUNT.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //订单退款量
        if (kpiCode.equals(OverviewOfIndicatorsEnum.REFUND_ORDER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.REFUND_ORDER.getCode());
            if (indexPathResponse == null) {
                //订单退款量
                indexPathResponse = this.getOrderRefundVolumeList(id, marketingKpiMapByCode);
                indexPathMap.put(OverviewOfIndicatorsEnum.REFUND_ORDER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //活动留资人数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.ENROLLMENT_INFO_PEOPLE_NUMBER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.ENROLLMENT_INFO_PEOPLE_NUMBER.getCode());
            if (indexPathResponse == null) {

                indexPathResponse =  this.getTodayYesterdayPeopleNumberList(id,marketingKpiMapByCode,
                        SensorsEventEnum.ENROLLMENT_INFO.getCode(),OverviewOfIndicatorsEnum.ENROLLMENT_INFO_PEOPLE_NUMBER,null,null);
                indexPathMap.put(OverviewOfIndicatorsEnum.ENROLLMENT_INFO_PEOPLE_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //帮忙助力人数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.HELP_FRIEND_PEOPLE_NUMBER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.HELP_FRIEND_PEOPLE_NUMBER.getCode());
            if (indexPathResponse == null) {

                indexPathResponse =  this.getTodayYesterdayPeopleNumberList(id,marketingKpiMapByCode,
                        SensorsEventEnum.HELP_FRIEND.getCode(),OverviewOfIndicatorsEnum.HELP_FRIEND_PEOPLE_NUMBER,null,null);
                indexPathMap.put(OverviewOfIndicatorsEnum.HELP_FRIEND_PEOPLE_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //帮忙砍价人数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.HELP_PRICE_CUT_PEOPLE_NUMBER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.HELP_PRICE_CUT_PEOPLE_NUMBER.getCode());
            if (indexPathResponse == null) {

                indexPathResponse =  this.getTodayYesterdayPeopleNumberList(id,marketingKpiMapByCode,
                        SensorsEventEnum.HELP_PRICE_CUT.getCode(),OverviewOfIndicatorsEnum.HELP_PRICE_CUT_PEOPLE_NUMBER,null,null);
                indexPathMap.put(OverviewOfIndicatorsEnum.HELP_PRICE_CUT_PEOPLE_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //砍价成功人数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.PRICE_CUT_PEOPLE_NUMBER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.PRICE_CUT_PEOPLE_NUMBER.getCode());
            if (indexPathResponse == null) {
                indexPathResponse =  this.getTodayYesterdayPeopleNumberList(id, marketingKpiMapByCode,
                    SensorsEventEnum.BARGAIN_SUCCESS.getCode(),OverviewOfIndicatorsEnum.PRICE_CUT_PEOPLE_NUMBER,null,null);
                indexPathMap.put(OverviewOfIndicatorsEnum.PRICE_CUT_PEOPLE_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //助力成功人数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.FRIEND_PEOPLE_NUMBER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.FRIEND_PEOPLE_NUMBER.getCode());
            if (indexPathResponse == null) {
                indexPathResponse =  this.getTodayYesterdayPeopleNumberList(id, marketingKpiMapByCode,
                        SensorsEventEnum.BOOST_SUCCESS.getCode(),OverviewOfIndicatorsEnum.FRIEND_PEOPLE_NUMBER,null,null);
                indexPathMap.put(OverviewOfIndicatorsEnum.FRIEND_PEOPLE_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }


        //获电人数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.ACTIVITY_AUTH.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.ACTIVITY_AUTH.getCode());
            if (indexPathResponse == null) {
                indexPathResponse =  this.getTodayYesterdayPeopleNumberList(id, marketingKpiMapByCode,
                        SensorsEventEnum.ACTIVITY_AUTH.getCode(),OverviewOfIndicatorsEnum.ACTIVITY_AUTH,null,null);
                indexPathMap.put(OverviewOfIndicatorsEnum.ACTIVITY_AUTH.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }


        //活动成交金额
        if (kpiCode.equals(OverviewOfIndicatorsEnum.ACTIVITY_TURNOVER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.ACTIVITY_TURNOVER.getCode());
            if (indexPathResponse == null) {
                indexPathResponse =  this.getTodayYesterdayPeopleNumberList(id, marketingKpiMapByCode,
                        SensorsEventEnum.ACTIVITY_TURNOVER.getCode(),OverviewOfIndicatorsEnum.ACTIVITY_TURNOVER,null,null);
                indexPathMap.put(OverviewOfIndicatorsEnum.ACTIVITY_TURNOVER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //拉新人数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.ACTIVITY_PULL_NEW.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.ACTIVITY_PULL_NEW.getCode());
            if (indexPathResponse == null) {
                indexPathResponse =  this.getTodayYesterdayPeopleNumberList(id, marketingKpiMapByCode,
                        SensorsEventEnum.ACTIVITY_PULL_NEW.getCode(),OverviewOfIndicatorsEnum.ACTIVITY_PULL_NEW,null,null);
                indexPathMap.put(OverviewOfIndicatorsEnum.ACTIVITY_PULL_NEW.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }

        //卡片合成人数
        if (kpiCode.equals(OverviewOfIndicatorsEnum.CARD_COMPOSE_PEOPLE_NUMBER.getCode())) {
            indexPathResponse = indexPathMap.get(OverviewOfIndicatorsEnum.CARD_COMPOSE_PEOPLE_NUMBER.getCode());
            if (indexPathResponse == null) {
                //查询卡片合成人数
                EventsReportQuery eventsReportQuery = new EventsReportQuery();
                eventsReportQuery.setActivityId(id.toString());
                eventsReportQuery.setEventName(SensorsEventEnum.CARD_COMPOSE_PEOPLE_NUMBER.getCode());
                ActivityTodayYesterdayVisitorsDTO activityTodayYesterdayShareNumber = sensorsService.todayYesterdayPeopleNumber(eventsReportQuery);
                //计算比上一天比率
                indexPathResponse = ActivityAnalysisConverter.pageViewConverter(OverviewOfIndicatorsEnum.CARD_COMPOSE_PEOPLE_NUMBER, activityTodayYesterdayShareNumber, marketingKpiMapByCode);

                indexPathMap.put(OverviewOfIndicatorsEnum.CARD_COMPOSE_PEOPLE_NUMBER.getCode(),indexPathResponse);
            }
            return indexPathResponse;
        }



        //未定义指标
        indexPathResponse = new IndexPathResponseDTO();
        indexPathResponse.setBeforeTodayRatio(BigDecimal.ZERO);
        indexPathResponse.setTodayNumber(0L);
        indexPathResponse.setTotalNumber(0L);
        return indexPathResponse;
    }

    /**
     * 查询某个活动在指定时间范围内的指标统计
     * @param query
     * @param indexPathMap
     * @param marketingKpiMapByCode
     */
    private void preGetAllIndicator(ActivityAnalysisQuery query, Map<String, IndexPathResponseDTO> indexPathMap, Map<String, MarketingKpiDTO> marketingKpiMapByCode) {
        //如果有值，说明初始化了，直接返回
        if (!indexPathMap.isEmpty()) {
            return;
        }
        List<ActivityTodayYesterdayVisitorsDTO> list = sensorsService.todayYesterdayPeopleNumberBatch(query.toSensorQuery());
        for (ActivityTodayYesterdayVisitorsDTO activityTodayYesterdayVisitors : list) {
            //根据埋点获取对应的指标
            List<OverviewOfIndicatorsEnum> indexs = OverviewOfIndicatorsEnum.getByEvent(activityTodayYesterdayVisitors.getEvent());
            for (OverviewOfIndicatorsEnum index : indexs) {
                //需要根据指标类型获取人数或次数
                ActivityTodayYesterdayVisitorsDTO item = activityTodayYesterdayVisitors;
                if (index.getType() == ActivityAnalysisDateTypeEnum.BY_PEOPLE) {
                    item = new ActivityTodayYesterdayVisitorsDTO();
                    item.setTotalNumber(activityTodayYesterdayVisitors.getTotalPeopleNumber());
                    item.setTodayNumber(activityTodayYesterdayVisitors.getTodayPeopleNumber());
                    item.setYesterdayNumber(activityTodayYesterdayVisitors.getYesterdayPeopleNumber());
                }
                IndexPathResponseDTO indexPathResponseDTO = ActivityAnalysisConverter.pageViewConverter(index, item, marketingKpiMapByCode);
                indexPathMap.put(index.getCode(), indexPathResponseDTO);
            }
        }
        // 获取所有指标
        Set<String> allIndicatorCodes = marketingKpiMapByCode.keySet();
        // 将 原子指标加入到 indexPathMap 否则后面会重新查询
        for (String indicatorCode : allIndicatorCodes) {
            // 如果不包含，则增加默认的结果0
            if (!indexPathMap.containsKey(indicatorCode)) {
                // 否则使用默认的转换结果
                OverviewOfIndicatorsEnum index = OverviewOfIndicatorsEnum.getByCode(indicatorCode);
                if (index != null) {
                    IndexPathResponseDTO indexPathResponseDTO = ActivityAnalysisConverter.pageViewConverter(index, null, marketingKpiMapByCode);
                    indexPathMap.put(indicatorCode, indexPathResponseDTO);
                }
            }
        }
    }

    private IndexPathResponseDTO getActivityParticipationNumber(Long id, Map<String, MarketingKpiDTO> marketingKpiMap) {
        //查询活动分享次数
        EventsReportQuery eventsReportQuery = new EventsReportQuery();
        eventsReportQuery.setActivityId(id.toString());
        eventsReportQuery.setEventName(SensorsEventEnum.ACTIVITY_PARTICIPATION.getCode());
        ActivityTodayYesterdayVisitorsDTO activityTodayYesterdayShareNumber = sensorsService.todayYesterdayNumber(eventsReportQuery);

        //计算比上一天比率
        return ActivityAnalysisConverter.pageViewConverter(OverviewOfIndicatorsEnum.JOIN_NUMBER, activityTodayYesterdayShareNumber, marketingKpiMap);
    }

    @Override
    public List<ActivityTrendResponseDTO> findActivityTrends(ActivityTrendsQuery query) {
        List<ActivityTrendResponseDTO> activityTrends;
        switch (query.getIndexType()){
            case 1:
                //访问人数趋势统计
                EventsReportQuery eventsReportQuery = new EventsReportQuery();
                eventsReportQuery.setEventName(SensorsEventEnum.ACTIVITY_PAGE_VIEW.getCode());
                eventsReportQuery.setActivityId(query.getId().toString());
                eventsReportQuery.setDate(query.getDate());
                eventsReportQuery.setDateType(query.getDateType());
                activityTrends = sensorsService.findActivityTrendsPageView(eventsReportQuery);
                break;
            case 2:
//                activityTrends = promotionActivityDAO.findActivityTrendJoinPeopleNumber(query);
                EventsReportQuery eventsJoinReportQuery = new EventsReportQuery();
                eventsJoinReportQuery.setEventName(SensorsEventEnum.ACTIVITY_PARTICIPATION.getCode());
                eventsJoinReportQuery.setActivityId(query.getId().toString());
                eventsJoinReportQuery.setDate(query.getDate());
                eventsJoinReportQuery.setDateType(query.getDateType());
                activityTrends = sensorsService.findActivityTrendsPageView(eventsJoinReportQuery);
                break;
            case 3:
                //分享人数趋势统计
                EventsReportQuery sharEventsReportQuery = new EventsReportQuery();
                sharEventsReportQuery.setEventName(SensorsEventEnum.PAGE_SHARE.getCode());
                sharEventsReportQuery.setActivityId(query.getId().toString());
                sharEventsReportQuery.setDate(query.getDate());
                sharEventsReportQuery.setDateType(query.getDateType());
                activityTrends = sensorsService.findActivityTrendsPageView(sharEventsReportQuery);
                break;
            case 4:
//                activityTrends = promotionActivityDAO.findActivityTrendlzPeopleNumber(query);
                EventsReportQuery lzEventsReportQuery = new EventsReportQuery();
                lzEventsReportQuery.setEventName(SensorsEventEnum.ENROLLMENT_INFO.getCode());
                lzEventsReportQuery.setActivityId(query.getId().toString());
                lzEventsReportQuery.setDate(query.getDate());
                lzEventsReportQuery.setDateType(query.getDateType());
                activityTrends = sensorsService.findActivityTrendsPageView(lzEventsReportQuery);
                break;
            default:
                throw new ApplicationException("没有这个选项");
        }
        return activityTrends;
    }

    @Override
    public List<IndexPathResponseDTO> findOverviewOfIndicators(ActivityAnalysisQuery query) {
        if (query.getId() == null){
            throw new ApplicationException("活动id不能为空");
        }

        PromotionActivityDO byId = promotionActivityDAO.getById(query.getId());
        if (byId == null){
            throw new ApplicationException("活动不存在");
        }
        query.setStartTime(DateUtils.toDateText(byId.getStartTime()));
        query.setEndTime(DateUtils.toDateText(byId.getEndTime()));

        //指标详情
        List<MarketingKpiDTO> marketingKpiList = this.getMarketingKpiMap();
        return this.findOverviewOfIndicators(query,Maps.newHashMap(),marketingKpiList,byId);
    }

    @Override
    public PageBean<ActivityAnalysisResponseDTO> posterChannelStatistics(ActivityAnalysisQuery query) {
        if (query.getPage() <1 ){
            query.setPage(1);
        }
        query.setPage((query.getPage()-1) * query.getSize());
        List<ActivityAnalysisResponseDTO> activityAnalysisResponseList = sensorsService.posterChannelStatistics(query);
        if (CollectionUtil.isEmpty(activityAnalysisResponseList)){
            return new PageBean<>();
        }
        activityAnalysisResponseList.forEach(e-> e.setPhone(PhoneEncryUtils.encode(e.getPhone())));

        PageUtil<ActivityAnalysisResponseDTO> responsePageUtil = new PageUtil<>();
        return responsePageUtil.getPage((int)query.getPage(), (int)query.getSize(),sensorsService.posterChannelStatisticsCount(query).intValue(), activityAnalysisResponseList);
    }

    private void findActivityTrend(ActivityAnalysisDetailResponseDTO detail, Long id) {
        //初始查询默认该活动当天统计数据
        ActivityTrendsQuery query = new ActivityTrendsQuery();
        query.setDate(DateUtils.getCurrenDate());
        query.setId(id);
        query.setDateType(1);
        List<ActivityTrendResponseDTO> activityTrends = promotionActivityDAO.findActivityTrendJoinPeopleNumber(query);
        detail.setActivityTrendResponseDTO(activityTrends);
    }

    /**
     * 活动期间到访人数,活动转到访人数,活动期间成交人数,活动转成交人数 从客户中心获取
     * @param detail
     * @param id
     */
    private void findCityDistribution(ActivityAnalysisDetailResponseDTO detail, Long id) {

        //查询神策信息登记数据  ===  神策参与活动人数
        EventsReportQuery eventsReportQuery = new EventsReportQuery();
        eventsReportQuery.setActivityId(id.toString());
        eventsReportQuery.setStartTime(detail.getStartTime());
        eventsReportQuery.setEndTime(detail.getEndTime());
        eventsReportQuery.setEventName(SensorsEventEnum.ENROLLMENT_INFO.getCode());
        List<ActivityPageViewVisitorsDTO> formSubmitList = sensorsService.findCityDistributionList(eventsReportQuery);
        if(CollectionUtil.isNotEmpty(formSubmitList)){

            int rank = 1;
            long joinPeopleNumberTotal = formSubmitList.stream().mapToLong(ActivityPageViewVisitorsDTO::getQuantity).sum();
            List<CityDistributionResponseDTO> channelDistribution = Lists.newArrayList();
            for (ActivityPageViewVisitorsDTO city : formSubmitList) {
                CityDistributionResponseDTO cityDistribution = new CityDistributionResponseDTO();
                cityDistribution.setProvince(PinyinUtils.pinyinToChinese(city.getProvince()));
                cityDistribution.setJoinPeopleNumber(city.getQuantity());
                cityDistribution.setRank(rank);

                //计算占比
                BigDecimal percentage = BigDecimal.ZERO;
                if (joinPeopleNumberTotal > 0) {
                    percentage = new BigDecimal(city.getQuantity())
                            .divide(new BigDecimal(joinPeopleNumberTotal), 4, BigDecimal.ROUND_HALF_UP)
                            .multiply(new BigDecimal(100));
                }

                cityDistribution.setPercentage(percentage);

                channelDistribution.add(cityDistribution);
                rank += 1;
            }
            detail.setCityDistributionList(channelDistribution);
        }else{
            detail.setCityDistributionList(new ArrayList<>());
        }

    }

    private void findChannelDistribution(ActivityAnalysisDetailResponseDTO detail, Long id) {

        //统计神策各渠道 访问人数
        EventsReportQuery eventsReportQuery = new EventsReportQuery();
        eventsReportQuery.setActivityId(id.toString());
        eventsReportQuery.setStartTime(detail.getStartTime());
        eventsReportQuery.setEndTime(detail.getEndTime());
        List<ChannelDistributionResponseDTO> sensorsChannelDistribution = sensorsService.sensorsChannelDistribution(eventsReportQuery);
        if (CollectionUtil.isNotEmpty(sensorsChannelDistribution)){

            detail.setChannelDistributionList(sensorsChannelDistribution);
        }else {
            detail.setChannelDistributionList(Lists.newArrayList());
        }

    }

    private List<IndexPathResponseDTO> findOverviewOfIndicators(ActivityAnalysisQuery query,
                                                                Map<String,IndexPathResponseDTO> indexPathMap,
                                                                List<MarketingKpiDTO> marketingKpiList,PromotionActivityDO activityDO) {
        Map<Long, MarketingKpiDTO> marketingKpiMapById = Maps.newHashMap();
        Map<String, MarketingKpiDTO> marketingKpiMapByCode = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(marketingKpiList)){
            marketingKpiMapById = marketingKpiList.stream().collect(Collectors.toMap(MarketingKpiDTO::getId, v->v));
            marketingKpiMapByCode = marketingKpiList.stream().collect(Collectors.toMap(MarketingKpiDTO::getCode, v->v));
        }

        //先查询缓存记录，没有在使用默认指标
        if (CollectionUtil.isEmpty(query.getCustomIndicator())) {
            RBucket<List<String>> bucketGet = redissonClient.getBucket(String.format(RedisConstants.ACTIVITY_ANALYSIS_OVERVIEW_OF_INDICATORS_RECORD, query.getCreatedBy(),query.getId()));
            List<String> customIndicators = bucketGet.get();
            if (CollectionUtil.isNotEmpty(customIndicators)) {
                query.setCustomIndicator(customIndicators);
            }
        }

        //指标概念自定义查询
        List<IndexPathResponseDTO> indexPathResponseList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(query.getCustomIndicator())) {

            //记录用户选择的指标
            RBucket<List<String>> bucket = redissonClient.getBucket(String.format(RedisConstants.ACTIVITY_ANALYSIS_OVERVIEW_OF_INDICATORS_RECORD,query.getCreatedBy(),query.getId()));
            bucket.set(query.getCustomIndicator());

            //确定缓存过期时间
            long expireTime;
            if(Objects.nonNull(activityDO.getEndTime())){
                expireTime = activityDO.getEndTime().getTime()-System.currentTimeMillis();
                expireTime = (expireTime / 1000) + RedisConstants.EXTRA_EXPIRE_SECOND;
            }else{
                expireTime = RedisConstants.SECKILL_EXPIRE_SECOND;
            }
            bucket.expire(expireTime, TimeUnit.SECONDS);

            for (String indicator : query.getCustomIndicator()) {
                IndexPathResponseDTO indexPath = this.findIndexPath(query, indicator,
                        indexPathMap, marketingKpiMapByCode, marketingKpiMapById);
                if (null != indexPath) {
                    indexPathResponseList.add(indexPath);
                }
            }
        }else{
            //指标概念默认查询
            String customIndicator = this.getDefaultCustomIndicator(activityDO.getPaTemplateId());
//            indexPathResponseList = getIndexPathResponseDTOS(query,indexPathMap,marketingKpiMapByCode,customIndicator);
            for (String indicator : customIndicator.split(",")) {
                IndexPathResponseDTO indexPath = this.findIndexPath(query, indicator,
                        indexPathMap, marketingKpiMapByCode, marketingKpiMapById);
                if (null != indexPath) {
                    indexPathResponseList.add(indexPath);
                }
            }
        }

        return indexPathResponseList;
    }

    private String getDefaultCustomIndicator(Integer paTemplateId) {
        String customIndicator;
        switch (StrategyGroupEnum.getById(String.valueOf(paTemplateId))){
            case HF_LUCKYDRAW_ACT:
                //活动访问次数	活动访问人数	活动分享次数	活动分享人数	活动参与人数	核销奖品人数
                customIndicator = "L4QBWC,L4YAwB,L4YAwE,L4YAwF,L4YAwA,L5KH57";
                break;
            case HF_FORM_ACT:
                //活动访问次数	活动访问人数	活动分享次数	活动分享人数	活动参与人数
                customIndicator = "L4QBWC,L4YAwB,L4YAwE,L4YAwF,L4YAwA";
                break;
            case HF_COUPON_ACT:
                //活动访问次数	活动访问人数	活动分享次数	活动分享人数	活动参与人数	核销优惠券人数
            case HF_SECKILL_ACT:
                //活动访问次数	活动访问人数	活动分享次数	活动分享人数	活动参与人数	核销优惠券人数
                customIndicator = "L4QBWC,L4YAwB,L4YAwE,L4YAwF,L4YAwA,L5KH57";
                break;
            case HF_FISSION_ASSIST_ACT:
                //活动访问次数	活动访问人数	活动分享次数	活动分享人数	活动参与人数	助力成功人数	核销奖品人数
            case HF_FISSION_REDUCEPRICE_ACT:
                //活动访问次数	活动访问人数	活动分享次数	活动分享人数	活动参与人数	助力成功人数	核销奖品人数
                customIndicator = "L4QBWC,L4YAwB,L4YAwE,L4YAwF,L4YAwA,L5KH58,L5KH57";
                break;
            case HF_CARD_COLLECTING_ACT:
                //活动访问次数	活动访问人数	活动分享次数	活动分享人数	活动参与人数   领取礼品人数 合成卡片人数
                customIndicator = "L4QBWC,L4YAwB,L4YAwE,L4YAwF,L4YAwA,L4QBat,L5KH71";
                break;
            default:
                // 活动访问人数   活动访问次数   活动参与人数  活动参与次数   领取礼品人数   活动核销人数
                customIndicator = "L4QBWC,L4YAwB,L4YAwA,L4QBWX,L4QBat,L5KH57";
        }
        return customIndicator;
    }

    private List<IndexPathResponseDTO> getIndexPathResponseDTOS(ActivityAnalysisQuery query,Map<String, IndexPathResponseDTO> indexPathMap, Map<String, MarketingKpiDTO> marketingKpiMap, String customIndicator) {
        List<IndexPathResponseDTO> indexPathResponseList = Lists.newArrayList();


        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(OverviewOfIndicatorsEnum.WECHAT_APPLET.getCode())) {
            //浏览用户数
            IndexPathResponseDTO indexPathResponseDTO  = this.getPageViewVisitorsList(query.getId(), marketingKpiMap);
            indexPathResponseList.add(indexPathResponseDTO);
            if (indexPathMap != null) {
                indexPathMap.put(OverviewOfIndicatorsEnum.WECHAT_APPLET.getCode(), indexPathResponseDTO);
            }
        }

        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(OverviewOfIndicatorsEnum.WECHAT_WEBSITE.getCode())) {
            //浏览次数
            IndexPathResponseDTO indexPathResponseDTO  = this.getPageViewVisitorsFrequencyList(query.getId(), marketingKpiMap);
            indexPathResponseList.add(indexPathResponseDTO);
            if (indexPathMap != null) {
                indexPathMap.put(OverviewOfIndicatorsEnum.WECHAT_WEBSITE.getCode(), indexPathResponseDTO);
            }
        }


        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(OverviewOfIndicatorsEnum.JOIN_PEOPLE_NUMBER.getCode())) {
            //参与人数
            IndexPathResponseDTO indexPathJoinPeopleNumber = this.getActivityParticipationPeopleNumber(query.getId(), marketingKpiMap);
            indexPathResponseList.add(indexPathJoinPeopleNumber);
            if (indexPathMap != null) {
                indexPathMap.put(OverviewOfIndicatorsEnum.JOIN_PEOPLE_NUMBER.getCode(), indexPathJoinPeopleNumber);
            }
        }

        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(OverviewOfIndicatorsEnum.JOIN_NUMBER.getCode())) {
            //活动参与次数
            IndexPathResponseDTO indexPathJoinPeopleNumber = this.getActivityParticipationNumber(query.getId(), marketingKpiMap);
            indexPathResponseList.add(indexPathJoinPeopleNumber);
            if (indexPathMap != null) {
                indexPathMap.put(OverviewOfIndicatorsEnum.JOIN_NUMBER.getCode(), indexPathJoinPeopleNumber);
            }
        }

        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(OverviewOfIndicatorsEnum.PRIZE_COLLECTION_PEOPLE_NUMBER.getCode())) {
            //领奖人数分析
            IndexAnalysisDTO receiveAward = promotionActivityDAO.receiveAwardAnalysis(query.getId());
            IndexPathResponseDTO indexPathPrizeNumber = ActivityAnalysisConverter.converter(OverviewOfIndicatorsEnum.PRIZE_COLLECTION_PEOPLE_NUMBER
                    , receiveAward.getTotalPeopleNumber()
                    , receiveAward.getTodayPeopleNumber()
                    , receiveAward.getYesterdayPeopleNumber()
                    , marketingKpiMap);
            indexPathResponseList.add(indexPathPrizeNumber);
            if (indexPathMap != null) {
                indexPathMap.put(OverviewOfIndicatorsEnum.PRIZE_COLLECTION_PEOPLE_NUMBER.getCode(), indexPathPrizeNumber);
            }
        }

        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(OverviewOfIndicatorsEnum.WRITE_OFF_PEOPLE_NUMBER.getCode())) {
            //核销奖品人数
            IndexAnalysisDTO receiveAward = promotionActivityDAO.writeOffNumberAnalysis(query.getId());
            IndexPathResponseDTO indexPathWriteOffNumber = ActivityAnalysisConverter.converter(OverviewOfIndicatorsEnum.WRITE_OFF_PEOPLE_NUMBER
                    , receiveAward.getTotalWriteOffNumber()
                    , receiveAward.getTodayWriteOffNumber()
                    , receiveAward.getYesterdayWriteOffNumber()
                    , marketingKpiMap);
            indexPathResponseList.add(indexPathWriteOffNumber);
            if (indexPathMap != null) {
                indexPathMap.put(OverviewOfIndicatorsEnum.WRITE_OFF_PEOPLE_NUMBER.getCode(), indexPathWriteOffNumber);
            }
        }

        //订单提交量
        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(OverviewOfIndicatorsEnum.ORDER_SUB_VOLUME.getCode())){
                //订单提交量
            IndexPathResponseDTO indexPathResponse =  this.getOrderSubVolumeList(query.getId(), marketingKpiMap);
            indexPathResponseList.add(indexPathResponse);
            if (indexPathMap != null) {
                indexPathMap.put(OverviewOfIndicatorsEnum.ORDER_SUB_VOLUME.getCode(), indexPathResponse);
            }
        }


        //订单支付量
        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(OverviewOfIndicatorsEnum.ORDER_PAY_AMOUNT.getCode())){
                //订单支付量
            IndexPathResponseDTO indexPathResponse =  this.getOrderPayVolumeList(query.getId(), marketingKpiMap);
            indexPathResponseList.add(indexPathResponse);
            if (indexPathMap != null) {
                indexPathMap.put(OverviewOfIndicatorsEnum.ORDER_PAY_AMOUNT.getCode(), indexPathResponse);
            }
        }

        //订单退款量
        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(OverviewOfIndicatorsEnum.REFUND_ORDER.getCode())){
                //订单退款量
            IndexPathResponseDTO indexPathResponse =  this.getOrderRefundVolumeList(query.getId(), marketingKpiMap);
            indexPathResponseList.add(indexPathResponse);
            if (indexPathMap != null) {
                indexPathMap.put(OverviewOfIndicatorsEnum.REFUND_ORDER.getCode(), indexPathResponse);
            }
        }

        //活动分享次数
        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(OverviewOfIndicatorsEnum.SHARE_NUMBER.getCode())){
                //活动分享次数
            IndexPathResponseDTO indexPathResponse =  this.getShareNumberList(query.getId(), marketingKpiMap);
            indexPathResponseList.add(indexPathResponse);
            if (indexPathMap != null) {
                indexPathMap.put(OverviewOfIndicatorsEnum.SHARE_NUMBER.getCode(), indexPathResponse);
            }
        }

        //活动分享人数
        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(OverviewOfIndicatorsEnum.SHARE_PEOPLE_NUMBER.getCode())){
            IndexPathResponseDTO indexPathResponse =  this.getTodayYesterdayPeopleNumberList(query.getId(), marketingKpiMap,
                    SensorsEventEnum.PAGE_SHARE.getCode(), OverviewOfIndicatorsEnum.SHARE_PEOPLE_NUMBER,null,null);
            indexPathResponseList.add(indexPathResponse);
            if (indexPathMap != null) {
                indexPathMap.put(OverviewOfIndicatorsEnum.SHARE_PEOPLE_NUMBER.getCode(), indexPathResponse);
            }
        }


        //帮助助力人数
        OverviewOfIndicatorsEnum helpFriendPeopleNumber = OverviewOfIndicatorsEnum.HELP_FRIEND_PEOPLE_NUMBER;
        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(helpFriendPeopleNumber.getCode())){
            IndexPathResponseDTO indexPathResponse =  this.getTodayYesterdayPeopleNumberList(query.getId(), marketingKpiMap,
                    SensorsEventEnum.HELP_FRIEND.getCode(),helpFriendPeopleNumber,null,null);
            indexPathResponseList.add(indexPathResponse);
            if (indexPathMap != null) {
                indexPathMap.put(helpFriendPeopleNumber.getCode(), indexPathResponse);
            }
        }

        //帮助砍价人数
        OverviewOfIndicatorsEnum helpPriceCutPeopleNumber = OverviewOfIndicatorsEnum.HELP_PRICE_CUT_PEOPLE_NUMBER;
        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(helpPriceCutPeopleNumber.getCode())){
            IndexPathResponseDTO indexPathResponse =  this.getTodayYesterdayPeopleNumberList(query.getId(), marketingKpiMap,
                    SensorsEventEnum.HELP_PRICE_CUT.getCode(),helpPriceCutPeopleNumber,null,null);
            indexPathResponseList.add(indexPathResponse);
            if (indexPathMap != null) {
                indexPathMap.put(helpPriceCutPeopleNumber.getCode(), indexPathResponse);
            }
        }

        //活动留资人数
        OverviewOfIndicatorsEnum enrollmentInfoPeopleNumber = OverviewOfIndicatorsEnum.ENROLLMENT_INFO_PEOPLE_NUMBER;
        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(enrollmentInfoPeopleNumber.getCode())){
            IndexPathResponseDTO indexPathResponse =  this.getTodayYesterdayPeopleNumberList(query.getId(), marketingKpiMap,
                    SensorsEventEnum.ENROLLMENT_INFO.getCode(),enrollmentInfoPeopleNumber,null,null);
            indexPathResponseList.add(indexPathResponse);
            if (indexPathMap != null) {
                indexPathMap.put(enrollmentInfoPeopleNumber.getCode(), indexPathResponse);
            }
        }

        //砍价成功人数
        OverviewOfIndicatorsEnum priceCutPeopleNumber = OverviewOfIndicatorsEnum.PRICE_CUT_PEOPLE_NUMBER;
        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(priceCutPeopleNumber.getCode())){
            IndexPathResponseDTO indexPathResponse =  this.getTodayYesterdayPeopleNumberList(query.getId(), marketingKpiMap,
                    SensorsEventEnum.BARGAIN_SUCCESS.getCode(),priceCutPeopleNumber,null,null);
            indexPathResponseList.add(indexPathResponse);
            if (indexPathMap != null) {
                indexPathMap.put(priceCutPeopleNumber.getCode(), indexPathResponse);
            }
        }

        //助力成功人数
        OverviewOfIndicatorsEnum friendPeopleNumber = OverviewOfIndicatorsEnum.FRIEND_PEOPLE_NUMBER;
        if (StringUtil.isNotBlank(customIndicator) && customIndicator.contains(friendPeopleNumber.getCode())){
            IndexPathResponseDTO indexPathResponse =  this.getTodayYesterdayPeopleNumberList(query.getId(), marketingKpiMap,
                    SensorsEventEnum.BOOST_SUCCESS.getCode(),friendPeopleNumber,null,null);
            indexPathResponseList.add(indexPathResponse);
            if (indexPathMap != null) {
                indexPathMap.put(friendPeopleNumber.getCode(), indexPathResponse);
            }
        }
        return indexPathResponseList;
    }

    /**
     * 查询活动页面浏览次数
     * @param id
     * @param marketingKpiMap
     * @return
     */
    private IndexPathResponseDTO getPageViewVisitorsFrequencyList(Long id, Map<String, MarketingKpiDTO> marketingKpiMap) {
        //今日及昨日浏览次数
        EventsReportQuery eventsReportQuery = new EventsReportQuery();
        eventsReportQuery.setActivityId(id.toString());
        eventsReportQuery.setEventName(SensorsEventEnum.ACTIVITY_PAGE_VIEW.getCode());
        ActivityTodayYesterdayVisitorsDTO activityTodayYesterdayVisitor = sensorsService.todayYesterdayNumber(eventsReportQuery);

        return ActivityAnalysisConverter.pageViewConverter(OverviewOfIndicatorsEnum.WECHAT_WEBSITE, activityTodayYesterdayVisitor, marketingKpiMap);
    }

    /**
     * 查询活动页面浏览用户数
     * @param id
     * @param marketingKpiMap
     * @return
     */
    private IndexPathResponseDTO getPageViewVisitorsList(Long id,Map<String, MarketingKpiDTO> marketingKpiMap) {
        //今日及昨日浏览用户数
        EventsReportQuery eventsReportQuery = new EventsReportQuery();
        eventsReportQuery.setActivityId(id.toString());
        eventsReportQuery.setEventName(SensorsEventEnum.ACTIVITY_PAGE_VIEW.getCode());
        ActivityTodayYesterdayVisitorsDTO activityTodayYesterdayVisitors = sensorsService.todayYesterdayPeopleNumber(eventsReportQuery);

        //计算比上一天比率
        return ActivityAnalysisConverter.pageViewConverter(OverviewOfIndicatorsEnum.WECHAT_APPLET, activityTodayYesterdayVisitors, marketingKpiMap);
    }
    /**
     * 查询活动参与用户数
     * @param id
     * @param marketingKpiMap
     * @return
     */
    private IndexPathResponseDTO getActivityParticipationPeopleNumber(Long id,Map<String, MarketingKpiDTO> marketingKpiMap) {
        //今日及昨日浏览用户数
        EventsReportQuery eventsReportQuery = new EventsReportQuery();
        eventsReportQuery.setActivityId(id.toString());
        eventsReportQuery.setEventName(SensorsEventEnum.ACTIVITY_PARTICIPATION.getCode());
        ActivityTodayYesterdayVisitorsDTO activityTodayYesterdayVisitors = sensorsService.todayYesterdayPeopleNumber(eventsReportQuery);

        //计算比上一天比率
        return ActivityAnalysisConverter.pageViewConverter(OverviewOfIndicatorsEnum.JOIN_PEOPLE_NUMBER, activityTodayYesterdayVisitors, marketingKpiMap);
    }

    /**
     * 查询订单提交量
     * @param id
     * @param marketingKpiMap
     * @return
     */
    private IndexPathResponseDTO getOrderSubVolumeList(Long id,Map<String, MarketingKpiDTO> marketingKpiMap) {
        //今日及昨日浏览用户数
        EventsReportQuery eventsReportQuery = new EventsReportQuery();
        eventsReportQuery.setActivityId(id.toString());
        eventsReportQuery.setEventName(SensorsEventEnum.SUBMIT_ORDERS.getCode());
        ActivityTodayYesterdayVisitorsDTO activityTodayYesterdayVisitors = sensorsService.todayYesterdayPeopleNumber(eventsReportQuery);

        //计算比上一天比率
        return ActivityAnalysisConverter.pageViewConverter(OverviewOfIndicatorsEnum.ORDER_SUB_VOLUME, activityTodayYesterdayVisitors, marketingKpiMap);
    }

    /**
     * 查询订单支付量
     * @param id
     * @param marketingKpiMap
     * @return
     */
    private IndexPathResponseDTO getOrderPayVolumeList(Long id,Map<String, MarketingKpiDTO> marketingKpiMap) {
        //今日及昨日支付量
        EventsReportQuery eventsReportQuery = new EventsReportQuery();
        eventsReportQuery.setActivityId(id.toString());
        eventsReportQuery.setEventName(SensorsEventEnum.PAY_ORDER.getCode());
        ActivityTodayYesterdayVisitorsDTO activityTodayYesterdayVisitors = sensorsService.todayYesterdayPeopleNumber(eventsReportQuery);

        //计算比上一天比率
        return ActivityAnalysisConverter.pageViewConverter(OverviewOfIndicatorsEnum.ORDER_PAY_AMOUNT, activityTodayYesterdayVisitors, marketingKpiMap);
    }

    /**
     * 查询订单退款量
     * @param id
     * @param marketingKpiMap
     * @return
     */
    private IndexPathResponseDTO getOrderRefundVolumeList(Long id,Map<String, MarketingKpiDTO> marketingKpiMap) {
        //今日及昨日支付量
        EventsReportQuery eventsReportQuery = new EventsReportQuery();
        eventsReportQuery.setActivityId(id.toString());
        eventsReportQuery.setEventName(SensorsEventEnum.REFUND_ORDER.getCode());
        ActivityTodayYesterdayVisitorsDTO activityTodayYesterdayVisitors = sensorsService.todayYesterdayPeopleNumber(eventsReportQuery);

        //计算比上一天比率
        return ActivityAnalysisConverter.pageViewConverter(OverviewOfIndicatorsEnum.REFUND_ORDER, activityTodayYesterdayVisitors, marketingKpiMap);
    }
    /**
     * 查询活动分享人数
     * @param id
     * @param marketingKpiMap
     * @return
     */
    private IndexPathResponseDTO getTodayYesterdayPeopleNumberList(Long id,Map<String, MarketingKpiDTO> marketingKpiMap,
                                                                   String eventName,OverviewOfIndicatorsEnum overviewOfIndicators,
                                                                   String startDate,String endDate) {

        //查询活动分享次数
        EventsReportQuery eventsReportQuery = new EventsReportQuery();
        eventsReportQuery.setActivityId(id.toString());
        eventsReportQuery.setEventName(eventName);
        if (StringUtil.isNotBlank(startDate) && StringUtil.isNotBlank(endDate)){
            eventsReportQuery.setStartTime(startDate);
            eventsReportQuery.setEndTime(endDate);
        }
        ActivityTodayYesterdayVisitorsDTO activityTodayYesterdayShareNumber = sensorsService.todayYesterdayPeopleNumber(eventsReportQuery);

        //计算比上一天比率
        return ActivityAnalysisConverter.pageViewConverter(overviewOfIndicators, activityTodayYesterdayShareNumber, marketingKpiMap);
    }
    /**
     * 查询活动分享次数
     * @param id
     * @param marketingKpiMap
     * @return
     */
    private IndexPathResponseDTO getShareNumberList(Long id,Map<String, MarketingKpiDTO> marketingKpiMap) {

        //查询活动分享次数
        EventsReportQuery eventsReportQuery = new EventsReportQuery();
        eventsReportQuery.setActivityId(id.toString());
        eventsReportQuery.setEventName(SensorsEventEnum.PAGE_SHARE.getCode());
        ActivityTodayYesterdayVisitorsDTO activityTodayYesterdayShareNumber = sensorsService.todayYesterdayNumber(eventsReportQuery);

        //计算比上一天比率
        return ActivityAnalysisConverter.pageViewConverter(OverviewOfIndicatorsEnum.SHARE_NUMBER, activityTodayYesterdayShareNumber, marketingKpiMap);
    }

    private List<MarketingKpiDTO> getMarketingKpiMap() {
        MarketingKpiQuery marketingKpiQuery = new MarketingKpiQuery();
        marketingKpiQuery.setStatus(1);
        return marketingKpiService.queryList(marketingKpiQuery,true);
    }

}
