package com.deepexi.dxp.marketing.controller.specify.adminapi;


import com.cnhuafas.common.annotation.DistributedLock;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.PartakeLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeRequest;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.PromotionActivityUpdateStatusRequest;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.OneCodeAuthDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityVO;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityPageQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.*;
import com.deepexi.dxp.marketing.enums.status.ActivityOnOffShelfStatus;
import com.deepexi.dxp.marketing.enums.status.ActivityStatus;
import com.deepexi.dxp.marketing.service.specify.ActivityParticipationService;
import com.deepexi.dxp.marketing.service.specify.PromotionActivityService;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFissionLogDO;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


@RestController
@Slf4j
@RequestMapping("/admin-api/v1/promotion-activity")
@Api(value = "活动管理", tags = "活动管理")
public class PromotionActivityController {

    @Autowired
    private PromotionActivityService promotionActivityService;

    @Autowired
    private ActivityParticipationService activityParticipationService;


    @GetMapping("/list")
    @ApiOperation(value="活动列表", notes = "活动列表")
    public Data<PageBean<PromotionActivityVO>> list(PromotionActivityQuery query) {
        return new Data<>(promotionActivityService.findPage(query));
    }

    /**
     * Feign调用方法
     * @param query
     * @return
     */
    @PostMapping("/listPost")
    @ApiOperation(value="活动列表", notes = "活动列表")
    public Data<PageBean<PromotionActivityVO>> listPost(@RequestBody PromotionActivityQuery query) {
        return new Data<>(promotionActivityService.findPage(query));
    }

    @PostMapping("/wholeList")
    @ApiOperation(value="分页查询所有活动", notes = "分页查询所有活动")
    public Data<PageBean<PromotionActivityDTO>> wholeList(@RequestBody PromotionActivityPageQuery query) {
        return new Data<>(promotionActivityService.findWholePageList(query));
    }

    /**
     * 创建活动
     *
     * @param vo
     * @return
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建活动")
    public Data<Long> create(@RequestBody PromotionActivityCreateRequest vo) {
        return new Data<>(promotionActivityService.create(vo));
    }

    @DeleteMapping("/delete")
    @ApiOperation(value="删除活动", notes = "删除活动")
    public Data<Boolean> delete(@RequestBody PromotionActivityDeleteRequest vo) {
        return new Data<>(promotionActivityService.delete(vo.getIds()));
    }

    /**
     * 修改活动
     *
     * @param vo
     * @return
     */
    @PutMapping("/update")
    @ApiOperation(value = "修改活动")
    public Data<Boolean> update(@RequestBody PromotionActivityUpdateRequest vo) {
        return new Data<>(promotionActivityService.updateActivityById(vo));
    }

    @ApiOperation(value = "更改活动状态，仅适用于秒杀活动")
    @PostMapping("/seckillUpdateStatus")
    public Data<Boolean> seckillUpdateStatus(@Valid @RequestBody PromotionActivityUpdateStatusRequest requestVo) {
        return new Data<>(promotionActivityService.seckillUpdateStatus(requestVo));
    }

    @ApiOperation(value = "终止活动-")
    @PutMapping("/stop")
    public Data<Boolean> stop(@RequestParam Long id) {
        return new Data<>(promotionActivityService.updateStatus(id, ActivityStatus.FINISH.getId()));
    }

    @ApiOperation(value = "开始活动")
    @PutMapping("/start")
    public Data<Boolean> start(@RequestParam Long id) {
        return new Data<>(promotionActivityService.updateStatus(id, ActivityStatus.IN_PROGRESS.getId()));
    }

    /**
     * 根据id查询活动
     *
     * @param id
     * @return
     */
    @GetMapping("/getById")
    @ApiOperation(value = "根据id查询活动")
    public Data<PromotionActivityDetailDTO> findActivityById(@RequestParam Long id) throws Exception {
        return new Data<>(promotionActivityService.getActivityById(id));
    }


    @PostMapping("/partakeLogList")
    @ApiOperation(value="活动参与明细", notes = "活动参与明细")
    public Data<PageBean<ActivityPartakeLogResponseDTO>> partakeLogList(@RequestBody @Valid PartakeLogQuery query) {
        return new Data<>(promotionActivityService.actPartakeLogList(query));
    }


    @ApiOperation(value="获取参与明细导出数据", notes = "获取参与明细导出数据")
    @PostMapping("/getExportPartakeLogExcelData")
    public ExcelExportActPartakeLogResponseDTO getExportPartakeLogExcelData(@RequestBody PartakeLogQuery query){
        return promotionActivityService.getExportPartakeLogExcelData(query);
    }


    @GetMapping("/projectListByActivityId")
    @ApiOperation(value="项目列表", notes = "项目列表")
    public Data<List<ActivityParticipationGroupResponseDTO>> getProjectList(@RequestParam Long id) {
        return new Data<>(activityParticipationService.projectGroupList(id));
    }

    @PostMapping("/rankList")
    @ApiOperation(value="排行榜", notes = "排行榜")
    public Data<FissionRankAdminResponseDTO> rankList(@RequestParam Long activityId) {
        FissionRankAdminResponseDTO fissionRankAdminResponseDTO = new FissionRankAdminResponseDTO();
        List<FissionRankResponseDTO> fissionRankResponseDTOS = promotionActivityService.rankList(activityId);
        if(CollectionUtil.isNotEmpty(fissionRankResponseDTOS)){
            fissionRankResponseDTOS = fissionRankResponseDTOS.stream().sorted(Comparator.comparing(FissionRankResponseDTO::getCnt)).collect(Collectors.toList());
            List<String> nickNameList = fissionRankResponseDTOS.stream().map(FissionRankResponseDTO::getNickName).collect(Collectors.toList());
            List<Integer> cntList = fissionRankResponseDTOS.stream().map(FissionRankResponseDTO::getCnt).collect(Collectors.toList());
            fissionRankAdminResponseDTO.setNickNameList(nickNameList);
            fissionRankAdminResponseDTO.setCntList(cntList);
        }
        return new Data<>(fissionRankAdminResponseDTO);
    }

    @GetMapping("/fissionFriendsList")
    @ApiOperation(value="裂变好友列表", notes = "裂变好友列表")
    public Data<PageBean<ActivityFissionLogResponseDTO>> fissionFriendsList(ActivityFissionLogQuery query) {
        return new Data<>(promotionActivityService.fissionFriendsList(query));
    }

    @PostMapping("/fissionFriendsListPost")
    @ApiOperation(value="裂变好友列表", notes = "裂变好友列表")
    public Data<PageBean<ActivityFissionLogResponseDTO>> fissionFriendsListPost(@RequestBody ActivityFissionLogQuery query) {
        return new Data<>(promotionActivityService.fissionFriendsList(query));
    }

    @GetMapping("/getUUID")
    @ApiOperation(value = "从后端获取uuid")
    public  Data<String> getUUID(@RequestParam String createdBy){
        return new Data<>(promotionActivityService.getUUID(String.format(RedisConstants.CACHE_PREV_KEY_ACT_CREATE_INFO_REPEAT,createdBy)));
    }


    @ApiOperation(value = "活动下架")
    @PutMapping("/offShelf")
    public Data<Boolean> offShelf(@RequestParam Long id,@RequestParam(required = false) String deliveryChannel) {
        return new Data<>(promotionActivityService.updateOnOffStatus(id, ActivityOnOffShelfStatus.OFF.getId(),deliveryChannel));
    }

    @ApiOperation(value = "活动上架")
    @PutMapping("/onShelf")
    public Data<Boolean> onShelf(@RequestParam Long id,@RequestParam String deliveryChannel) {
        return new Data<>(promotionActivityService.updateOnOffStatus(id, ActivityOnOffShelfStatus.ON.getId(),deliveryChannel));
    }

    @PostMapping("/partakeAct")
    @ApiOperation(value = "参与活动", notes = "参与活动")
    public Data<Object> partakeAct(@RequestBody @Valid ActivityPartakeRequest dto) {
        return promotionActivityService.partakeAct(dto);
    }

    @PostMapping("/audit")
    @ApiOperation(value = "审核活动")
    public Data<Boolean> audit(@RequestBody @Valid ActivityAuditRequest dto) {
        return new Data<>(promotionActivityService.audit(dto));
    }

    @GetMapping("/scanList")
    @ApiOperation(value = "扫码签到记录")
    public Data<List<OneCodeAuthDTO>> scanList(@RequestParam Long id) {
        return new Data<>(promotionActivityService.scanList(id));
    }
    @ApiOperation(value = "活动置顶/取消置顶")
    @PostMapping("/toggleTop")
    public Data<Boolean> toggleTop(@RequestParam Long id, @RequestParam Integer status) {
        return new Data<>(promotionActivityService.toggleTopStatus(id, status));
    }
    @ApiOperation(value = "取消参与活动")
    @PostMapping("/cancelAct")
    @DistributedLock(suffix = "#partakeLogId")
    public Data<Boolean> cancelAct(@Valid @RequestBody CancelActivityRequest dto) {
        dto.setAdmin(true);
        return new Data<>(promotionActivityService.cancelActByAdmin(dto));
    }
}
