package com.deepexi.dxp.marketing.controller.specify.adminapi;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityVerifyRejectLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MyVerifyQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.VerifyPageQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityVerifyRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyInResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyRejectLogResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ExcelExportActVerifyResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.OrderDetailResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.VerifyOrderResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.VerifyOrderPageQuery;
import com.deepexi.dxp.marketing.service.specify.ActivityVerifyRejectLogService;
import com.deepexi.dxp.marketing.service.specify.ActivityVerifyService;
import com.deepexi.dxp.marketing.utils.ExcelExportActPartakeLogUtil;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@RequestMapping("/admin-api/v1/verify")
@Api(value = "核销管理-",tags = {"核销管理模块"})
public class ActivityVerifyController {

    @Autowired
    private ActivityVerifyService activityVerifyService;

    @Autowired
    private ActivityVerifyRejectLogService activityVerifyRejectLogService;

    @GetMapping("/list")
    @ApiOperation(value="核销列表", notes = "核销列表")
    public Data<PageBean<ActivityVerifyInResponseDTO>> list(@Valid VerifyPageQuery query) {
        return new Data<>(activityVerifyService.findPage(query));
    }
    @PostMapping("/listPost")
    @ApiOperation(value="核销列表", notes = "核销列表")
    public Data<PageBean<ActivityVerifyInResponseDTO>> listPost(@RequestBody @Valid VerifyPageQuery query) {
        return new Data<>(activityVerifyService.findPage(query));
    }
    @PostMapping("/orderList")
    @ApiOperation(value="订单核销列表")
    public Data<PageBean<VerifyOrderResponseDTO>> orderList(@RequestBody @Valid VerifyOrderPageQuery query) {
        return new Data<>(activityVerifyService.orderList(query));
    }

    @GetMapping("/orderDetail")
    @ApiOperation(value="订单核销详情")
    public Data<OrderDetailResponseDTO> orderDetail(@RequestParam String code) {
        return new Data<>(activityVerifyService.orderDetail(code));
    }
    @GetMapping("/detail")
    @ApiOperation(value="核销详情", notes = "核销详情")
    public Data<ActivityVerifyInResponseDTO> detail(@RequestParam Long id) {
        return new Data<>(activityVerifyService.getDetail(id));
    }

    @GetMapping("/rejectLogList")
    @ApiOperation(value="驳回列表", notes = "驳回列表")
    public Data<PageBean<ActivityVerifyRejectLogResponseDTO>> rejectLogList(@Valid ActivityVerifyRejectLogQuery query) {
        return new Data<>(activityVerifyRejectLogService.findPage(query));
    }

    @PostMapping("/rejectLogListPost")
    @ApiOperation(value="驳回列表", notes = "驳回列表")
    public Data<PageBean<ActivityVerifyRejectLogResponseDTO>> rejectLogListPost(@RequestBody @Valid ActivityVerifyRejectLogQuery query) {
        return new Data<>(activityVerifyRejectLogService.findPage(query));
    }

    @ApiOperation(value = "核销")
    @PutMapping("/wiped")
    public Data<Boolean> wiped(@RequestBody @Valid ActivityVerifyRequestDTO requestDTO) {
        return new Data<>(activityVerifyService.wiped(requestDTO));
    }


    @ApiOperation(value = "驳回")
    @PutMapping("/reject")
    public Data<Boolean> reject(@RequestBody @Valid ActivityVerifyRequestDTO requestDTO) {
        return new Data<>(activityVerifyService.reject(requestDTO));
    }

    @ApiOperation(value = "退款")
    @PutMapping("/refunds")
    public Data<Boolean> refunds(@RequestBody @Valid ActivityVerifyRequestDTO requestDTO) {//reason 退款原因
        return new Data<>(activityVerifyService.refunds(requestDTO));
    }

    @GetMapping("/myList")
    @ApiOperation(value="个人核销列表", notes = "个人核销列表")
    public Data<PageBean<ActivityVerifyInResponseDTO>> myList(@Valid MyVerifyQuery query) {
        return new Data<>(activityVerifyService.myList(query));
    }

    @PostMapping("/myListPost")
    @ApiOperation(value="个人核销列表", notes = "个人核销列表")
    public Data<PageBean<ActivityVerifyInResponseDTO>> myListPost(@RequestBody @Valid MyVerifyQuery query) {
        return new Data<>(activityVerifyService.myList(query));
    }

    @GetMapping("/getQrCode")
    @ApiOperation(value="核销二维码", notes = "核销二维码")
    public Data<String> getQrCode(@RequestParam String code) {
        return new Data<>(activityVerifyService.getQrCode(code));
    }

    @GetMapping("/sendIncentiveManual")
    @ApiOperation(value="手动发送红包激励", notes = "手动发送红包激励")
    public Data<Boolean> sendIncentiveManual(@RequestParam Long verifyId) {
        return new Data<>(activityVerifyService.sendIncentiveManual(verifyId));
    }

    @ApiOperation(value="导出核销数据", notes = "导出核销数据")
    @PostMapping("/getExportVerifyExcelData")
    public Data<Object> getExportVerifyExcelData(@RequestBody VerifyPageQuery query){
        return new Data<>(activityVerifyService.getExportVerifyExcelData(query));
    }


    @ApiOperation(value="导出核销记录", notes = "导出核销记录")
    @GetMapping("/exportVerifyExcel")
    public Data<Object> exportVerifyExcel(HttpServletResponse response, VerifyPageQuery query) {
        ExcelExportActPartakeLogUtil util = ExcelExportActPartakeLogUtil.getInstance();
        Data<Object> returnInfo = this.getExportVerifyExcelData(query);//activityVerifyService.getExportVerifyExcelData(query);

        if(!CommonExceptionCode.SUCCESS.equals(returnInfo.getCode())){
            return returnInfo;
        }

        if (returnInfo.getdata() != null) {

            ExcelExportActVerifyResponseDTO exportActVerifyResponseDTO = (ExcelExportActVerifyResponseDTO)returnInfo.getdata();

            try {
                util.exportExcel(response, exportActVerifyResponseDTO.getTitle(),
                        exportActVerifyResponseDTO.getFileName(),
                        exportActVerifyResponseDTO.getHeaderKey(),
                        exportActVerifyResponseDTO.getHeardList(),
                        exportActVerifyResponseDTO.getDataList(),
                        exportActVerifyResponseDTO.getSheetName());
            } catch (IOException e) {
                e.printStackTrace();
                return new Data<>(Boolean.FALSE);
            }
        }
        return new Data<>(Boolean.TRUE);
    }

}
