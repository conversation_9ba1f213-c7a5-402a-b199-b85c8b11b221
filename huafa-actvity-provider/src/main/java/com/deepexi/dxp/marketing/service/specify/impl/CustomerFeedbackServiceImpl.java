package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CustomerFeedbackQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.CustomerFeedbackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.FeedbackInfoVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.FormInfoVO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityFormFeedbackResponseVO;
import com.deepexi.dxp.marketing.domain.marketing.response.ExcelExportActPartakeLogResponseDTO;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.FeedbackActivityTypeEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.specify.DynamicFormEnum;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.ActivityAnalysisService;
import com.deepexi.dxp.marketing.service.specify.CustomerFeedbackService;
import com.deepexi.dxp.marketing.service.specify.MiniProgramService;
import com.deepexi.dxp.marketing.service.specify.PhoneService;
import com.deepexi.dxp.marketing.utils.ExcelExportActPartakeLogUtil;
import com.deepexi.dxp.marketing.utils.PhoneEncryUtils;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityVerifyDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.CustomerFeedbackDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFormFeedbackDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityVerifyDO;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.exception.CommonExceptionCode;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CustomerFeedbackServiceImpl implements CustomerFeedbackService {

    @Resource
    public CustomerFeedbackDAO customerFeedbackDAO;
    @Resource
    public PhoneService phoneService;

    @Autowired
    private PromotionActivityManager promotionActivityManager;

    @Resource
    private PromotionActivityDAO promotionActivityDAO;

    @Resource
    private MiniProgramService miniProgramService;

    @Resource
    private ActivityAnalysisService activityAnalysisService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ActivityVerifyDAO activityVerifyDAO;

    @Override
    public Boolean save(CustomerFeedbackRequestDTO requestDTO) {
        if (Objects.isNull(requestDTO)) {
            throw new ApplicationException("入参对象不能为空");
        }
        Map<String, Object> limits = requestDTO.getLimits();

        /*boolean flag = requestDTO.getPaTemplateId() != null && StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(requestDTO.getPaTemplateId().toString()) && requestDTO.getId() != null;//true 秒杀修改 不用进行短信验证码较验
        //校验库存是否足够
        if(flag){
            Long currentCount = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT+requestDTO.getResourceId()).get();
            if (currentCount <= 0){
                throw new ApplicationException("资源库存不足");
            }
        }*/
        if (StrUtil.isBlank(requestDTO.getPhone())) {
            requestDTO.setPhone((String)limits.get("phone"));
            log.warn("没有授权手机号码，使用登记号码先。" + requestDTO.getPhone());
        }
        if(requestDTO.getActivityType().equals(FeedbackActivityTypeEnum.OTHER.getId())
                /*&& !flag*/){
            Object phone = limits.get("phone");
            if (Objects.isNull(phone)){
                throw new ApplicationException("手机号不能为空");
            }
            if(!phoneService.checkSmsCode(String.valueOf(phone),requestDTO.getSmsCode())){
                throw new ApplicationException("短信验证码不正确");
            }
        }
        if (requestDTO.getId() == null) {

            if (getActivityFormFeedbackPageBean(requestDTO)) {
                return true;
            }

            return saveActivityFormFeedback(requestDTO);
        }else{
            if (requestDTO.getResourceId() != null && !getActivityFormFeedbackPageBean(requestDTO)) {
                return saveActivityFormFeedback(requestDTO);
            }
            ActivityFormFeedbackDO formFeedbackOldDO = customerFeedbackDAO.getById(requestDTO.getId());
            if(formFeedbackOldDO ==  null || !formFeedbackOldDO.getActivityId().equals(requestDTO.getActivityId())
                    || !formFeedbackOldDO.getPhone().equals(requestDTO.getPhone()) || !formFeedbackOldDO.getActivityType().equals(requestDTO.getActivityType())){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"所更新信息有数据库信息不一致!");
            }
            formFeedbackOldDO.setLimits(limits);
            if(StrUtil.isBlank(formFeedbackOldDO.getProjectId()) && StrUtil.isNotBlank(requestDTO.getProjectId())){
                formFeedbackOldDO.setProjectId(requestDTO.getProjectId());
                formFeedbackOldDO.setProjectName(requestDTO.getProjectName());
            }
            return customerFeedbackDAO.updateById(formFeedbackOldDO);
        }
    }

    private boolean saveActivityFormFeedback(CustomerFeedbackRequestDTO requestDTO) {
        ActivityFormFeedbackDO activityFormFeedbackDO = requestDTO.clone(ActivityFormFeedbackDO.class);
        activityFormFeedbackDO.setAppId(AppRuntimeEnv.getAppId());
        activityFormFeedbackDO.setTenantId(AppRuntimeEnv.getTenantId());
        boolean flag = customerFeedbackDAO.save(activityFormFeedbackDO);
        if(flag){
            promotionActivityManager.incrUserFeedBackCount(requestDTO.getPhone(), requestDTO.getActivityId(), requestDTO.getResourceId());
            //潜客池推送信息
            miniProgramService.adoptUserLog(activityFormFeedbackDO,null,null);
        }
        return flag;
    }

    private Boolean getActivityFormFeedbackPageBean(CustomerFeedbackRequestDTO requestDTO) {
        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
        query.setPhone(requestDTO.getPhone());
        query.setActivityId(requestDTO.getActivityId());
        query.setResourceId(requestDTO.getResourceId());
        query.setPage(1L);
        query.setSize(1L);
        PageBean<ActivityFormFeedbackDO> activityFormFeedbackPage = customerFeedbackDAO.pageList(query);
        return activityFormFeedbackPage != null && CollectionUtil.isNotEmpty(activityFormFeedbackPage.getContent());
    }

    @Override
    public PageBean<ActivityFormFeedbackResponseVO> pageList(CustomerFeedbackQuery query) {
        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(query.getActivityId());
        if(Objects.nonNull(promotionActivityDO) && StrategyGroupEnum.HF_FORM_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())){
            query.setActivityType(0);//表单类型
        }
        PageBean<ActivityFormFeedbackDO> activityFormFeedbackPage = customerFeedbackDAO.pageList(query);

        //如果返回的数据为空
        if (Objects.isNull(activityFormFeedbackPage)) {
            return new PageBean<>(Lists.newArrayList());
        }
        return this.getActivityFormFeedbackResponsePage(activityFormFeedbackPage,query.getActivityId());
    }

    private PageBean<ActivityFormFeedbackResponseVO> getActivityFormFeedbackResponsePage(PageBean<ActivityFormFeedbackDO> activityFormFeedbackPage, Long activityId) {
        if (activityId == null){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"活动id不能为空!");
        }
        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(activityId);
        PageBean<ActivityFormFeedbackResponseVO> promotionActivityResponsePageBean = ObjectCloneUtils.convertPageBean(activityFormFeedbackPage, ActivityFormFeedbackResponseVO.class);
        List<ActivityFormFeedbackDO> content = activityFormFeedbackPage.getContent();
        if (CollectionUtil.isNotEmpty(content)){
//            Map<String, ActivityAnalysisResponseDTO> analysisMap = Maps.newHashMap();

            //查询个人码信息
//            List<String> phoneList = content.stream().map(ActivityFormFeedbackDO::getPhone).collect(Collectors.toList());
//            if (CollectionUtil.isNotEmpty(phoneList)){
//                List<ActivityAnalysisResponseDTO> activityAnalysisResponseList = activityAnalysisService.posterChannelStatistics(activityId, phoneList);
//                if (CollectionUtil.isNotEmpty(activityAnalysisResponseList)){
//                    analysisMap = activityAnalysisResponseList.stream().collect(Collectors.toMap(ActivityAnalysisResponseDTO::getPhone, v -> v));
//                }
//            }

//            Map<String, ActivityAnalysisResponseDTO> finalAnalysisMap = analysisMap;
            List<ActivityFormFeedbackResponseVO> collect = content.stream().map(back -> {
                ActivityFormFeedbackResponseVO clone = back.clone(ActivityFormFeedbackResponseVO.class);
                clone.setFeedbackVO(JSON.parseObject(JSON.toJSONString(back.getLimits()), FeedbackInfoVO.class));
                //表单活动解析
                if(StrategyGroupEnum.HF_FORM_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())){
                    clone.setLimits(getFormLimits(back.getLimits(),promotionActivityDO.getExt()));
                }
                clone.setFullPhone(clone.getPhone());
                clone.setPhone(PhoneEncryUtils.encode(clone.getPhone()));

                //添加个人码信息
//                ActivityAnalysisResponseDTO activityAnalysisResponseDTO = finalAnalysisMap.get(back.getPhone());
//                if (activityAnalysisResponseDTO != null){
//                    clone.setAccessPeopleNumber(activityAnalysisResponseDTO.getAccessPeopleNumber());
//                    clone.setJoinPeopleNumber(activityAnalysisResponseDTO.getJoinPeopleNumber());
//                    clone.setLzPeopleNumber(activityAnalysisResponseDTO.getLzPeopleNumber());
//                }
                return clone;
            }).collect(Collectors.toList());

            promotionActivityResponsePageBean.setContent(collect);
        }
        return promotionActivityResponsePageBean;
    }

    @Override
    public PageBean<ActivityFormFeedbackResponseVO> pageHisList(CustomerFeedbackQuery query) {

        PageBean<ActivityFormFeedbackDO> activityFormFeedbackPage = customerFeedbackDAO.pageList(query);

        if (CollectionUtil.isEmpty(activityFormFeedbackPage.getContent())){
            query.setResourceId(null);
            query.setPage(1);
            query.setSize(1);
            activityFormFeedbackPage = customerFeedbackDAO.pageList(query);
        }

        if (CollectionUtil.isEmpty(activityFormFeedbackPage.getContent())){
            return new PageBean<>();
        }
        return this.getActivityFormFeedbackResponsePage(activityFormFeedbackPage,query.getActivityId());
    }

    public Map<String,Object> getFormLimits(Map<String,Object> limits,Map<String, Object> ext){
        Map<String,Object> map = Maps.newHashMap();
        List<FormInfoVO> list = Lists.newArrayList();
        JSONArray jsonArrayData = (JSONArray) limits.get("dynamicForm");//填写内容
        JSONArray jsonArrayForm = (JSONArray) ext.get("dynamicForm");//后台配置
        Map<Integer, String> dataMap = jsonArrayData.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        object -> {
                            JSONObject item = (JSONObject) object;
                            return item.getInteger("formId");
                        },
                        object -> {
                            JSONObject item = (JSONObject) object;
                            return item.getString("inputValue");
                        }
                ));
        for (int i = 0; i < jsonArrayForm.size(); i++) {
            JSONObject formObject = jsonArrayForm.getJSONObject(i);//填写的表单
            String inputValue = dataMap.get(formObject.getInteger("formId"));
            String type = formObject.getString("type");
            FormInfoVO vo = new FormInfoVO();
            vo.setValue(formObject.getString("title"));
            String val = analysisJsonValue(formObject, inputValue);
            if(DynamicFormEnum.INPUT.getId().equals(type) || DynamicFormEnum.TEXTAREA.getId().equals(type)){
                val = PhoneEncryUtils.strDesens(val);//对单行文本或多行文本中的手机号进行脱敏
            }
            vo.setInputValue(val);
            vo.setType(type);
            vo.setIndex(formObject.getInteger("formId"));
            list.add(vo);
        }
        map.put("dynamicForm", list);
        return map;
    }


    private Map<String,Object> getFormLimitsExport(Map<String,Object> limits,Map<String, Object> ext){
        Map<String,Object> map = Maps.newHashMap();
        List<FormInfoVO> list = Lists.newArrayList();
        JSONArray jsonArrayData = (JSONArray) limits.get("dynamicForm");//填写内容
        JSONArray jsonArrayForm = (JSONArray) ext.get("dynamicForm");//后台配置
        Map<Integer, String> dataMap = jsonArrayData.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        object -> {
                            JSONObject item = (JSONObject) object;
                            return item.getInteger("formId");
                        },
                        object -> {
                            JSONObject item = (JSONObject) object;
                            return item.getString("inputValue");
                        }
                ));
        for (int i = 0; i < jsonArrayForm.size(); i++) {
            JSONObject formObject = jsonArrayForm.getJSONObject(i);//填写的表单
            String inputValue = dataMap.get(formObject.getInteger("formId"));
            String type = formObject.getString("type");
            FormInfoVO vo = new FormInfoVO();
            vo.setValue(formObject.getString("title"));
            String val = analysisJsonValue(formObject, inputValue);
            /*if(DynamicFormEnum.INPUT.getId().equals(type) || DynamicFormEnum.TEXTAREA.getId().equals(type)){
                val = PhoneEncryUtils.strDesens(val);//对单行文本或多行文本中的手机号进行脱敏
            }*/
            vo.setInputValue(val);
            vo.setType(type);
            vo.setIndex(formObject.getInteger("formId"));
            list.add(vo);
        }
        map.put("dynamicForm", list);
        return map;
    }

    /**
     * 获取表单配置的值
     * @param item
     * @param entryValues
     * @return
     */
    public static String analysisJsonValue(JSONObject item, Object entryValues) {
        StringBuilder value = new StringBuilder();
        if(Objects.isNull(entryValues) || StringUtil.isEmpty(entryValues.toString())){
            return value.toString();
        }
        switch (item.getString("type")) {
            case "radio"://单选
                JSONArray options = item.getJSONArray("options");
                if(Objects.nonNull(options) && options.size() > 0){
                    for(int i=0;i<options.size();i++){
                        JSONObject jsonObject = (JSONObject)options.get(i);
                        if(jsonObject.get("value").toString().equals(entryValues)){
                            value = new StringBuilder((String) jsonObject.get("label"));
                        }
                    }
                }
                break;
            case "check"://复选框
                JSONArray checkOptions = item.getJSONArray("options");
                if(Objects.nonNull(checkOptions) && checkOptions.size() > 0 && entryValues != null){
                    List<Object> Strings = JSONArray.parseArray(entryValues.toString(), Object.class);
                    if (CollectionUtil.isNotEmpty(Strings) && Strings.size() > 0) {
                        int j = 0;
                        for(int i=0;i<checkOptions.size();i++){
                            JSONObject jsonObject = (JSONObject)checkOptions.get(i);
                            if(Strings.contains(jsonObject.get("value").toString())){
                                if(j > 0){
                                    value.append(",");
                                }
                                value.append((String) jsonObject.get("label"));
                                j++;
                            }
                        }
                    }
                }
                break;
            case "select"://下拉
                JSONArray selectOptions = item.getJSONArray("options");
                if(Objects.nonNull(selectOptions) && selectOptions.size() > 0){
                    for (int i = 0; i < selectOptions.size(); i++) {
                        JSONObject jsonObject = (JSONObject) selectOptions.get(i);
                        if (jsonObject.get("value").toString().equals(entryValues)) {
                            value = new StringBuilder((String) jsonObject.get("label"));
                        }
                    }
                }
                break;
            case "timers"://时段
                JSONArray pickerOptions = item.getJSONArray("pickerOptions");
                if(Objects.nonNull(pickerOptions) && pickerOptions.size() > 0){
                    for (int i = 0; i < pickerOptions.size(); i++) {
                        JSONObject jsonObject = (JSONObject) pickerOptions.get(i);
                        if (jsonObject.get("time1").equals(entryValues)) {
                            value = new StringBuilder((String) jsonObject.get("time1"));
                        }
                    }
                }
                break;
            case "image":
                List<Object> urls = JSONArray.parseArray(entryValues.toString(), Object.class);
                for(int i=0;i<urls.size();i++){
                    if (i > 0) {
                        value.append(",");
                    }
                    value.append((String) urls.get(i));
                }
                break;
            default://local 位置、日期、单行、多行
                value = new StringBuilder(entryValues.toString());
                break;
        }
        return value.toString();
    }

    @Override
    public ActivityFormFeedbackResponseVO detail(Long id) {
        ActivityFormFeedbackDO byId = customerFeedbackDAO.getActivityFormFeedbackById(id);
        ActivityFormFeedbackResponseVO clone = byId.clone(ActivityFormFeedbackResponseVO.class);
        clone.setFeedbackVO(JSON.parseObject(JSON.toJSONString(byId.getLimits()),FeedbackInfoVO.class));
        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(byId.getActivityId());
        if(ObjectUtil.equal(promotionActivityDO.getPaTemplateId().toString(),StrategyGroupEnum.HF_FORM_ACT.getId())){
            clone.setLimits(this.getFormLimits(byId.getLimits(),promotionActivityDO.getExt()));//表单内容
        }
        return clone;
    }

    @Override
    public ExcelExportActPartakeLogResponseDTO exportFormFeedbackExcel(CustomerFeedbackQuery query) {
        List<ActivityFormFeedbackDO> list = customerFeedbackDAO.findList(query);
        ExcelExportActPartakeLogUtil util = ExcelExportActPartakeLogUtil.getInstance();
        List<Map<String, String>> dataList = new ArrayList<>();
        String fileName = "表单活动反馈列表";

        String[] heardList1 = new String[]{"序号","昵称","手机号","提交时间"};
        String[] headerKey1 = new String[]{"","nickName","phone","createdTime"};
        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(query.getActivityId());
        Map<String, String[]> heardMap = getHeardMap(heardList1, headerKey1, promotionActivityDO);
        String[] heardList = heardMap.get("heardList");
        String[] headerKey = heardMap.get("headerKey");
        try{
            //重新生成headList headerkye feedbackInfo需
            //如果返回的数据为空
            if (CollectionUtil.isNotEmpty(list)) {
                for (int i = 0; i < list.size(); i++) {
                    ActivityFormFeedbackDO activityFormFeedback = list.get(i);
                    ActivityFormFeedbackResponseVO clone = activityFormFeedback.clone(ActivityFormFeedbackResponseVO.class);
                    clone.setFeedbackVO(JSON.parseObject(JSON.toJSONString(list.get(i).getLimits()), FeedbackInfoVO.class));
                    //clone.setPhone(PhoneEncryUtils.encode(clone.getPhone()));
                    Map<String, String> data = Maps.newHashMap();
                    data.put(headerKey[0], (i+1) + "");
                    data.put(headerKey[1], clone.getNickName());
                    data.put(headerKey[2], clone.getPhone());
                    data.put(headerKey[3], DateUtils.format(clone.getCreatedTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
                    Map<String, Object> formMap= this.getFormLimitsExport(activityFormFeedback.getLimits(), promotionActivityDO.getExt());
                    List<FormInfoVO> formDataList = (List<FormInfoVO>)formMap.get("dynamicForm");
                    for (int k = 0; k < formDataList.size(); k++) {
                        FormInfoVO formInfoVO = formDataList.get(k);
                        data.put(headerKey[4+k],formInfoVO.getInputValue());
                    }
                    dataList.add(data);
                }
            }
            return ExcelExportActPartakeLogResponseDTO
                    .builder()
                    .title(fileName)
                    .fileName(fileName)
                    .headerKey(headerKey)
                    .heardList(heardList)
                    .dataList(dataList)
                    .build();
        }catch(Exception e){
        }
        return null;
    }

    @Override
    public ActivityFormFeedbackResponseVO getByUserId(Long activityId, String phone) {
        if(activityId == null || StringUtil.isEmpty(phone)){
            return null;
        }
        QueryWrapper<ActivityFormFeedbackDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityFormFeedbackDO::getActivityId,activityId);
        queryWrapper.lambda().eq(ActivityFormFeedbackDO::getPhone,phone);
        List<ActivityFormFeedbackDO> list = customerFeedbackDAO.list(queryWrapper);
        if(CollectionUtil.isNotEmpty(list)){
            ActivityFormFeedbackDO byId = customerFeedbackDAO.getActivityFormFeedbackById(list.get(0).getId());
            PromotionActivityDO activityDo = promotionActivityDAO.selectById(activityId);
            ActivityFormFeedbackResponseVO activityFormFeedbackResponseVO = byId.clone(ActivityFormFeedbackResponseVO.class);

            activityFormFeedbackResponseVO.setStartTime(activityDo.getStartTime());
            activityFormFeedbackResponseVO.setEndTime(activityDo.getEndTime());
            return activityFormFeedbackResponseVO;
        }
        return null;
    }

    @Override
    public String getCity() {
            String json = "[{\"label\":\"北京市\",\"children\":[{\"label\":\"北京市\",\"id\":\"1119019\",\"value\":\"110100\",\"signList\":[{\"name\":\"华发中央公园\"}]}],\"id\":\"1119019\",\"value\":\"110000\"},{\"label\":\"天津市\",\"children\":[{\"label\":\"天津市\",\"id\":\"1119021\",\"value\":\"120100\",\"signList\":[{\"name\":\"华发未来荟\"}]}],\"id\":\"1119021\",\"value\":\"120000\"},{\"label\":\"河北省\",\"children\":[{\"label\":\"石家庄市\",\"value\":\"130100\"},{\"label\":\"唐山市\",\"value\":\"130200\"},{\"label\":\"秦皇岛市\",\"value\":\"130300\"},{\"label\":\"邯郸市\",\"value\":\"130400\"},{\"label\":\"邢台市\",\"value\":\"130500\"},{\"label\":\"保定市\",\"value\":\"130600\"},{\"label\":\"张家口市\",\"value\":\"130700\"},{\"label\":\"承德市\",\"value\":\"130800\"},{\"label\":\"沧州市\",\"value\":\"130900\"},{\"label\":\"廊坊市\",\"value\":\"131000\"},{\"label\":\"衡水市\",\"value\":\"131100\"}],\"value\":\"130000\"},{\"label\":\"山西省\",\"children\":[{\"label\":\"太原市\",\"value\":\"140100\"},{\"label\":\"大同市\",\"value\":\"140200\"},{\"label\":\"阳泉市\",\"value\":\"140300\"},{\"label\":\"长治市\",\"value\":\"140400\"},{\"label\":\"晋城市\",\"value\":\"140500\"},{\"label\":\"朔州市\",\"value\":\"140600\"},{\"label\":\"晋中市\",\"value\":\"140700\"},{\"label\":\"运城市\",\"value\":\"140800\"},{\"label\":\"忻州市\",\"value\":\"140900\"},{\"label\":\"临汾市\",\"value\":\"141000\"},{\"label\":\"吕梁市\",\"value\":\"141100\"}],\"value\":\"140000\"},{\"label\":\"内蒙古自治区\",\"children\":[{\"label\":\"呼和浩特市\",\"value\":\"150100\"},{\"label\":\"包头市\",\"id\":\"1119012\",\"value\":\"150200\",\"signList\":[{\"name\":\"华发新天地\"}]},{\"label\":\"乌海市\",\"value\":\"150300\"},{\"label\":\"赤峰市\",\"value\":\"150400\"},{\"label\":\"通辽市\",\"value\":\"150500\"},{\"label\":\"鄂尔多斯市\",\"value\":\"150600\"},{\"label\":\"呼伦贝尔市\",\"value\":\"150700\"},{\"label\":\"巴彦淖尔市\",\"value\":\"150800\"},{\"label\":\"乌兰察布市\",\"value\":\"150900\"},{\"label\":\"兴安盟\",\"value\":\"152200\"},{\"label\":\"锡林郭勒盟\",\"value\":\"152500\"},{\"label\":\"阿拉善盟\",\"value\":\"152900\"}],\"value\":\"150000\"},{\"label\":\"辽宁省\",\"children\":[{\"label\":\"沈阳市\",\"id\":\"1119009\",\"value\":\"210100\",\"signList\":[{\"name\":\"华发中东和平首府\"},{\"name\":\"华发全运首府\"}]},{\"label\":\"大连市\",\"id\":\"1119011\",\"value\":\"210200\",\"signList\":[{\"name\":\"华发绿洋湾\"},{\"name\":\"华发花间月\"}]},{\"label\":\"鞍山市\",\"value\":\"210300\"},{\"label\":\"抚顺市\",\"value\":\"210400\"},{\"label\":\"本溪市\",\"value\":\"210500\"},{\"label\":\"丹东市\",\"value\":\"210600\"},{\"label\":\"锦州市\",\"value\":\"210700\"},{\"label\":\"营口市\",\"value\":\"210800\"},{\"label\":\"阜新市\",\"value\":\"210900\"},{\"label\":\"辽阳市\",\"value\":\"211000\"},{\"label\":\"盘锦市\",\"value\":\"211100\"},{\"label\":\"铁岭市\",\"value\":\"211200\"},{\"label\":\"朝阳市\",\"value\":\"211300\"},{\"label\":\"葫芦岛市\",\"value\":\"211400\"}],\"value\":\"210000\"},{\"label\":\"吉林省\",\"children\":[{\"label\":\"长春市\",\"value\":\"220100\"},{\"label\":\"吉林市\",\"value\":\"220200\"},{\"label\":\"四平市\",\"value\":\"220300\"},{\"label\":\"辽源市\",\"value\":\"220400\"},{\"label\":\"通化市\",\"value\":\"220500\"},{\"label\":\"白山市\",\"value\":\"220600\"},{\"label\":\"松原市\",\"value\":\"220700\"},{\"label\":\"白城市\",\"value\":\"220800\"},{\"label\":\"延边朝鲜族自治州\",\"value\":\"222400\"}],\"value\":\"220000\"},{\"label\":\"黑龙江省\",\"children\":[{\"label\":\"哈尔滨市\",\"value\":\"230100\"},{\"label\":\"齐齐哈尔市\",\"value\":\"230200\"},{\"label\":\"鸡西市\",\"value\":\"230300\"},{\"label\":\"鹤岗市\",\"value\":\"230400\"},{\"label\":\"双鸭山市\",\"value\":\"230500\"},{\"label\":\"大庆市\",\"value\":\"230600\"},{\"label\":\"伊春市\",\"value\":\"230700\"},{\"label\":\"佳木斯市\",\"value\":\"230800\"},{\"label\":\"七台河市\",\"value\":\"230900\"},{\"label\":\"牡丹江市\",\"value\":\"231000\"},{\"label\":\"黑河市\",\"value\":\"231100\"},{\"label\":\"绥化市\",\"value\":\"231200\"},{\"label\":\"大兴安岭地区\",\"value\":\"232700\"}],\"value\":\"230000\"},{\"label\":\"上海市\",\"children\":[{\"label\":\"上海市\",\"id\":\"1119006\",\"value\":\"310100\",\"signList\":[{\"name\":\"华发四季半岛\"}]}],\"id\":\"1119006\",\"value\":\"310000\"},{\"label\":\"江苏省\",\"children\":[{\"label\":\"南京市\",\"id\":\"1119014\",\"value\":\"320100\",\"signList\":[{\"name\":\"紫麒府\"},{\"name\":\"万象天地\"}]},{\"label\":\"无锡市\",\"id\":\"1119013\",\"value\":\"320200\",\"signList\":[{\"name\":\"华发首府\"},{\"name\":\"华发中央首府\"}]},{\"label\":\"徐州市\",\"value\":\"320300\"},{\"label\":\"常州市\",\"value\":\"320400\"},{\"label\":\"苏州市\",\"id\":\"1119007\",\"value\":\"320500\",\"signList\":[{\"name\":\"华发未来城\"},{\"name\":\"华发星辰海\"}]},{\"label\":\"南通市\",\"value\":\"320600\"},{\"label\":\"连云港市\",\"value\":\"320700\"},{\"label\":\"淮安市\",\"value\":\"320800\"},{\"label\":\"盐城市\",\"value\":\"320900\"},{\"label\":\"扬州市\",\"value\":\"321000\"},{\"label\":\"镇江市\",\"value\":\"321100\"},{\"label\":\"泰州市\",\"value\":\"321200\"},{\"label\":\"宿迁市\",\"value\":\"321300\"}],\"value\":\"320000\"},{\"label\":\"浙江省\",\"children\":[{\"label\":\"杭州市\",\"id\":\"1119016\",\"value\":\"330100\",\"signList\":[{\"name\":\"华发峰荟|荟天府\"}]},{\"label\":\"宁波市\",\"value\":\"330200\"},{\"label\":\"温州市\",\"value\":\"330300\"},{\"label\":\"嘉兴市\",\"value\":\"330400\"},{\"label\":\"湖州市\",\"value\":\"330500\"},{\"label\":\"绍兴市\",\"id\":\"1119022\",\"value\":\"330600\",\"signList\":[{\"name\":\"华发金融活力城\"}]},{\"label\":\"金华市\",\"value\":\"330700\"},{\"label\":\"衢州市\",\"value\":\"330800\"},{\"label\":\"舟山市\",\"value\":\"330900\"},{\"label\":\"台州市\",\"value\":\"331000\"},{\"label\":\"丽水市\",\"value\":\"331100\"}],\"value\":\"330000\"},{\"label\":\"安徽省\",\"children\":[{\"label\":\"合肥市\",\"value\":\"340100\"},{\"label\":\"芜湖市\",\"value\":\"340200\"},{\"label\":\"蚌埠市\",\"value\":\"340300\"},{\"label\":\"淮南市\",\"value\":\"340400\"},{\"label\":\"马鞍山市\",\"value\":\"340500\"},{\"label\":\"淮北市\",\"value\":\"340600\"},{\"label\":\"铜陵市\",\"value\":\"340700\"},{\"label\":\"安庆市\",\"value\":\"340800\"},{\"label\":\"黄山市\",\"value\":\"341000\"},{\"label\":\"滁州市\",\"value\":\"341100\"},{\"label\":\"阜阳市\",\"value\":\"341200\"},{\"label\":\"宿州市\",\"value\":\"341300\"},{\"label\":\"六安市\",\"value\":\"341500\"},{\"label\":\"亳州市\",\"value\":\"341600\"},{\"label\":\"池州市\",\"value\":\"341700\"},{\"label\":\"宣城市\",\"value\":\"341800\"}],\"value\":\"340000\"},{\"label\":\"福建省\",\"children\":[{\"label\":\"福州市\",\"value\":\"350100\"},{\"label\":\"厦门市\",\"value\":\"350200\"},{\"label\":\"莆田市\",\"value\":\"350300\"},{\"label\":\"三明市\",\"value\":\"350400\"},{\"label\":\"泉州市\",\"value\":\"350500\"},{\"label\":\"漳州市\",\"value\":\"350600\"},{\"label\":\"南平市\",\"value\":\"350700\"},{\"label\":\"龙岩市\",\"value\":\"350800\"},{\"label\":\"宁德市\",\"value\":\"350900\"}],\"value\":\"350000\"},{\"label\":\"江西省\",\"children\":[{\"label\":\"南昌市\",\"value\":\"360100\"},{\"label\":\"景德镇市\",\"value\":\"360200\"},{\"label\":\"萍乡市\",\"value\":\"360300\"},{\"label\":\"九江市\",\"value\":\"360400\"},{\"label\":\"新余市\",\"value\":\"360500\"},{\"label\":\"鹰潭市\",\"value\":\"360600\"},{\"label\":\"赣州市\",\"value\":\"360700\"},{\"label\":\"吉安市\",\"value\":\"360800\"},{\"label\":\"宜春市\",\"value\":\"360900\"},{\"label\":\"抚州市\",\"value\":\"361000\"},{\"label\":\"上饶市\",\"value\":\"361100\"}],\"value\":\"360000\"},{\"label\":\"山东省\",\"children\":[{\"label\":\"济南市\",\"value\":\"370100\"},{\"label\":\"青岛市\",\"id\":\"1119015\",\"value\":\"370200\",\"signList\":[{\"name\":\"华发四季\"},{\"name\":\"华发紫金峰景\"}]},{\"label\":\"淄博市\",\"value\":\"370300\"},{\"label\":\"枣庄市\",\"value\":\"370400\"},{\"label\":\"东营市\",\"value\":\"370500\"},{\"label\":\"烟台市\",\"id\":\"1119026\",\"value\":\"370600\",\"signList\":[{\"name\":\"华发观山水\"}]},{\"label\":\"潍坊市\",\"value\":\"370700\"},{\"label\":\"济宁市\",\"value\":\"370800\"},{\"label\":\"泰安市\",\"value\":\"370900\"},{\"label\":\"威海市\",\"id\":\"1119008\",\"value\":\"371000\",\"signList\":[{\"name\":\"华发九龙湾\"},{\"name\":\"华发樱花湖\"}]},{\"label\":\"日照市\",\"value\":\"371100\"},{\"label\":\"临沂市\",\"value\":\"371300\"},{\"label\":\"德州市\",\"value\":\"371400\"},{\"label\":\"聊城市\",\"value\":\"371500\"},{\"label\":\"滨州市\",\"value\":\"371600\"},{\"label\":\"菏泽市\",\"value\":\"371700\"}],\"value\":\"370000\"},{\"label\":\"河南省\",\"children\":[{\"label\":\"郑州市\",\"id\":\"1119027\",\"value\":\"410100\",\"signList\":[{\"name\":\"华发峰景湾\"}]},{\"label\":\"开封市\",\"value\":\"410200\"},{\"label\":\"洛阳市\",\"value\":\"410300\"},{\"label\":\"平顶山市\",\"value\":\"410400\"},{\"label\":\"安阳市\",\"value\":\"410500\"},{\"label\":\"鹤壁市\",\"value\":\"410600\"},{\"label\":\"新乡市\",\"value\":\"410700\"},{\"label\":\"焦作市\",\"value\":\"410800\"},{\"label\":\"濮阳市\",\"value\":\"410900\"},{\"label\":\"许昌市\",\"value\":\"411000\"},{\"label\":\"漯河市\",\"value\":\"411100\"},{\"label\":\"三门峡市\",\"value\":\"411200\"},{\"label\":\"南阳市\",\"value\":\"411300\"},{\"label\":\"商丘市\",\"value\":\"411400\"},{\"label\":\"信阳市\",\"value\":\"411500\"},{\"label\":\"周口市\",\"value\":\"411600\"},{\"label\":\"驻马店市\",\"value\":\"411700\"},{\"label\":\"省直辖县级行政区划\",\"value\":\"419000\"}],\"value\":\"410000\"},{\"label\":\"湖北省\",\"children\":[{\"label\":\"武汉市\",\"id\":\"1119004\",\"value\":\"420100\",\"signList\":[{\"name\":\"华发中城荟中央首府\"},{\"name\":\"华发公园首府\"},{\"name\":\"华发四季\"},{\"name\":\"华发都荟天地\"}]},{\"label\":\"黄石市\",\"value\":\"420200\"},{\"label\":\"十堰市\",\"value\":\"420300\"},{\"label\":\"宜昌市\",\"id\":\"1119034\",\"value\":\"420500\"},{\"label\":\"襄阳市\",\"value\":\"420600\"},{\"label\":\"鄂州市\",\"id\":\"1119020\",\"value\":\"420700\"},{\"label\":\"荆门市\",\"value\":\"420800\"},{\"label\":\"孝感市\",\"value\":\"420900\"},{\"label\":\"荆州市\",\"value\":\"421000\"},{\"label\":\"黄冈市\",\"value\":\"421100\"},{\"label\":\"咸宁市\",\"value\":\"421200\"},{\"label\":\"随州市\",\"value\":\"421300\"},{\"label\":\"恩施土家族苗族自治州\",\"value\":\"422800\"},{\"label\":\"省直辖县级行政区划\",\"value\":\"429000\"}],\"value\":\"420000\"},{\"label\":\"湖南省\",\"children\":[{\"label\":\"长沙市\",\"id\":\"1119032\",\"value\":\"430100\",\"signList\":[{\"name\":\"滨江府1913\"}]},{\"label\":\"株洲市\",\"value\":\"430200\"},{\"label\":\"湘潭市\",\"value\":\"430300\"},{\"label\":\"衡阳市\",\"value\":\"430400\"},{\"label\":\"邵阳市\",\"value\":\"430500\"},{\"label\":\"岳阳市\",\"value\":\"430600\"},{\"label\":\"常德市\",\"value\":\"430700\"},{\"label\":\"张家界市\",\"value\":\"430800\"},{\"label\":\"益阳市\",\"value\":\"430900\"},{\"label\":\"郴州市\",\"value\":\"431000\"},{\"label\":\"永州市\",\"value\":\"431100\"},{\"label\":\"怀化市\",\"value\":\"431200\"},{\"label\":\"娄底市\",\"value\":\"431300\"},{\"label\":\"湘西土家族苗族自治州\",\"value\":\"433100\"}],\"value\":\"430000\"},{\"label\":\"广东省\",\"children\":[{\"label\":\"广州市\",\"id\":\"1119003\",\"value\":\"440100\"},{\"label\":\"韶关市\",\"value\":\"440200\"},{\"label\":\"深圳市\",\"id\":\"1119031\",\"value\":\"440300\"},{\"label\":\"珠海市\",\"id\":\"1119001\",\"value\":\"440400\",\"signList\":[{\"name\":\"华发琴澳新城\"},{\"name\":\"华发又一城\"},{\"name\":\"华发碧桂园滨海半岛\"},{\"name\":\"华发天茂国际半岛\"},{\"name\":\"华发悦谷\"},{\"name\":\"华发水岸华府\"},{\"name\":\"华发绿洋湾\"}]},{\"label\":\"汕头市\",\"value\":\"440500\"},{\"label\":\"佛山市\",\"value\":\"440600\"},{\"label\":\"江门市\",\"id\":\"1119017\",\"value\":\"440700\",\"signList\":[{\"name\":\"华发四季\"},{\"name\":\"华发水岸\"}]},{\"label\":\"湛江市\",\"id\":\"1119018\",\"value\":\"440800\"},{\"label\":\"茂名市\",\"value\":\"440900\"},{\"label\":\"肇庆市\",\"value\":\"441200\"},{\"label\":\"惠州市\",\"id\":\"1119030\",\"value\":\"441300\"},{\"label\":\"梅州市\",\"value\":\"441400\"},{\"label\":\"汕尾市\",\"id\":\"1119028\",\"value\":\"441500\",\"signList\":[{\"name\":\"华发清泉谷\"}]},{\"label\":\"河源市\",\"value\":\"441600\"},{\"label\":\"阳江市\",\"value\":\"441700\"},{\"label\":\"清远市\",\"value\":\"441800\"},{\"label\":\"东莞市\",\"value\":\"441900\"},{\"label\":\"中山市\",\"id\":\"1119002\",\"value\":\"442000\",\"signList\":[{\"name\":\"华发观山水\"},{\"name\":\"华发首府\"},{\"name\":\"华发四季\"},{\"name\":\"华发广场\"},{\"name\":\"华发广场-商都展厅\"}]},{\"label\":\"潮州市\",\"value\":\"445100\"},{\"label\":\"揭阳市\",\"value\":\"445200\"},{\"label\":\"云浮市\",\"value\":\"445300\"}],\"value\":\"440000\"},{\"label\":\"广西壮族自治区\",\"children\":[{\"label\":\"南宁市\",\"id\":\"1119005\",\"value\":\"450100\"},{\"label\":\"柳州市\",\"value\":\"450200\"},{\"label\":\"桂林市\",\"value\":\"450300\"},{\"label\":\"梧州市\",\"value\":\"450400\"},{\"label\":\"北海市\",\"value\":\"450500\"},{\"label\":\"防城港市\",\"value\":\"450600\"},{\"label\":\"钦州市\",\"value\":\"450700\"},{\"label\":\"贵港市\",\"value\":\"450800\"},{\"label\":\"玉林市\",\"value\":\"450900\"},{\"label\":\"百色市\",\"value\":\"451000\"},{\"label\":\"贺州市\",\"value\":\"451100\"},{\"label\":\"河池市\",\"value\":\"451200\"},{\"label\":\"来宾市\",\"value\":\"451300\"},{\"label\":\"崇左市\",\"value\":\"451400\"}],\"value\":\"450000\"},{\"label\":\"海南省\",\"children\":[{\"label\":\"海口市\",\"value\":\"460100\"},{\"label\":\"三亚市\",\"value\":\"460200\"},{\"label\":\"三沙市\",\"value\":\"460300\"},{\"label\":\"儋州市\",\"value\":\"460400\"},{\"label\":\"省直辖县级行政区划\",\"value\":\"469000\"}],\"value\":\"460000\"},{\"label\":\"重庆市\",\"children\":[{\"label\":\"重庆市\",\"id\":\"1119024\",\"value\":\"500100\",\"signList\":[{\"name\":\"华发四季半岛\"}]},{\"label\":\"县\",\"value\":\"500200\"}],\"id\":\"1119024\",\"value\":\"500000\"},{\"label\":\"四川省\",\"children\":[{\"label\":\"成都市\",\"id\":\"1119029\",\"value\":\"510100\"},{\"label\":\"自贡市\",\"value\":\"510300\"},{\"label\":\"攀枝花市\",\"value\":\"510400\"},{\"label\":\"泸州市\",\"value\":\"510500\"},{\"label\":\"德阳市\",\"value\":\"510600\"},{\"label\":\"绵阳市\",\"value\":\"510700\"},{\"label\":\"广元市\",\"value\":\"510800\"},{\"label\":\"遂宁市\",\"value\":\"510900\"},{\"label\":\"内江市\",\"value\":\"511000\"},{\"label\":\"乐山市\",\"value\":\"511100\"},{\"label\":\"南充市\",\"value\":\"511300\"},{\"label\":\"眉山市\",\"value\":\"511400\"},{\"label\":\"宜宾市\",\"value\":\"511500\"},{\"label\":\"广安市\",\"value\":\"511600\"},{\"label\":\"达州市\",\"value\":\"511700\"},{\"label\":\"雅安市\",\"value\":\"511800\"},{\"label\":\"巴中市\",\"value\":\"511900\"},{\"label\":\"资阳市\",\"value\":\"512000\"},{\"label\":\"阿坝藏族羌族自治州\",\"value\":\"513200\"},{\"label\":\"甘孜藏族自治州\",\"value\":\"513300\"},{\"label\":\"凉山彝族自治州\",\"value\":\"513400\"}],\"value\":\"510000\"},{\"label\":\"贵州省\",\"children\":[{\"label\":\"贵阳市\",\"value\":\"520100\"},{\"label\":\"六盘水市\",\"value\":\"520200\"},{\"label\":\"遵义市\",\"value\":\"520300\"},{\"label\":\"安顺市\",\"value\":\"520400\"},{\"label\":\"毕节市\",\"value\":\"520500\"},{\"label\":\"铜仁市\",\"value\":\"520600\"},{\"label\":\"黔西南布依族苗族自治州\",\"value\":\"522300\"},{\"label\":\"黔东南苗族侗族自治州\",\"value\":\"522600\"},{\"label\":\"黔南布依族苗族自治州\",\"value\":\"522700\"}],\"value\":\"520000\"},{\"label\":\"云南省\",\"children\":[{\"label\":\"昆明市\",\"value\":\"530100\"},{\"label\":\"曲靖市\",\"value\":\"530300\"},{\"label\":\"玉溪市\",\"value\":\"530400\"},{\"label\":\"保山市\",\"value\":\"530500\"},{\"label\":\"昭通市\",\"value\":\"530600\"},{\"label\":\"丽江市\",\"value\":\"530700\"},{\"label\":\"普洱市\",\"value\":\"530800\"},{\"label\":\"临沧市\",\"value\":\"530900\"},{\"label\":\"楚雄彝族自治州\",\"value\":\"532300\"},{\"label\":\"红河哈尼族彝族自治州\",\"value\":\"532500\"},{\"label\":\"文山壮族苗族自治州\",\"value\":\"532600\"},{\"label\":\"西双版纳傣族自治州\",\"value\":\"532800\"},{\"label\":\"大理白族自治州\",\"value\":\"532900\"},{\"label\":\"德宏傣族景颇族自治州\",\"value\":\"533100\"},{\"label\":\"怒江傈僳族自治州\",\"value\":\"533300\"},{\"label\":\"迪庆藏族自治州\",\"value\":\"533400\"}],\"value\":\"530000\"},{\"label\":\"西藏自治区\",\"children\":[{\"label\":\"拉萨市\",\"value\":\"540100\"},{\"label\":\"日喀则市\",\"value\":\"540200\"},{\"label\":\"昌都市\",\"value\":\"540300\"},{\"label\":\"林芝市\",\"value\":\"540400\"},{\"label\":\"山南市\",\"value\":\"540500\"},{\"label\":\"那曲市\",\"value\":\"540600\"},{\"label\":\"阿里地区\",\"value\":\"542500\"}],\"value\":\"540000\"},{\"label\":\"陕西省\",\"children\":[{\"label\":\"西安市\",\"id\":\"1119025\",\"value\":\"610100\",\"signList\":[{\"name\":\"CID中央首府\"}]},{\"label\":\"铜川市\",\"value\":\"610200\"},{\"label\":\"宝鸡市\",\"value\":\"610300\"},{\"label\":\"咸阳市\",\"value\":\"610400\"},{\"label\":\"渭南市\",\"value\":\"610500\"},{\"label\":\"延安市\",\"value\":\"610600\"},{\"label\":\"汉中市\",\"value\":\"610700\"},{\"label\":\"榆林市\",\"value\":\"610800\"},{\"label\":\"安康市\",\"value\":\"610900\"},{\"label\":\"商洛市\",\"value\":\"611000\"}],\"value\":\"610000\"},{\"label\":\"甘肃省\",\"children\":[{\"label\":\"兰州市\",\"value\":\"620100\"},{\"label\":\"嘉峪关市\",\"value\":\"620200\"},{\"label\":\"金昌市\",\"value\":\"620300\"},{\"label\":\"白银市\",\"value\":\"620400\"},{\"label\":\"天水市\",\"value\":\"620500\"},{\"label\":\"武威市\",\"value\":\"620600\"},{\"label\":\"张掖市\",\"value\":\"620700\"},{\"label\":\"平凉市\",\"value\":\"620800\"},{\"label\":\"酒泉市\",\"value\":\"620900\"},{\"label\":\"庆阳市\",\"value\":\"621000\"},{\"label\":\"定西市\",\"value\":\"621100\"},{\"label\":\"陇南市\",\"value\":\"621200\"},{\"label\":\"临夏回族自治州\",\"value\":\"622900\"},{\"label\":\"甘南藏族自治州\",\"value\":\"623000\"}],\"value\":\"620000\"},{\"label\":\"青海省\",\"children\":[{\"label\":\"西宁市\",\"value\":\"630100\"},{\"label\":\"海东市\",\"value\":\"630200\"},{\"label\":\"海北藏族自治州\",\"value\":\"632200\"},{\"label\":\"黄南藏族自治州\",\"value\":\"632300\"},{\"label\":\"海南藏族自治州\",\"value\":\"632500\"},{\"label\":\"果洛藏族自治州\",\"value\":\"632600\"},{\"label\":\"玉树藏族自治州\",\"value\":\"632700\"},{\"label\":\"海西蒙古族藏族自治州\",\"value\":\"632800\"}],\"value\":\"630000\"},{\"label\":\"宁夏回族自治区\",\"children\":[{\"label\":\"银川市\",\"value\":\"640100\"},{\"label\":\"石嘴山市\",\"value\":\"640200\"},{\"label\":\"吴忠市\",\"value\":\"640300\"},{\"label\":\"固原市\",\"value\":\"640400\"},{\"label\":\"中卫市\",\"value\":\"640500\"}],\"value\":\"640000\"},{\"label\":\"新疆维吾尔自治区\",\"children\":[{\"label\":\"乌鲁木齐市\",\"value\":\"650100\"},{\"label\":\"克拉玛依市\",\"value\":\"650200\"},{\"label\":\"吐鲁番市\",\"value\":\"650400\"},{\"label\":\"哈密市\",\"value\":\"650500\"},{\"label\":\"昌吉回族自治州\",\"value\":\"652300\"},{\"label\":\"博尔塔拉蒙古自治州\",\"value\":\"652700\"},{\"label\":\"巴音郭楞蒙古自治州\",\"value\":\"652800\"},{\"label\":\"阿克苏地区\",\"value\":\"652900\"},{\"label\":\"克孜勒苏柯尔克孜自治州\",\"value\":\"653000\"},{\"label\":\"喀什地区\",\"value\":\"653100\"},{\"label\":\"和田地区\",\"value\":\"653200\"},{\"label\":\"伊犁哈萨克自治州\",\"value\":\"654000\"},{\"label\":\"塔城地区\",\"value\":\"654200\"},{\"label\":\"阿勒泰地区\",\"value\":\"654300\"},{\"label\":\"自治区直辖县级行政区划\",\"value\":\"659000\"}],\"value\":\"650000\"},{\"label\":\"台湾省\",\"children\":[{\"label\":\"台北市\",\"value\":\"710100\"},{\"label\":\"高雄市\",\"value\":\"710200\"},{\"label\":\"基隆市\",\"value\":\"710300\"},{\"label\":\"台中市\",\"value\":\"710400\"},{\"label\":\"台南市\",\"value\":\"710500\"},{\"label\":\"新竹市\",\"value\":\"710600\"},{\"label\":\"嘉义市\",\"value\":\"710700\"}],\"value\":\"710000\"},{\"label\":\"香港特别行政区\",\"children\":[{\"label\":\"中西區\",\"value\":\"810001\"},{\"label\":\"灣仔區\",\"value\":\"810002\"},{\"label\":\"東區\",\"value\":\"810003\"},{\"label\":\"南區\",\"value\":\"810004\"},{\"label\":\"油尖旺區\",\"value\":\"810005\"},{\"label\":\"深水埗區\",\"value\":\"810006\"},{\"label\":\"九龍城區\",\"value\":\"810007\"},{\"label\":\"黃大仙區\",\"value\":\"810008\"},{\"label\":\"觀塘區\",\"value\":\"810009\"},{\"label\":\"荃灣區\",\"value\":\"810010\"},{\"label\":\"屯門區\",\"value\":\"810011\"},{\"label\":\"元朗區\",\"value\":\"810012\"},{\"label\":\"北區\",\"value\":\"810013\"},{\"label\":\"大埔區\",\"value\":\"810014\"},{\"label\":\"西貢區\",\"value\":\"810015\"},{\"label\":\"沙田區\",\"value\":\"810016\"},{\"label\":\"葵青區\",\"value\":\"810017\"},{\"label\":\"離島區\",\"value\":\"810018\"}],\"value\":\"810000\"},{\"label\":\"澳门特别行政区\",\"children\":[{\"label\":\"花地瑪堂區\",\"value\":\"820001\"},{\"label\":\"花王堂區\",\"value\":\"820002\"},{\"label\":\"望德堂區\",\"value\":\"820003\"},{\"label\":\"大堂區\",\"value\":\"820004\"},{\"label\":\"風順堂區\",\"value\":\"820005\"},{\"label\":\"嘉模堂區\",\"value\":\"820006\"},{\"label\":\"路氹填海區\",\"value\":\"820007\"},{\"label\":\"聖方濟各堂區\",\"value\":\"820008\"}],\"value\":\"820000\"}]";
        return json;
    }

    @Override
    public ActivityFormFeedbackDTO getDetailByVerifyCode(String code) {
        if(StringUtil.isEmpty(code)){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"核销码不能为空!");
        }
        ActivityVerifyDO activityVerifyDO = activityVerifyDAO.getByCode(code);
        PromotionActivityDO activityDO = promotionActivityDAO.getById(activityVerifyDO.getActivityId());

        ActivityFormFeedbackDTO formFeedbackDTOByActivty = this.customerFeedbackDAO.getFormFeedbackDTOByActivty(activityDO.getPaTemplateId(), activityDO.getId(), activityVerifyDO.getPhone(), activityVerifyDO.getResourceId());
        //处理表单内容
        if(activityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_FORM_ACT.getId())){
            formFeedbackDTOByActivty.setLimits(this.getFormLimits(formFeedbackDTOByActivty.getLimits(),activityDO.getExt()));
        }

        return PhoneEncryUtils.feeBackDesensitization(formFeedbackDTOByActivty);
    }



    private Map<String,String[]> getHeardMap(String[] heardList1,String[] headerKey1,PromotionActivityDO promotionActivityDO){
        Map<String,String[]> map = Maps.newHashMap();
        JSONArray jsonArrayForm = (JSONArray) promotionActivityDO.getExt().get("dynamicForm");//后台配置
        Map<Integer, String> dataMap = jsonArrayForm.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        object -> {
                            JSONObject item = (JSONObject) object;
                            return item.getInteger("formId");
                        },
                        object -> {
                            JSONObject item = (JSONObject) object;
                            return item.getString("title");
                        }
                ));
        String[] heardList = new String[heardList1.length+dataMap.size()];
        String[] headerKey = new String[headerKey1.length+dataMap.size()];
        System.arraycopy(heardList1,0,heardList,0,heardList1.length);
        System.arraycopy(headerKey1,0,headerKey,0,headerKey1.length);
        if(CollectionUtil.isNotEmpty(dataMap) && dataMap.size() >0) {
            int i =  0;
            for (Map.Entry<Integer, String> entry : dataMap.entrySet()) {
                int j = heardList1.length + i;
                i++;
                heardList[j] = entry.getValue();
                headerKey[j] = "param" + i;
            }
        }
        map.put("heardList",heardList);
        map.put("headerKey",headerKey);
        return map;
    }
}
