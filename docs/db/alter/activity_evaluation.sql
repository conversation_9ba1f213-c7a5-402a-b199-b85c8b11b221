-- 活动评价表
CREATE TABLE `promotion_activity_evaluation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `partake_log_id` bigint(20) NOT NULL COMMENT '参与记录ID',
  `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
  `nick_name` varchar(255) DEFAULT NULL COMMENT '用户昵称',
  `phone` varchar(20) NOT NULL COMMENT '用户电话',
  `overall_rating` int(1) NOT NULL COMMENT '总体评分（1-5分）',
  `decoration_rating` int(1) NOT NULL COMMENT '软装评分（1-5分）',
  `content_rating` int(1) NOT NULL COMMENT '内容评分（1-5分）',
  `venue_rating` int(1) NOT NULL COMMENT '场地评分（1-5分）',
  `service_rating` int(1) NOT NULL COMMENT '服务评分（1-5分）',
  `comment` text NOT NULL COMMENT '评论内容',
  `image_urls` json DEFAULT NULL COMMENT '评价图片URL列表',
  `is_anonymous` tinyint(1) DEFAULT '0' COMMENT '是否匿名评价',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `version` bigint(20) DEFAULT '0' COMMENT '版本号',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` int(1) DEFAULT '0' COMMENT '逻辑删除标记',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_activity_phone` (`activity_id`,`phone`) COMMENT '活动用户唯一索引',
  KEY `idx_activity_id` (`activity_id`) COMMENT '活动ID索引',
  KEY `idx_partake_log_id` (`partake_log_id`) COMMENT '参与记录ID索引',
  KEY `idx_phone` (`phone`) COMMENT '用户电话索引',
  KEY `idx_created_time` (`created_time`) COMMENT '创建时间索引',
  KEY `idx_overall_rating` (`overall_rating`) COMMENT '总体评分索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动评价表'; 