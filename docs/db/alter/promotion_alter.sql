-- 添加 促销域 ext拓展字段
ALTER TABLE deepexi_domain_promotion.`promotion_open_group` ADD COLUMN   `ext` json DEFAULT NULL COMMENT '拓展字段';
ALTER TABLE deepexi_domain_promotion.`promotion_strategy` ADD COLUMN   `ext` json DEFAULT NULL COMMENT '拓展字段';
ALTER TABLE deepexi_domain_promotion.`promotion_coupon_logger` ADD COLUMN   `ext` json DEFAULT NULL COMMENT '拓展字段';
ALTER TABLE deepexi_domain_promotion.`promotion_coupon` ADD COLUMN   `ext` json DEFAULT NULL COMMENT '拓展字段';
ALTER TABLE deepexi_domain_promotion.`promotion_activity_template` ADD COLUMN   `ext` json DEFAULT NULL COMMENT '拓展字段';
ALTER TABLE deepexi_domain_promotion.`promotion_activity_logger` ADD COLUMN   `ext` json DEFAULT NULL COMMENT '拓展字段';
ALTER TABLE deepexi_domain_promotion.`promotion_activity_limit` ADD COLUMN   `ext` json DEFAULT NULL COMMENT '拓展字段';
ALTER TABLE deepexi_domain_promotion.`promotion_activity` ADD COLUMN   `ext` json DEFAULT NULL COMMENT '拓展字段';


-- 华发项目 数据库结构变更 记录
-- 在下面记录，需要备注sql更改内容、更改目的
ALTER TABLE promotion_activity_fission_log MODIFY COLUMN user_id varchar(64) NOT NULL COMMENT '助力用户ID';
ALTER TABLE promotion_activity_fission_log MODIFY COLUMN union_id varchar(64) NOT NULL COMMENT 'unionId';

-- 添加两个表
CREATE TABLE `promotion_activity_fission_assist_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(32) DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(32) DEFAULT NULL COMMENT '更新者',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除状态 0无效 1有效',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号，乐观锁',
  `app_id` bigint(20) NOT NULL COMMENT '接入方id',
  `activity_id` bigint(12) NOT NULL COMMENT '活动ID',
  `partake_log_id` bigint(12) DEFAULT NULL COMMENT '活动发起者参与记录id',
  `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
  `nick_name` varchar(32) DEFAULT NULL COMMENT '用户昵称',
  `user_name` varchar(32) DEFAULT NULL COMMENT '用户名',
  `phone` bigint(11) DEFAULT NULL COMMENT '手机号',
  `prize_result` varchar(64) DEFAULT NULL COMMENT '中奖结果',
  `code` varchar(32) DEFAULT NULL COMMENT '兑奖码',
  `get_time` varchar(64) DEFAULT NULL COMMENT '领奖时间',
  `verify_time` varchar(64) DEFAULT NULL COMMENT '核销时间',
  `verify_by` varchar(64) DEFAULT NULL COMMENT '核销人',
  `resource_id` bigint(20) DEFAULT NULL COMMENT '奖品ID',
  `union_id` varchar(64) DEFAULT NULL COMMENT 'unionId',
  `type` bigint(4) DEFAULT '0' COMMENT '类型:1-分享获奖,2-帮忙助力获奖',
  `is_received` tinyint(4) DEFAULT '0' COMMENT '是否领取',
  `project_name` varchar(255) DEFAULT NULL COMMENT '项目名称',
  `project_id` varchar(20) DEFAULT NULL COMMENT '项目ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_activity_id` (`activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=386 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='分享、帮忙助力获奖记录表';


CREATE TABLE `promotion_activity_fission_assist_resource` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户id',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(32) NOT NULL COMMENT '创建者',
  `updated_by` varchar(32) NOT NULL COMMENT '更新者',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除状态 0无效 1有效',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号，乐观锁',
  `app_id` bigint(20) NOT NULL COMMENT '接入方id',
  `activity_id` bigint(12) NOT NULL COMMENT '活动ID',
  `partake_log_id` bigint(12) DEFAULT NULL COMMENT '活动发起者参与记录id',
  `user_id` varchar(50) DEFAULT NULL COMMENT '用户id',
  `phone` bigint(11) DEFAULT NULL COMMENT '手机号',
  `resource_id` bigint(20) NOT NULL COMMENT '奖品Id',
  `union_id` varchar(50) NOT NULL COMMENT 'unionId',
  `type` bigint(4) DEFAULT '0' COMMENT '活动类型:0-助力活动',
  `ladder_sort` int(4) DEFAULT '0' COMMENT '阶梯号',
  `get_time` varchar(64) DEFAULT NULL COMMENT '领取时间',
  `is_received` tinyint(4) DEFAULT '0' COMMENT '是否领取:0-未领取,1-领取',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=156 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='助力活动可领取奖品表';


--抽奖、秒杀、优惠券的用户参与记录表添加项目id
ALTER TABLE  promotion_activity_partake_log ADD project_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '项目名称';
ALTER TABLE  promotion_activity_partake_log ADD project_id varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '项目ID';




-- 活动项目表，添加客观地理城市名称
ALTER TABLE `promotion_activity_participation`
MODIFY COLUMN `city_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '城市id' AFTER `area_name`,
MODIFY COLUMN `city_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '城市名称' AFTER `city_id`,
ADD COLUMN `real_city_id` varchar(64) COMMENT '地址位置，城市id' AFTER `city_name`,
ADD COLUMN `real_city_name` varchar(255) COMMENT '地址位置，城市名称' AFTER `real_city_id`;

-- 添加活动专题推广
ALTER TABLE `promotion_activity_promotion_channel`
ADD COLUMN `promotion_type` tinyint(2) NOT NULL COMMENT '推广类型，0：活动推广；1：活动专题推广' AFTER `activity_id`;

-- 去掉创建人和更新人的必填选项
ALTER TABLE `promotion_activity_promotion_channel`
MODIFY COLUMN `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '创建者' AFTER `remark`,
MODIFY COLUMN `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '更新者' AFTER `created_by`;



-- 非必填
ALTER TABLE `promotion_activity_form_feedback`
MODIFY COLUMN `resource_id`  bigint(12) NULL COMMENT '资源奖品ID' AFTER `activity_id`;

-- 参数类型修改
ALTER TABLE `promotion_activity_fission_log`
MODIFY COLUMN `user_id`  varchar(20) NOT NULL COMMENT '助力用户ID' AFTER `activity_id`;

-- 参数长度修改
ALTER TABLE `promotion_activity_fission_log`
MODIFY COLUMN `user_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '助力用户ID' AFTER `activity_id`;

-- 参数类型、长度修改
ALTER TABLE `promotion_activity_fission_log`
MODIFY COLUMN `union_id`  varchar(32) NOT NULL COMMENT 'unionId' AFTER `money`;


-- 添加手机号字段
ALTER TABLE `promotion_activity_logger`
ADD COLUMN `phone`  bigint(11) NULL COMMENT '手机号' AFTER `user_id`;

ALTER TABLE `promotion_activity_order`
ADD COLUMN `phone`  bigint(11) NULL COMMENT '手机号' AFTER `user_id`;

-- 添加是否可跳转字段
ALTER TABLE promotion_activity_participation ADD is_jumpable tinyint(1) DEFAULT 0 NULL COMMENT '是否可跳转（0不可跳转，1可跳转）' AFTER porject_period_list;

-- 修改字段长度
ALTER TABLE `promotion_activity_form_feedback`
MODIFY COLUMN `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID' AFTER `limits`,
MODIFY COLUMN `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号' AFTER `user_name`,
MODIFY COLUMN `union_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'unionId' AFTER `activity_type`;

-- 修改表注释
ALTER TABLE `promotion_activity_partake_log` COMMENT = '用户参与活动记录表';

-- 非必填
ALTER TABLE `promotion_activity_verify`
MODIFY COLUMN `user_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户ID' AFTER `verify_type`,
MODIFY COLUMN `user_name`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户名称' AFTER `nick_name`;

-- 非必填

ALTER TABLE `promotion_activity_fission_log`
MODIFY COLUMN `user_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '助力用户ID' AFTER `activity_id`;

ALTER TABLE `promotion_activity_form_feedback`
MODIFY COLUMN `user_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户ID' AFTER `limits`;

ALTER TABLE `promotion_activity_order`
MODIFY COLUMN `user_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户ID' AFTER `resource_id`;

ALTER TABLE `promotion_activity_partake_log`
MODIFY COLUMN `user_id`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户ID' AFTER `activity_id`;

ALTER TABLE `promotion_activity_user_related`
MODIFY COLUMN `user_id`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户ID' AFTER `activity_id`;


-- 注释修改
ALTER TABLE `promotion_resource`
MODIFY COLUMN `house_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目名称(房源名称)' AFTER `use_rule`,
MODIFY COLUMN `house_message`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '楼栋房号(房源信息)' AFTER `house_volume`,
MODIFY COLUMN `cost_price`  varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原总价(原价)' AFTER `house_message`,
MODIFY COLUMN `discount_price`  varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '折后总价(折扣价)' AFTER `cost_price`;

ALTER TABLE `promotion_his_resource`
MODIFY COLUMN `house_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目名称(房源名称)' AFTER `use_rule`,
MODIFY COLUMN `house_message`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '楼栋房号(房源信息)' AFTER `house_volume`,
MODIFY COLUMN `cost_price`  varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原总价(原价)' AFTER `house_message`,
MODIFY COLUMN `discount_price`  varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '折后总价(折扣价)' AFTER `cost_price`;

-- 添加字段
ALTER TABLE `promotion_resource`
ADD COLUMN `cost_price_type`  bigint(10) NULL DEFAULT 0 COMMENT '原单价或原总价类型:0-原单价,1-原总价' AFTER `discount_price`,
ADD COLUMN `discount_price_type`  bigint(10) NULL DEFAULT 0 COMMENT '折后总价及折后单价类型:0-折后单价,1-折后总价' AFTER `cost_price_type`;

ALTER TABLE `promotion_his_resource`
ADD COLUMN `cost_price_type`  bigint(10) NULL DEFAULT 0 COMMENT '原单价或原总价类型:0-原单价,1-原总价' AFTER `discount_price`,
ADD COLUMN `discount_price_type`  bigint(10) NULL DEFAULT 0 COMMENT '折后总价及折后单价类型:0-折后单价,1-折后总价' AFTER `cost_price_type`;


-- 添加字段码类型
ALTER TABLE `promotion_activity_promotion_channel`
ADD COLUMN `codeType`  tinyint(4) NULL COMMENT '码类型' AFTER `mini_program_code`;

ALTER TABLE `promotion_activity_promotion_channel`
CHANGE COLUMN `codeType` `code_type`  tinyint(4) NULL DEFAULT NULL COMMENT '码类型' AFTER `mini_program_code`,
ADD COLUMN `code_id`  bigint(20) NULL COMMENT '一物一码ID' AFTER `code_type`;


-- 非必填
ALTER TABLE `promotion_activity_page_share`
MODIFY COLUMN `share_content`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分享内容' AFTER `share_title`;

-- 添加字段
ALTER TABLE `promotion_activity_promotion_channel`
ADD COLUMN `mini_url`  varchar(255) NULL COMMENT '小程序活动页连接' AFTER `code_id`;

-- 类型修改
ALTER TABLE `promotion_activity_promotion_channel`
MODIFY COLUMN `code_type`  varchar(32) NULL DEFAULT NULL COMMENT '码类型' AFTER `mini_program_code`;

-- 属性长度修改
ALTER TABLE `promotion_activity_promotion_channel`
MODIFY COLUMN `url`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接' AFTER `type`;


-- 添加字段
ALTER TABLE `promotion_activity_group`
ADD COLUMN `sceneCode`  varchar(255) NULL COMMENT '场景码' AFTER `arrangement`;

ALTER TABLE `promotion_activity_group`
CHANGE COLUMN `sceneCode` `scene_code`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '场景码' AFTER `arrangement`;



-- 添加字段
ALTER TABLE promotion_activity_participation ADD real_area_id varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '地理位置，区域ID';
ALTER TABLE promotion_activity_participation ADD real_area_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '地理位置，区域名称';

-- 添加字段
ALTER TABLE `promotion_his_resource`
ADD COLUMN `project_id`  varchar(20) NULL COMMENT '项目ID' AFTER `ladder_sort`,
ADD COLUMN `project_name`  varchar(255) NULL COMMENT '项目名称' AFTER `project_id`;

-- 长度修改
ALTER TABLE `promotion_activity_order`
MODIFY COLUMN `union_id`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'unionId' AFTER `ext`;

-- 添加字段
ALTER TABLE `promotion_his_resource`
ADD COLUMN `sys_type`  tinyint(2) NULL DEFAULT 0 COMMENT '项目类型:0-活动项目,1-系统项目' AFTER `project_name`;

-- 修改unionId为非必填
ALTER TABLE promotion_activity_form_feedback MODIFY COLUMN union_id varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'unionId';
ALTER TABLE promotion_activity_fission_assist_resource MODIFY COLUMN union_id varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'unionId';
ALTER TABLE promotion_activity_fission_log MODIFY COLUMN union_id varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'unionId';
ALTER TABLE promotion_activity_order MODIFY COLUMN union_id varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'unionId';
ALTER TABLE promotion_activity_partake_log MODIFY COLUMN union_id varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'unionId';
ALTER TABLE promotion_activity_user_related MODIFY COLUMN union_id varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'unionId';
ALTER TABLE promotion_activity_verify MODIFY COLUMN union_id varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'unionId';


-- version 2.0.2 开始
-- 2021-9-1 之后 更新
-- 添加创建人id字段
ALTER TABLE `promotion_activity_group`
ADD COLUMN `user_id`  varchar(32) NULL DEFAULT NULL COMMENT '创建人用户ID' AFTER `scene_code`;


ALTER TABLE `promotion_activity`
MODIFY COLUMN `created_person`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人(创建人userId)' AFTER `created_time`;


ALTER TABLE `promotion_activity_promotion_channel`
ADD COLUMN `scene` varchar(50) COMMENT '一物一码参数加密字段' AFTER `mini_url`;


-- 添加接口日志表
CREATE TABLE `promotion_external_service_log` (
  `id` bigint(32) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(32) DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(32) DEFAULT NULL COMMENT '更新者',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除状态 0无效 1有效',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号，乐观锁',
  `app_id` bigint(20) DEFAULT NULL COMMENT '接入方id',
  `activity_id` bigint(32) DEFAULT NULL COMMENT '活动ID',
  `resource_id` bigint(32) DEFAULT NULL COMMENT '资源ID',
  `request_json` text COMMENT '请求json数据',
  `response_json` text COMMENT '返回json数据',
  `url` varchar(255) DEFAULT NULL COMMENT '请求接口地址',
  `type` bigint(4) DEFAULT '0' COMMENT '类型:0-红包,1-话费',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='外部接口日志表';



-- 添加马拉松活动
CREATE TABLE promotion_marathon(
        id bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
        activity_id bigint(32) NOT NULL COMMENT '活动id',
        code VARCHAR(16) NOT NULL COMMENT '马拉松活动参赛码',
        phone VARCHAR(16) COMMENT '手机号',
        status INT NOT NULL COMMENT '活动状态',
        created_time DATETIME NOT NULL COMMENT '创建时间',
        updated_time DATETIME COMMENT '修改时间',
        PRIMARY KEY (id),
        CONSTRAINT promotionmarathon_ix1 UNIQUE (code),
        CONSTRAINT promotionmarathon_ix2 UNIQUE(activity_id,phone)
    ) COMMENT='马拉松活动参赛码表';

-- version 2.0.2 结束


-- 2021-09-24 新增上下架状态字段
ALTER TABLE `promotion_activity`
ADD COLUMN `upper_status`  int(10) NULL COMMENT '上下架状态:0-下架,1-上架' AFTER `status`;


-- 修改注释
ALTER TABLE `promotion_activity_form_feedback`
MODIFY COLUMN `type`  bigint(4) NOT NULL COMMENT '0、微信小程序 1、H5  2、抖音 3、非微信外浏览器' AFTER `union_id`;


-- 2021-11-04 添加拓展字段
ALTER TABLE `promotion_activity_promotion_channel`
ADD COLUMN `ext`  json NULL COMMENT '拓展字段' AFTER `scene`;


-- 用于记录机构类型
ALTER TABLE `promotion_activity_group`
ADD COLUMN `ext`  json NULL COMMENT '拓展字段' AFTER `user_id`;