INSERT INTO promotion.marketing_kpi (`type`,code,name,unit_name,sort,frequency_type,frequency,status,tenant_id,app_id,created_time,updated_time,created_by,updated_by,is_deleted,version,remark) VALUES 
(1,'L4YAwA','活动参与人数','人',1,1,'0',1,'0',NULL,'2021-04-26 10:51:51.0','2021-04-26 10:51:51.0','zhangbiwei','',0,0,'统计不同活动的参与人数，不同活动模板的参与定义有所区别：
①点击抽奖；
②提交表单；
③领取优惠券；
④支付优惠券，后续退款忽略；
⑤发起助力，不管是否助力成功（参与助力不计算）；
⑥发起砍价，不管是否砍价成功（参与砍价不计算）。')
,(1,'L4YAwB','活动访问人数','人',2,1,'0',1,'0',NULL,'2021-04-26 10:52:48.0','2021-04-26 10:52:48.0','zhangbiwei','',0,0,'统计不同活动的访问人数')
,(1,'L4QBWC','活动访问次数','次',3,1,'0',1,'0',NULL,'2021-04-26 10:53:11.0','2021-04-26 10:53:11.0','zhangbiwei','',0,0,'统计不同活动的访问次数')
,(1,'L5KH57','活动核销人数','人',5,1,'0',1,'0',NULL,'2021-04-26 10:53:30.0','2021-04-26 10:53:30.0','zhangbiwei','',0,0,'统计不同活动中的核销资源人数，包括主动核销和被动核销（手机话费和现金红包为被动核销）')
,(1,'L4QBat','领取礼品人数','人',6,1,'0',1,'0',NULL,'2021-04-26 10:53:30.0','2021-04-26 10:53:30.0','zhangbiwei','',0,0,'统计不同活动的领取礼品人数，不同模板的领取礼品定义区别如下：（秒杀及优惠券活动不计算）
①抽奖：完成信息登记后领取礼品成功；
②表单：开启抽奖的情况下，提交表单则为领取礼品成功；
③助力：助力有效期结束后手动领取礼品或者整个活动结束后系统自动发放礼品；
④砍价：成功以最低价完成支付即为领取礼品成功。')
,(1,'L5KH6D','提交订单人数','人',7,1,'0',1,'0',NULL,'2021-04-26 10:53:30.0','2021-04-26 10:53:30.0','zhangbiwei','',0,0,'统计不同活动的提交订单人数（仅秒杀、优惠券活动和砍价活动中支持统计该指标）')
,(1,'L5KH6G','支付订单人数','人',5,1,'0',1,'0',NULL,'2021-04-26 10:53:30.0','2021-04-26 10:53:30.0','zhangbiwei','',0,0,'统计不同活动的支付订单人数（仅秒杀、优惠券活动和砍价活动中支持统计该指标，忽略后续退款）')
,(1,'L5KH60','帮忙助力人数','人',9,1,'0',1,'0',NULL,'2021-04-26 10:53:30.0','2021-04-26 10:53:30.0','zhangbiwei','',0,0,'统计不同的助力活动中，每个发起者的助力好友人数（进入助力页面授权成功即为帮忙助力）')
,(1,'L5KH61','帮忙砍价人数','人',10,1,'0',1,'0',NULL,'2021-04-26 10:53:30.0','2021-04-26 10:53:30.0','zhangbiwei','',0,0,'统计不同的砍价活动中，每个发起者的砍价好友人数（进入砍价页面授权成功即为帮忙砍价）')
,(1,'L5KH62','活动留资人数','人',11,1,'0',1,'0',NULL,'2021-04-26 10:53:30.0','2021-04-26 10:53:30.0','zhangbiwei','',0,0,'统计不同活动完成信息登记的人数（除表单外，其它活动模板均支持留资率统计）')
;
INSERT INTO promotion.marketing_kpi (`type`,code,name,unit_name,sort,frequency_type,frequency,status,tenant_id,app_id,created_time,updated_time,created_by,updated_by,is_deleted,version,remark) VALUES 
(1,'L4YAwE','活动分享次数','次',12,1,'0',1,'0',NULL,'2021-04-26 10:53:30.0','2021-04-26 10:53:30.0','zhangbiwei','',0,0,'统计不同活动的分享次数，点击分享按钮（页面底部分享或者微信自带按钮）或生成海报按钮')
,(1,'L4YAwF','活动分享人数','人',13,1,'0',1,'0',NULL,'2021-04-26 10:53:30.0','2021-04-26 10:53:30.0','zhangbiwei','',0,0,'统计不同活动的分享次数，点击分享按钮（页面底部分享或者微信自带按钮）或生成海报按钮')
,(1,'L5LA9H','退款成功人数','人',5,1,'0',1,'0',NULL,'2021-04-26 10:53:30.0','2021-04-26 10:53:30.0','zhangbiwei','',0,0,'统计不同活动的退款成功人数（仅秒杀、优惠券活动和砍价活动中支持统一该指标）')
,(1,'L5KH68','获电数','人',19,1,'0',1,'0',NULL,'2021-07-20 10:53:30.0','2021-07-20 10:53:30.0','zhangbiwei','',0,0,'通过活动页面触发的授权，为活动获电数')
;

INSERT INTO promotion.marketing_kpi (`type`,code,name,unit_name,sort,frequency_type,frequency,status,tenant_id,app_id,created_time,updated_time,created_by,updated_by,is_deleted,version,remark) VALUES
(1,'L5KH58','助力成功人数','人',21,1,'0',1,'0',NULL,'2021-08-26 10:51:51.0','2021-04-26 10:51:51.0','zhangbiwei','',0,0,'统计不同的助力活动中，助力成功的人数，完成所有阶梯视为助力成功')
,(1,'L5KH59','砍价成功人数','人',20,1,'0',1,'0',NULL,'2021-08-26 10:51:51.0','2021-04-26 10:51:51.0','zhangbiwei','',0,0,'统计不同的砍价活动中，砍至系统所设定的最低价的人数')
;