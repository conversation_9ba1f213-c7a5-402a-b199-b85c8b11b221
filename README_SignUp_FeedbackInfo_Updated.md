# 报名活动增加用户信息配置功能

## 功能概述

本次更新为报名活动增加了用户信息配置功能，允许管理平台在创建活动时配置用户需要填写的字段信息，并在用户报名时进行相应的校验。同时增加了配置校验功能，确保每个字段类型只能配置一个。

## 功能特性

### 1. 管理平台配置

管理平台在创建活动时，可以在 `ext` 字段中配置 `feedbackInfo`，用于定义用户报名时需要填写的字段信息。

**配置示例：**
```json
{
  "ext": {
    "feedbackInfo": [
      {
        "type": 4,
        "value": "姓名",
        "code": "name",
        "isRequired": 1
      },
      {
        "type": 1,
        "value": "手机号码",
        "code": "phone",
        "isRequired": 1
      },
      {
        "value": "身份证",
        "type": 3,
        "code": "idCardNo",
        "isRequired": 0
      }
    ]
  }
}
```

**字段说明：**
- `type`: 字段类型（0-文本框，1-手机号码，2-所在地区，3-身份证，4-姓名，5-港澳通行证）
- `value`: 字段显示名称
- `code`: 字段编码，用于标识字段
- `isRequired`: 是否必填（0-否，1-是）

**配置校验规则：**
- 每个字段类型只能配置一个（如：不能同时配置两个姓名字段）
- 每个字段编码必须唯一（如：不能有两个字段都使用 "name" 编码）
- 必填字段属性（`isRequired`）不能为空
- 字段显示名称（`value`）不能为空

### 2. 用户报名校验

用户在小程序报名时，系统会根据配置的 `feedbackInfo` 进行以下校验：

#### 2.1 必填字段校验
- 如果字段配置为必填（`isRequired: 1`），则用户必须填写该字段
- 如果字段配置为非必填（`isRequired: 0`），则用户可以选择性填写

#### 2.2 字段格式校验
- **手机号码**：校验格式为 `1[3-9]xxxxxxxxx`
- **身份证**：校验18位身份证号码格式
- **姓名**：校验长度在2-20个字符之间
- **港澳通行证**：校验港澳通行证格式
- **其他类型**：暂不进行格式校验

#### 2.3 默认配置
如果活动没有配置 `feedbackInfo`，系统会使用默认配置：
- 姓名：必填
- 手机号码：必填

### 3. 用户报名数据结构

用户报名时的数据结构示例：

```json
{
  "ext": {
    "items": [
      {
        "name": "张三",
        "phone": "13589670955"
      },
      {
        "name": "李四",
        "phone": "13589670956",
        "idCardNo": "******************"
      }
    ]
  }
}
```

## 技术实现

### 1. 核心类

- **SignUpService**: 报名活动服务类，负责处理报名逻辑和校验
- **EnrollmentInfoVO**: 用户信息字段配置对象
- **EnrollmentInfoEnum**: 字段类型枚举

### 2. 主要方法

#### `validateFeedbackInfoUniqueness()`
- 校验用户信息配置字段类型唯一性
- 确保每个字段类型只能配置一个
- 校验字段编码唯一性
- 校验必填字段属性完整性

#### `validateFeedbackInfo()`
- 获取活动配置的 `feedbackInfo`
- 如果没有配置，使用默认配置
- 调用校验方法验证用户输入

#### `validateUserItemInfo()`
- 校验单个用户的信息项
- 检查必填字段是否已填写

#### `getDefaultFeedbackInfo()`
- 提供默认的字段配置
- 包含姓名和手机号码两个必填字段

### 3. 校验流程

#### 3.1 创建活动时校验
1. 管理平台提交活动创建请求
2. 系统调用 `processDefaultValue()` 方法
3. 调用 `validateFeedbackInfoUniqueness()` 校验配置
4. 校验字段类型唯一性
5. 校验字段编码唯一性
6. 校验必填字段属性完整性

#### 3.2 用户报名时校验
1. 用户提交报名请求
2. 系统获取活动配置的 `feedbackInfo`
3. 如果没有配置，使用默认配置
4. 遍历每个报名用户的信息
5. 根据配置校验必填字段
6. 校验通过后继续后续流程

## 使用示例

### 1. 创建活动时配置字段

```java
// 在活动创建时，ext字段中配置feedbackInfo
Map<String, Object> ext = new HashMap<>();
List<EnrollmentInfoVO> feedbackInfo = new ArrayList<>();

// 配置姓名字段（必填）
EnrollmentInfoVO nameField = new EnrollmentInfoVO();
nameField.setType(EnrollmentInfoEnum.FULL_NAME.getId());
nameField.setValue("姓名");
nameField.setCode("name");
nameField.setIsRequired(1);
feedbackInfo.add(nameField);

// 配置手机号码字段（必填）
EnrollmentInfoVO phoneField = new EnrollmentInfoVO();
phoneField.setType(EnrollmentInfoEnum.MOBILE.getId());
phoneField.setValue("手机号码");
phoneField.setCode("phone");
phoneField.setIsRequired(1);
feedbackInfo.add(phoneField);

// 配置身份证字段（选填）
EnrollmentInfoVO idCardField = new EnrollmentInfoVO();
idCardField.setType(EnrollmentInfoEnum.IDCARD.getId());
idCardField.setValue("身份证");
idCardField.setCode("idCardNo");
idCardField.setIsRequired(0);
feedbackInfo.add(idCardField);

ext.put("feedbackInfo", feedbackInfo);
```

### 2. 用户报名时提交信息

```java
// 用户报名时，在ext.items中提交用户信息
Map<String, Object> userExt = new HashMap<>();
List<Map<String, Object>> items = new ArrayList<>();

Map<String, Object> user1 = new HashMap<>();
user1.put("name", "张三");
user1.put("phone", "13589670955");
items.add(user1);

Map<String, Object> user2 = new HashMap<>();
user2.put("name", "李四");
user2.put("phone", "13589670956");
user2.put("idCardNo", "******************");
items.add(user2);

userExt.put("items", items);
```

## 错误处理

### 1. 配置校验错误

#### 重复字段类型
```
字段类型[姓名]重复配置，每个字段类型只能配置一个
```

#### 重复字段编码
```
字段编码[name]重复，每个字段编码必须唯一
```

#### 缺少必填字段属性
```
字段[姓名]的isRequired属性不能为空
```

#### 缺少字段显示名称
```
字段[name]的value属性不能为空
```

### 2. 用户报名校验错误

#### 必填字段为空
```
第1位用户的姓名不能为空
第2位用户的手机号码不能为空
```

#### 字段格式错误
```
第1位用户的手机号码格式不正确
第2位用户的身份证格式不正确
```

#### 字段长度错误
```
第1位用户的姓名长度应在2-20个字符之间
```

## 注意事项

1. **字段类型唯一性**：每个字段类型只能配置一个，避免重复配置
2. **字段编码唯一性**：每个字段的 `code` 必须唯一，建议使用有意义的英文名称
3. **字段类型支持**：目前支持6种字段类型（0-文本框，1-手机号码，2-所在地区，3-身份证，4-姓名，5-港澳通行证）
4. **默认配置**：如果活动没有配置 `feedbackInfo`，系统会自动使用默认配置
5. **向后兼容**：新增功能不影响现有活动的正常运行
6. **性能考虑**：校验逻辑在内存中进行，不会影响数据库性能

## 测试

项目包含完整的测试用例，覆盖了以下测试场景：

### 1. 功能测试
- **SignUpServiceTest.java**: 测试基本的报名功能
- **SignUpServiceValidationTest.java**: 测试配置校验功能

### 2. 测试场景
- 有效配置测试
- 重复字段类型配置测试
- 重复字段编码配置测试
- 缺少必填字段配置测试
- 字段类型映射测试

运行测试命令：
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=SignUpServiceTest
mvn test -Dtest=SignUpServiceValidationTest
```

## 总结

本次更新成功实现了报名活动的用户信息配置功能，提供了灵活的字段配置能力和严格的配置校验机制。主要特性包括：

1. **灵活的字段配置**：支持多种字段类型，可自定义必填属性
2. **严格的配置校验**：确保字段类型和编码的唯一性
3. **智能的默认配置**：自动提供姓名和手机号码的默认配置
4. **完整的用户校验**：根据配置自动校验用户输入信息
5. **完善的错误处理**：提供清晰的错误提示信息

系统能够根据配置自动进行字段校验，确保用户输入信息的完整性和准确性，同时通过配置校验防止管理员的配置错误，提高了系统的稳定性和用户体验。 