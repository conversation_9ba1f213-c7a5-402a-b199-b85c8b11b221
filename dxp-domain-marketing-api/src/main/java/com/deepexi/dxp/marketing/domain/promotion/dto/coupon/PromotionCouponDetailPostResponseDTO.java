package com.deepexi.dxp.marketing.domain.promotion.dto.coupon;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.CouponLimitDTO;
import com.deepexi.util.domain.dto.BaseExtDTO;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠券详情DTO
 *
 * <AUTHOR> ming.zhong
 * @date created in 14:48 2019/11/28
 */
@Data
@EqualsAndHashCode
@ApiModel
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PromotionCouponDetailPostResponseDTO extends BaseExtDTO {
    /**
     * 优惠券code
     */
    private String code;
    /**
     * 优惠券名称
     */
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 优惠券类型
     */
    @ApiModelProperty(value = "优惠券类型")
    private String couponType;

    /**
     * 代金券面值|折扣券折扣
     */
    @ApiModelProperty(value = "代金券面值|折扣券折扣")
    private BigDecimal couponValue;

    /**
     * 是否启用 0 禁用 1 启用
     */
    @NotBlank
    @ApiModelProperty(value = "是否启用 0 禁用 1 启用")
    private Integer status;

    /**
     * 使用条件适用金额
     */
    @ApiModelProperty(value = "使用条件适用金额")
    private BigDecimal condition;

    /**
     * 使用限制json
     */
    @ApiModelProperty("限制")
    private CouponLimitDTO couponLimit;
    /**
     * 使用限制json
     */
    @ApiModelProperty("限制")
    private String limits;


    /**
     * 租户隔离
     */
    @ApiModelProperty(value = "租户隔离")
    private String tenantId;

    /**
     * 应用隔离
     */
    @ApiModelProperty(value = "应用隔离")
    private Long appId;

    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;

    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标记
     */
    private Integer deleted = 0;
}
