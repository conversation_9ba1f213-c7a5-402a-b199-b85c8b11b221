package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "核销管理列表返回对象", description = "核销管理列表返回对象")
public class ActivityVerifyInResponseDTO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;


    @ApiModelProperty(value = "资源ID或者优惠ID")
    private Long resourceId;

    /**
     * 0、优惠券 1、资源
     */
    @ApiModelProperty(value = "核销类型:0、优惠券 1、资源")
    private Integer verifyType;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 电话号码
     */
    @ApiModelProperty(value = "电话号码")
    private String phone;

    /**
     * 优惠券名称或者奖品名称
     */
    @ApiModelProperty(value = "优惠券名称或者奖品名称")
    private String name;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 是否付费 0否 1是
     */
    @ApiModelProperty(value = "是否付费 0否 1是")
    private Integer isPay;

    /**
     * 使用时间类型 0 、不限制 1、指定时间 2、有效天数
     */
    @ApiModelProperty(value = "使用时间类型 0 、不限制 1、指定时间 2、有效天数")
    private Integer validTimeType;

    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date validStartTime;

    /**
     * 排行榜 0关闭,1开启
     */
    @ApiModelProperty(value = "有效期结束时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date validEndTime;

    @ApiModelProperty(value = "有效天数")
    private Integer validDay;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "领取时间/支付时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date payTime;

    /**
     * 核销码
     */
    @ApiModelProperty(value = "核销码")
    private String code;

    /**
     * 核销状态 0、待核销 1、已核销 2、已过期 3、已失效
     */
    @ApiModelProperty(value = "核销状态 0、待核销 1、已核销 2、已过期 3、已失效")
    private Integer verifyStatus;
    /**
     * 退款状态 0、未退款 1、已退款
     */
    @ApiModelProperty(value = "退款状态 0、未退款 1、已退款")
    private Integer refundStatus;

    /**
     * 核销人
     */
    @ApiModelProperty(value = "核销人")
    private String verifyBy;

    /**
     * 核销时间
     */
    @ApiModelProperty(value = "核销时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date verifyTime;


    /**
     * 退款人
     */
    @ApiModelProperty(value = "退款人")
    private String refundBy;
    /**
     * 退款时间
     */
    @ApiModelProperty(value = "退款时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date refundTime;
    /**
     * order_id
     */
    @ApiModelProperty(value = "order_id")
    private Integer orderId;
    /**
     * unionId
     */
    @ApiModelProperty(value = "unionId")
    private String unionId;
    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty(value = "类型0、微信小程序  1、H5  2、抖音")
    private Integer type;


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "资源信息")
    private ResourceResponseVO resourceInfo;

    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value="资源属性:0-不限项目,1-指定项目")
    private Integer resourcesAttribute;

    @ApiModelProperty("活动名称")
    private String activityName;


    @ApiModelProperty("信息登记内容")
    private ActivityFormFeedbackDTO activityFormFeedbackDTO;

    @ApiModelProperty(value = "支付平台订单号")
    private String wxOrderNo;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal payMoney;


  @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("资源信息")
    public static class ResourceResponseVO extends AbstractObject implements Serializable {
        /**
         * 资源类型 0、卡劵 1、第三方接入
         */
        @ApiModelProperty(value = "资源类型")
        private Integer type;

        /**
         * 细分类别 0、普通劵 1、房源劵 2、商品劵
         */
        @ApiModelProperty("细分类别")
        private Integer couponCategory;

        @ApiModelProperty("第三方细分类别")
        private Integer thirdCategory;

        /**
         * 卡劵类型 0、代金劵 1、折扣劵
         */
        @ApiModelProperty("卡劵类型")
        private Integer couponType;

        @ApiModelProperty("资源名称")
        private String name;

        @ApiModelProperty("房源名称")
        private String houseName;

        @ApiModelProperty("礼品券发放方式:0-线上邮寄,1-线下核销")
        private Integer grantWay;

        /**
         * 资源图片
         */
        @ApiModelProperty("资源图片")
        private String url;

        /**
         * 卡劵面值
         */
        @ApiModelProperty("卡劵面值")
        private BigDecimal couponValue;

        /**
         * 使用时间类型 0 、不限制 1、指定时间 2、有效天数
         */
        @ApiModelProperty("使用时间类型")
        private Integer validTimeType;


        @ApiModelProperty("有效期开始时间")
        @DateTimeFormat(
                pattern = "yyyy-MM-dd HH:mm:ss"
        )
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
        private Date validStartTime;
        /**
         * 有效期结束时间
         */
        @ApiModelProperty("有效期结束时间")
        @DateTimeFormat(
                pattern = "yyyy-MM-dd HH:mm:ss"
        )
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
        private Date validEndTime;

        /**
         * 有效天数
         */
        @ApiModelProperty("有效天数")
        private Integer validDay;


        @ApiModelProperty("房间信息")
        private String houseMessage;

        @ApiModelProperty("优惠卷code")
        private String code;

        @ApiModelProperty("领取方式:0-免费领取,1-付费领取")
        private Integer receiveMode;

        @ApiModelProperty("原价")
        private String costPrice;

        @ApiModelProperty("拆后价")
        private String discountPrice;

        @ApiModelProperty("使用规则")
        private String useRule;

        @ApiModelProperty("房源面积")
        private String houseVolume;

        @ApiModelProperty("原单价或原总价类型:0-原单价,1-原总价")
        private Integer costPriceType;

        @ApiModelProperty("折后总价及折后单价类型:0-折后单价,1-折后总价")
        private Integer discountPriceType;
    }
}
