package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 商品可用活动入参
 *
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/12/4 20:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class CommodityActivityRequest extends AbstractObject {
    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("用户类型")
    private String userType;
    @ApiModelProperty("用户分组id List")
    private List<Long> groupIdList;
    @ApiModelProperty("渠道")
    private String clientTenant;

    @ApiModelProperty("租户")
    private String tenantId;
    @ApiModelProperty("应用")
    private Long appId;
    @ApiModelProperty("优惠券")
    private Long couponId;

    @ApiModelProperty("商品列表")
    private ActivityCommodityDTO commodityDTO;

    @ApiModelProperty("商店code")
    private String storeCode;

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("是否被邀请 ‘true’|‘false’")
    private String isInvited;
}
