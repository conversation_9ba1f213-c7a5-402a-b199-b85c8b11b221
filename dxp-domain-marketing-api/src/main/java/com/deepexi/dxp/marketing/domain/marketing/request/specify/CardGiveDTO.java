package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 集卡活动-赠送卡片结果
 * <AUTHOR>
 * @date 2021/12/27
 */
@ApiModel(value = "集卡活动-赠送卡片", description = "集卡活动-赠送卡片")
@Data
public class CardGiveDTO implements Serializable {

    @ApiModelProperty(value = "参与记录id")
    private Long partakeLogId;

    @ApiModelProperty(value = "资源id")
    private Long assistResourceId;
    /**
     * 奖品Id
     */
    @TableField(value = "`resource_id`")
    private Long resourceId;

    @TableField(value = "`ladder_sort`")
    private Integer ladderSort;

    @ApiModelProperty(value = "卡片名称")
    private String cardName;

    @ApiModelProperty(value = "卡片图片")
    private String cardImg;

}
