package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


/**
 * 抽奖活动奖品配置保存dto
 * <AUTHOR>
 */
@ApiModel
@Data
public class PrizeConfigVO extends AbstractObject {

    @ApiModelProperty("资源表主键id")
    private Long id;

    @ApiModelProperty("资源id")
    @NotNull(message = "资源id不能为空")
    private Long resourceId;

    @ApiModelProperty("资源发放数量")
    @Max(value = 999999,message = "资源发放数量不能超过{value}")
    private Integer issuedQuantity;

    @ApiModelProperty("每天发放上限")
    //@NotNull(message = "每天发放上限不能为空")
    @Max(value = 999999,message = "每天发放上限不能超过{value}")
    private Integer issuanceCap;

    @ApiModelProperty("中奖率")
    //@NotNull(message = "中奖率不能为空")
    @Max(value = 100,message = "中奖率不能超过{value}")
    @Min(value = 0)
    private Double oddsOfWinning;

    @ApiModelProperty("限制（1限制用户中奖1次，0不限中奖次数）")
    //@NotNull(message = "限制类型不能为空")
    @Max(value = 1,message = "限制类型有误")
    @Min(value = 0)
    private Integer limitType;

    /* ******************* 营销类资源相关属性 start ******************* */
    @ApiModelProperty("每人限制领取/购买次数")
    private Integer limitTimes;

    @ApiModelProperty("价格")
    private BigDecimal purchasePrice;

    //优惠券活动 特有
    @ApiModelProperty("领取方式:0-免费领取,1-付费领取")
    private Integer receiveMode;
    //优惠券活动 特有

    @ApiModelProperty("助力人数/累积签到次数")
    private Integer fissonCount;

    @ApiModelProperty("助力资源类型:0-助力阶梯,1-分享获奖,2-帮忙助力获奖")
    private Integer fissonResourceType;

    @ApiModelProperty("阶梯序号")
    private Integer ladderSort;

    //红包、话费需要的核销属性
    @ApiModelProperty("项目ID")
    private String projectId;

    //红包、话费需要的核销属性
    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目类型:0-活动项目,1-系统项目")
    private Integer sysType;
    /* ******************* 营销类资源相关属性 end ******************* */
    @ApiModelProperty("资源已发放数量")
    private Integer issuedUsedQuantity = 0;
}
