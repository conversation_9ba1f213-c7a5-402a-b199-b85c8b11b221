package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @Class: MarketingMappingQuery
 * @Description:
 * @Author: zht
 * @Date: 2020/10/12
 */
@Data
@ToString
@ApiModel
public class MarketingTaskResourceMappingQuery extends SuperQuery {

    @ApiModelProperty(value = "主键id列表")
    private List<Long> ids;
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "任务id列表")
    private List<Long> taskIds;

    /**
     * 任务类型1:主动 2：自动 3:画布
     */
    @ApiModelProperty(value = "任务类型1:主动 2：自动 3:画布")
    private Integer taskType;

    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源id")
    private Long resourceId;

    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源id列表")
    private List<Long> resourceIds;

    /**
     * 资源渠道
     */
    @ApiModelProperty(value = "资源渠道")
    private String resourceChannel;

    /**
     * 短链接
     */
    @ApiModelProperty(value = "短链接")
    private String shortURL;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "查询是否含资源的mapping")
    private Boolean hasResource;
}
