package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "活动分析活动趋势返回对象", description = "活动分析活动趋势返回对象")
public class ActivityTrendResponseDTO {

    @ApiModelProperty(value = "指标")
    private String indexPath;

    @ApiModelProperty(value = "时间")
    private Integer date;

    @ApiModelProperty(value = "人数")
    private Long peopleNumber;

    private String createdTime;
}
