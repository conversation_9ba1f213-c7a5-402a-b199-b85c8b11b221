package com.deepexi.dxp.marketing.domain.promotion.dto.coupon;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 优惠券使用统计DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class PromotionCouponUsingStatisticsResponseDTO extends AbstractObject implements Serializable {

    private static final long serialVersionUID = -287335560926910814L;

    /**
     * 来源 id
     */
    @ApiModelProperty("来源 id")
    private Long releaseId;

    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    private Integer releaseType;

    /**
     * 待核销数量
     */
    @ApiModelProperty(value = "待核销数量")
    private Integer unused;

    /**
     * 已核销数量
     */
    @ApiModelProperty(value = "已核销数量")
    private Integer used;

    /**
     * 已过期数量
     */
    @ApiModelProperty(value = "已过期数量")
    private Integer overtime;

    /**
     * 已锁定数量
     */
    @ApiModelProperty(value = "已锁定数量")
    private Integer lock;


}
