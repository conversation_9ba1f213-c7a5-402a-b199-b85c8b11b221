package com.deepexi.dxp.marketing.domain.marketing.response;


import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ActivityPageViewVisitorsDTO {

    /**
     * 活动id
     */
    private String distinctId;
    /**
     * 活动id
     */
    private String activityId;

    /**
     * 项目所在省份
     */
    private String province;

    /**
     * 渠道
     */
    private String platformType;
    /**
     * 访问次数
     */
    private Long quantity;
    /**
     * 日期
     */
    private String date;
    /**
     * 渠道id
     */
    private Integer channelId;
}
