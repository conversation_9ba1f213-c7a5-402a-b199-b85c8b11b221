package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.common.base.dto.SuperExtDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 抽奖活动保存dto
 * <AUTHOR>
 */
@ApiModel
@Data
public class LuckyDrawRequestDTO extends SuperExtDTO {

    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;

    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    @NotNull(message = "模板id不能为空")
    private Integer paTemplateId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotBlank(message = "活动名称不能为空")
    @Length(max = 20,message = "活动名称不能超过20个字符")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private Integer status;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endTime;

    @ApiModelProperty(value = "适用项目ids")
    @NotNull(message = "适用项目ids不能为空")
    private List<ActivityParticipationVO> projectIds;

//    @ApiModelProperty(value = "活动扩展信息")
//    @NotNull(message = "活动扩展信息不能为空")
//    @Valid
//    private ActivityExtVO activityExtVO;
    /**
     * 规则配置
     */
    @ApiModelProperty(value = "规则配置")
    @NotNull(message = "规则配置不能为空")
    @Valid
    private RuleConfigVO ruleConfigVO;

    @ApiModelProperty(value = "奖品配置")
    @NotNull(message = "奖品配置不能为空")
    @Valid
    private List<PrizeConfigVO> prizeList;

    /**
     * 界面配置
     */
    @ApiModelProperty(value = "活动页配置")
    @NotNull(message = "活动页配置不能为空")
    @Valid
    private ActivityPageVO activityPageVO;


    @ApiModelProperty(value = "分享页配置")
    @NotNull(message = "分享页配置不能为空")
    @Valid
    private ActivityPageShareVO activityPageShareVO;
}
