package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ComdityLimitDTO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;


/**
 * //优惠券限制
 *
 * <AUTHOR> ming.zhong
 * @date created in 17:49 2019/12/3
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@ApiModel
public class CouponLimitDTO extends AbstractObject {
    @ApiModelProperty("时间限制")
    private ActivityRuleDTO timeLimit;

    @ApiModelProperty("商品限制")
    private ComdityLimitDTO commodity;

    @ApiModelProperty("优惠券叠加活动限制：true：叠加使用，false：不能叠加使用")
    private List<BaseActivityDTO> couponLimit;
}
