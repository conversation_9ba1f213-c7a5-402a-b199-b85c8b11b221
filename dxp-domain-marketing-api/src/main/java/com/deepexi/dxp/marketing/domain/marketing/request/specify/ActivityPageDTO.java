package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 活动页
 * <AUTHOR>
 */
@ApiModel(value = "ActivityPageDTO入参对象", description = "ActivityPageDTO返回对象")
@Data
public class ActivityPageDTO extends SuperDTO {

private static final long serialVersionUID = 1L;


    /**
     * 基础配置通用ID
     */
    @ApiModelProperty(value = "基础配置通用ID")
    private Long activityId;

    /**
     * 活动图标
     */
    @ApiModelProperty(value = "活动图标")
    private String activityIconUrl;

    /**
     * 表单背景图 0、隐藏,1、显示
     */
    @ApiModelProperty(value = "表单背景图 0、隐藏,1、显示")
    private Integer isFormBackground;

    /**
     * 背景图
     */
    @ApiModelProperty(value = "背景图")
    private String backGroundUrl;

    /**
     * 表单头图 0、隐藏,1、显示
     */
    @ApiModelProperty(value = "表单头图 0、隐藏,1、显示")
    private Integer isHead;

    /**
     * 表单头图
     */
    @ApiModelProperty(value = "表单头图")
    private String headUrl;

    /**
     * 活动图
     */
    @ApiModelProperty(value = "活动图")
    private String activityDiagram;

    /**
     * 活动规则图
     */
    @ApiModelProperty(value = "活动规则图")
    private String activityRulesUrl;

    /**
     * 底部按钮 1、项目详情, 2、联系销售,3、联系电话,4、分享,5、生成海报
     */
    @ApiModelProperty(value = "底部按钮 1、项目详情, 2、联系销售,3、联系电话,4、分享,5、生成海报")
    private String bottomBtnType;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 邀请助力按钮文案
     */
    @ApiModelProperty(value = "邀请助力按钮文案")
    private String inviteAssistBtnText;

    /**
     * 邀请助力按钮颜色 0 、蓝色,1、红色
     */
    @ApiModelProperty(value = "邀请助力按钮颜色 0 、蓝色,1、红色")
    private Integer inviteColorType;

    /**
     * 参与助力按钮文案
     */
    @ApiModelProperty(value = "参与助力按钮文案")
    private String partakeAssistText;

    /**
     * 参与助力按钮颜色 0 、蓝色,1、红色
     */
    @ApiModelProperty(value = "参与助力按钮颜色 0 、蓝色,1、红色")
    private Integer partakeColorType;

    /**
     * 排行榜 0关闭,1开启
     */
    @ApiModelProperty(value = "排行榜 0关闭,1开启")
    private Integer isShowRanklist;

    /**
     * 排行榜图片
     */
    @ApiModelProperty(value = "排行榜图片")
    private String ranklistUrl;

    /**
     * 0、基础配置,1、活动专题
     */
    @ApiModelProperty(value = "0、基础配置,1、活动专题")
    private Integer type;


}