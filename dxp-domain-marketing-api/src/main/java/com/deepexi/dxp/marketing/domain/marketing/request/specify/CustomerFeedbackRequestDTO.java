package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * CustomerFeedbackDTO入参对象
 * <AUTHOR>
 */
@ApiModel(value = "CustomerFeedbackDTO入参对象", description = "CustomerFeedbackDTO入参对象")
@Data
public class CustomerFeedbackRequestDTO  extends SuperDTO {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    //@NotNull(message = "项目ID不能为空")
    private String projectId;
    /**
     *项目名称
     */
    @ApiModelProperty(value = "项目名称")
    //@NotBlank(message = "项目名称不能为空")
    @Length(max = 32, message = "项目名称不能超过32个字")
    private String projectName;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID",required = true)
    @NotNull(message = "用户ID不能为空")
    private String userId;
    /**
     * 微信昵称
     */
    @ApiModelProperty(value = "微信昵称")
    private String nickName;
    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号",required = true)
//    @NotBlank(message = "手机号不能为空")
    private String phone;
    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID",required = true)
    @NotNull(message = "活动ID不能为空")
    private Long activityId;
    /**
     *资源奖品ID
     */
    @ApiModelProperty(value = "资源奖品ID")
    //@NotNull(message = "资源奖品ID不能为空")
    private Long resourceId;
    /**
     * 0、表单活动,1、其它活动
     */
    @ApiModelProperty(value = "活动类型",required = true)
    @NotNull(message = "活动类型不能为空")
    @Range(min = 0,max = 1 ,message = "活动类型不合法")
    private Integer activityType;
    /**
     * unionId
     */
    @ApiModelProperty(value = "unionId")
    private String unionId;
    /**
     * 0、微信小程序 1、H5  2、抖音
     */
    @ApiModelProperty(value = "来源",required = true)
    @NotNull(message = "来源不能为空")
    @Range(min = 0,max = 3 ,message = "来源不合法")
    private Integer type;

    @ApiModelProperty(value = "用户表单数据")
    private Map<String,Object> limits;

    @ApiModelProperty(value = "短信验证码")
    private String smsCode;


    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId;

}
