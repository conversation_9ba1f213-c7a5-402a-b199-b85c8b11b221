package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 裂变活动助力/砍价记录信息返回
 * <AUTHOR>
 */
@ApiModel(value = "裂变活动助力/砍价记录信息返回", description = "裂变活动助力/砍价记录信息返回")
@Data
public class ActivityFissionLogDTO extends SuperDTO {
    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 用户电话
     */
    @ApiModelProperty(value = "用户电话")
    private String phone;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal money;

    /**
     * unionId
     */
    @ApiModelProperty(value = "unionId")
    private String unionId;

    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty(value = "0、微信小程序  1、H5  2、抖音")
    private Integer type;

    /**
     * 活动发起者参与记录id
     */
    @ApiModelProperty(value="活动发起者参与记录id")
    private Long partakeLogId;

    @ApiModelProperty(value="avatar")
    private String avatar;
}
