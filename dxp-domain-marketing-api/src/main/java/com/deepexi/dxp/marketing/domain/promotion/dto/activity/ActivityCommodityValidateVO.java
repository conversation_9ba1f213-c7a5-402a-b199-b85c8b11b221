package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.domain.dto.BaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 商品已经匹配活动校验
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/12 11:08
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class ActivityCommodityValidateVO extends BaseDTO {
    private static final long serialVersionUID = -132241068293248541L;
    /**
     * 自增主键
     */
    @ApiModelProperty("自增主键")
    private Long id;


    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;


    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;


    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;

    @ApiModelProperty("商品匹配活动列表")
    private List<ActivityCommodityValidateDTO> commodityList;

    public ActivityCommodityValidateVO(List<ActivityCommodityValidateDTO> commodityList) {
        this.commodityList = commodityList;
    }
}
