package com.deepexi.dxp.marketing.domain.promotion.request.coupon;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR> ming.zhong
 * @date created in 14:49 2019/11/28
 */
@Data
@EqualsAndHashCode
@ApiModel
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PromotionCouponDeletedPostRequest extends AbstractObject {

    /**
     * 优惠券名称
     */
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 优惠券类型
     */
    @ApiModelProperty(value = "优惠券类型")
    private String couponType;

    /**
     * 代金券面值|折扣券折扣
     */
    @ApiModelProperty(value = "代金券面值|折扣券折扣")
    private BigDecimal couponValue;

    /**
     * 是否启用 0 禁用 1 启用
     */
    @NotBlank
    @ApiModelProperty(value = "是否启用 0 禁用 1 启用")
    private Integer status;

    /**
     * 使用条件适用金额
     */
    @ApiModelProperty(value = "使用条件适用金额")
    private BigDecimal condition;

    /**
     * 使用限制json
     */
    @ApiModelProperty(value = "使用限制json")
    private String limits;

    /**
     * 租户隔离
     */
    @ApiModelProperty(value = "租户隔离")
    private String tenantId;

    /**
     * 应用隔离
     */
    @ApiModelProperty(value = "应用隔离")
    private Long appId;

    /**
     * 主键
     */
    @NotNull
    private Long id;
}
