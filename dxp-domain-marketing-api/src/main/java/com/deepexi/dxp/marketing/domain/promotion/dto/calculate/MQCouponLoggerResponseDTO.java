package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.dxp.marketing.domain.promotion.request.coupon.PromotionCouponLoggerCreatePostRequest;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.util.List;

/**
 * 领券中心返回
 *
 * <AUTHOR>
 * @Date 2020/1/16 15:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@ApiModel
public class MQCouponLoggerResponseDTO extends AbstractObject {
    /**
     * 领券中心分页返回
     */
    private PageBean<CouponResponseDTO> pageBean;

    /**
     * 领券信息
     */
    private List<PromotionCouponLoggerCreatePostRequest> dtoList;
}
