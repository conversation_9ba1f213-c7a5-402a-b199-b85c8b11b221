package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * 抽奖活动信息登记保存dto
 * <AUTHOR>
 */
@ApiModel
@Data
public class EnrollmentInfoVO {
    @ApiModelProperty("输入类型（0普通文本框，1手机号码，2所在地区，3身份证,4姓名,5港澳通行证）")
    @NotNull(message = "输入类型不能为空")
    private Integer type;

    @ApiModelProperty("输入标题")
    @Length(max = 5,message = "限制最多5个字符")
    private String value;
}
