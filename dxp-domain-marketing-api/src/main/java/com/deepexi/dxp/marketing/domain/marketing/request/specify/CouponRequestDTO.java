package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * 优惠券活动保存dto
 */
@ApiModel
@Data
public class CouponRequestDTO extends SuperDTO {

    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;

    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    @NotNull(message = "模板id不能为空")
    private Integer paTemplateId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotBlank(message = "活动名称不能为空")
    @Length(max = 20,message = "活动名称不能超过20个字符")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private Integer status = 0;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endTime;

    @ApiModelProperty(value = "适用项目ids")
    //@NotNull(message = "适用项目ids不能为空")
    private List<ActivityParticipationVO> projectIds;

    /**
     * 规则配置
     */
    @ApiModelProperty(value = "规则配置")
    @NotNull(message = "规则配置不能为空")
    @Valid
    private RuleConfigVO ruleConfigVO;

    @ApiModelProperty(value = "领券/购券规则")
    @NotNull(message = "领券/购券规则配置不能为空")
    @Valid
    private List<PrizeConfigVO> prizeList;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 界面配置
     */
    @ApiModelProperty(value = "活动页配置")
    @NotNull(message = "活动页配置不能为空")
    @Valid
    private ActivityPageVO activityPageVO;


    @ApiModelProperty(value = "分享页配置")
    @NotNull(message = "分享页配置不能为空")
    @Valid
    private ActivityPageShareVO activityPageShareVO;



    @ApiModelProperty(value = "活动扩展信息")
    @NotNull(message = "活动扩展信息不能为空")
    @Valid
    private ActivityExtRequestDTO activityExtVO;

    @ApiModel
    @Data
    public static class ActivityExtRequestDTO extends AbstractObject implements Serializable {
        @ApiModelProperty(value = "活动目标")
        @NotNull(message = "活动目标不能为空")
        private Integer activityGoal;

        @ApiModelProperty(value = "指标路径")
        @NotNull(message = "指标路径不能为空")
        private Integer kpiPath;

        @ApiModelProperty(value = "活动类型（0大转盘，1九宫格）")
        //@NotNull(message = "活动类型不能为空")
        @Min(value = 0)
        @Max(value = 1,message = "活动类型输入有误")
        private Integer activityType;

        @ApiModelProperty(value = "是否热门（0否，1是）")
        @NotNull(message = "是否热门不能为空")
        @Min(value = 0)
        @Max(value = 1,message = "是否热门输入有误")
        private Integer isPopular;

        /* ****************营销类相关属性 start **************** */
        @ApiModelProperty(value = "活动关联组织id")
        private String orgId;

        @ApiModelProperty(value = "支付账户号")
        private String payNo;

        @ApiModelProperty(value="参与对象:1-全部用户")
        private Integer participants;

        @ApiModelProperty(value="资源属性:0-不限项目,1-指定项目")
        private Integer resourcesAttribute;

        @ApiModelProperty(value="投放渠道:0-不在置业通小程序展示,1-在置业通小程序-活动列表中展示")
        private Integer deliveryChannel;
        /* ****************营销类相关属性 end **************** */
    }
}
