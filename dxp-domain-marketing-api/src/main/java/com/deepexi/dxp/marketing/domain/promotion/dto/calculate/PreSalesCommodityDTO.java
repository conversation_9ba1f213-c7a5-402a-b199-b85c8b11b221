package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2020/3/10 10:26
 */

/**
 * 预售活动商品信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class PreSalesCommodityDTO extends AbstractObject {

    @ApiModelProperty("预售活动的策略类型(定金加尾款|全款)")
    private String preSalesStrategyType;

    @ApiModelProperty("商品的预售金额")
    private String preSalesMoney;


    @ApiModelProperty("定金钱")
    private BigDecimal depositPayment;
    @ApiModelProperty("尾款钱")
    private BigDecimal tailPayment;
    @ApiModelProperty("全款钱")
    private BigDecimal allPayment;

    @ApiModelProperty("定金开始支付时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date depositStartTime;

    @ApiModelProperty("定金结束支付时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date depositEndTime;


    @ApiModelProperty("定金是否可退")
    private Boolean returnDepositPaymentFlag;

    @ApiModelProperty("多久时间前需要支付尾款")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date tailTime;

    @ApiModelProperty("预售活动的商品信息")
    ActivityCommodityDTO activityCommodityDTO;
}
