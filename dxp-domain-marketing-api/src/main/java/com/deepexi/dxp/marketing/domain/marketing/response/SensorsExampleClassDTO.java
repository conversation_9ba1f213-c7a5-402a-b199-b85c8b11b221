package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.util.pojo.AbstractObject;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 神策 Response
 * <AUTHOR>
 */
@Data
public class SensorsExampleClassDTO  extends AbstractObject implements Serializable {

    private DetailResultDTO detailResult;
    private RollupResult rollupResult;
    private TotalDetailResult totalDetailResult;
    private TotalRollupResult totalRollupResult;
    private Boolean downloadTruncated;
    private String reportUpdateTime;
    private String dataUpdateTime;
    private String dataSufficientUpdateTime;
    private Boolean truncated;
    private Integer samplingFactor;

    @Data
    public static class DetailResultDTO  extends AbstractObject implements Serializable{
        private List<String> series;
        private List<Row> rows;
        private Integer numRows;
        private Integer totalRows;
        private Boolean approx;
        private Boolean downloadTruncated;
        private Boolean truncated;
    }

    @Data
    public static class RollupResult  extends AbstractObject implements Serializable{
        private List<String> series;
        private List<Row__1> rows;
        private Integer numRows;
        private Integer totalRows;
        private Boolean approx;
        private Boolean downloadTruncated;
        private Boolean truncated;
    }

    @Data
    public static class Row  extends AbstractObject implements Serializable{

        private List<List<Integer>> values;
        private List<Object> byValues;

    }
    @Data
    public static class Row__1  extends AbstractObject implements Serializable{

        private List<List<Integer>> values = new ArrayList<List<Integer>>();
        private List<Object> byValues = new ArrayList<Object>();
        private List<List<Integer>> avgValues = new ArrayList<List<Integer>>();
        private List<List<Integer>> minValues = new ArrayList<List<Integer>>();
        private List<List<Integer>> maxValues = new ArrayList<List<Integer>>();

    }

    @Data
    public static class Row__2  extends AbstractObject implements Serializable{

        private List<List<Integer>> values = new ArrayList<List<Integer>>();
        private List<Object> byValues = new ArrayList<Object>();
    }

    @Data
    public static class Row__3  extends AbstractObject implements Serializable{

        private List<List<Integer>> values = new ArrayList<List<Integer>>();
        private List<String> byValues = new ArrayList<String>();
    }

    @Data
    public static class TotalDetailResult extends AbstractObject implements Serializable {

        private List<String> series = new ArrayList<String>();
        private List<Row__2> rows = new ArrayList<Row__2>();
        private Integer numRows;
        private Integer totalRows;
        private Boolean approx;
        private Boolean downloadTruncated;
        private Boolean truncated;
    }

    @Data
    public static class TotalRollupResult  extends AbstractObject implements Serializable{

        private List<String> series = new ArrayList<String>();
        private List<Row__3> rows = new ArrayList<Row__3>();
        private Integer numRows;
        private Integer totalRows;
        private Boolean approx;
        private Boolean downloadTruncated;
        private Boolean truncated;
    }
}
