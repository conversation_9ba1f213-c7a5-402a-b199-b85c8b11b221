package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperExtVO;
import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageShareVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动分组返回
 * <AUTHOR>
 */
@ApiModel(value = "活动分组返回参数", description = "活动分组返回参数")
@Data
public class ActivityGroupResponseDTO extends SuperExtVO {
    /**
     * 活动专题名称
     */
    @ApiModelProperty("活动专题名称")
    private String name;

    @ApiModelProperty("关联活动数量")
    private Integer activityNumber;

    @ApiModelProperty("页面排列方式(0：区域展示，1：列表展示)")
    private Integer arrangement;

    @ApiModelProperty("场景码")
    private String sceneCode;

    @ApiModelProperty("组织机构类型")
    private Integer orgType;

    /**
     * 0、活动组,1、活动专题
     */
    @ApiModelProperty("0、活动组,1、活动专题")
    private Integer type;

    @ApiModelProperty("活动页信息")
    private ActivityPageVO activityPageVO;

    @ApiModelProperty("分享页信息")
    private ActivityPageShareVO activityShareVO;

}
