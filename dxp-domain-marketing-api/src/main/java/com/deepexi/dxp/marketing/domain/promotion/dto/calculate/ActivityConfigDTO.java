package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityUserRelatedDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityParticipationDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ComdityLimitDTO;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 活动配置dto列表
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/22 16:27
 */
@Data
@ToString
@ApiModel
public class ActivityConfigDTO extends AbstractObject {

    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;

    @ApiModelProperty(value="适用项目类型（1-选择项目，2全国项目）")
    private Integer projectType;

    @ApiModelProperty(value="会员时间类型（1-不限（默认），2-活动期间）")
    private Integer memberTimeType;

    @ApiModelProperty(value="适用项目列表")
    private List<ActivityParticipationDTO> projectList;

    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId;
    @ApiModelProperty("0 全款可退 1 定金可退 2 不可退 （默认是0）")
    private Integer returnMoneyType;
    @ApiModelProperty("活动id")
    private Long activityId;
    @ApiModelProperty("活动名称")
    private String activityName;
    @ApiModelProperty("活动描述")
    private String description;
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;
    @ApiModelProperty("结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;
    @ApiModelProperty("活动状态 2=进行中")
    private Integer status;
    @ApiModelProperty("商品限制")
    private ComdityLimitDTO commodityList;
    @ApiModelProperty("策略规则")
    private List<ActivityRuleDTO> activityRuleDTOList;
    @ApiModelProperty("渠道限制")
    private List<BaseActivityDTO> tenantLimit;
    @ApiModelProperty("用户限制")
    private List<BaseActivityDTO> userLimit;
    @ApiModelProperty("数量限制")
    private List<BaseActivityDTO> numberLimit;
    @ApiModelProperty("优惠券限制")
    private List<BaseActivityDTO> couponLimit;
    @ApiModelProperty("门店限制")
    private List<BaseActivityDTO> shopLimit;
    @ApiModelProperty("抽奖限制")
    private List<BaseActivityDTO> luckyDrawLimit;
    @ApiModelProperty("助力活动限制")
    private List<BaseActivityDTO> assistLimit;
    @ApiModelProperty("缓存用户信息")
    private ActivityParamsDTO params;
    @ApiModelProperty("活动用户关联信息")
    private ActivityUserRelatedDTO userRelated;

}
