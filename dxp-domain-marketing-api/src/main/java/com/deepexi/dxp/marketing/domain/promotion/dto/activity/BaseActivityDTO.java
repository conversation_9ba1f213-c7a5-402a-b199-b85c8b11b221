package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * 一种条件
 *
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/11/22 16:20
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
@ToString
@NoArgsConstructor
public class BaseActivityDTO extends AbstractObject implements Serializable {

    private static final long serialVersionUID = 2741441540262303511L;
    @ApiModelProperty("条件id")
    private String id;
    @ApiModelProperty("条件的值")
    private String value;
    @ApiModelProperty("条件的符号")
    private String flag;
    @ApiModelProperty("条件的el表达式")
    private String elExpression;

    public BaseActivityDTO(String id, String value, String flag) {
        this.id = id;
        this.value = value;
        this.flag = flag;
    }
}
