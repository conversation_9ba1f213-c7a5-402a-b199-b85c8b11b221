package com.deepexi.dxp.marketing.domain.promotion.request.calculate;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.util.domain.dto.BaseExtDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 订单计算入参
 *
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/11/29 17:30
 */

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class ActivityOrderParamsRequest extends BaseExtDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    @NotEmpty(message = "用户id不能为空")
    private String userId;

    @ApiModelProperty("用户类型")
    //@NotBlank(message = "用户类型不能为空")
    private String userType;

    @ApiModelProperty("用户分组id List")
    //@NotNull(message = "用户分组不能为空")
    private List<Long> groupIdList;

    @ApiModelProperty("渠道")
    private String clientTenant;

    @ApiModelProperty("活动商品")
    //@Valid
    private List<ActivityCommodityDTO> activityCommodityDTOList;

    @ApiModelProperty("购物券")
    private List<Long> couponUserList;

    @ApiModelProperty("商店code")
    private String storeCode;

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("活动类型")
    private Integer activityType;
}
