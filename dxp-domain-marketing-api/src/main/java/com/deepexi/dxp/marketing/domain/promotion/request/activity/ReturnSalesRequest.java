package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ReturnSalesCommodityDTO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 退货商品更新日志入参
 *
 * <AUTHOR> xinjian.yao
 * @date 2019/12/6 10:57
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class ReturnSalesRequest extends AbstractObject implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "是否归还coupon")
    private Boolean ifReturnCoupon;
    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;

    /**
     * 会员id
     */
    @ApiModelProperty(value = "会员id")
    private Long userId;

    /**
     * 用户类型
     */
    @ApiModelProperty(value = "用户类型")
    private Integer userType;
    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    private String followNumber;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty("商品列表")
    private List<ReturnSalesCommodityDTO> returnSalesCommodityDTOList;

}
