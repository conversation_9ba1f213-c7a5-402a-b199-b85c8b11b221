package com.deepexi.dxp.marketing.domain.promotion.query.activity;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动查询DTO
 *
 * <AUTHOR> xinjian.yao
 * @date 2019/11/21 14:08
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class PromotionActivityQuery extends AbstractObject implements Serializable {

    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;
    /**
     * 主键ids
     */
    @ApiModelProperty(value = "主键id")
    private List<Long> ids;

    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value = "模板id")
    private Integer paTemplateId;
    @ApiModelProperty("活动类型")
    private List<Integer> paTemplateList;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;


    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态,1-未开始，2-进行中，3-已结束")
    private Integer status;

    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private List<Integer> statusList;
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endTime;

    /**
     * 活动开始窗口时间
     */
    @ApiModelProperty(value = "活动开始窗口时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String windowStart;

    /**
     * 活动结束窗口时间
     */
    @ApiModelProperty(value = "活动结束窗口时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String windowEnd;


    /**
     * 查询的页码(-1 表示查询所有数据)
     */
    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;

    /**
     *项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "指标路径id")
    private Long kpiPath;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "城市id")
    private String cityId;
    @ApiModelProperty(value = "城市公司id")
    private List<String> companyIds;
    private List<String> projectIds;

    @ApiModelProperty(value="投放渠道:0-置业通小程序展示,2-抖音小程序展示，4－客服小程序，6-社群小程序")
    private Integer deliveryChannel;

    @ApiModelProperty(value="上下架状态:0-下架,1-上架")
    private Integer upperStatus;

    @ApiModelProperty(value="社群id")
    private Integer communityId;
    @ApiModelProperty(value="标签名称")
    private List<String> tagNames;
    @ApiModelProperty(value="查询类型：1-我参与的，2-我发起的，3-已取消的")
    @Min(1)@Max(3)
    private int queryType = 1;

    public List<String> getProjectIds() {
        //需要排除掉空字符串的元素
        if (projectIds != null) {
            return projectIds.stream().filter(s -> !"".equals(s)).collect(Collectors.toList());
        }
        return null;
    }
    public List<String> getCompanyIds() {
        //需要排除掉空字符串的元素
        if (companyIds != null) {
            return companyIds.stream().filter(s -> !"".equals(s)).collect(Collectors.toList());
        }
        return null;
    }
}