package com.deepexi.dxp.marketing.domain.promotion.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 通用渠道自定义
 */
@Data
@ApiModel
public class ActivityPromotionChannelCreateRequestDTO extends AbstractObject {


    @ApiModelProperty(value = "渠道类型:0、微信小程序,1、微信内网页,2、抖音小程序",required = true)
    @NotNull(message = "渠道类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "推广类型:0、活动推广,1、活动专题推广",required = true)
    @NotNull(message = "推广类型不能为空")
    private Integer promotionType;

    @ApiModelProperty(value = "活动id",required = true)
    @NotNull(message = "活动id不能为空")
    private Long activityId;

    @ApiModelProperty("活动类型")
    private Integer paTemplateId;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value ="码类型",required = true)
    private String codeType;

    @ApiModelProperty(value ="渠道名称",required = true)
    private String channelName;

    @ApiModelProperty(value = "小程序码失效时间")
    private Date invalidTime;
    /**
     * 是否自定义:0-默认1-自定义
     */
    @ApiModelProperty(value = "是否自定义:0-默认1-自定义")
    private Integer custom = 1;
    @ApiModelProperty("选择项目列表")
    private List<ChannelProjectDTO> channelProjectList;


    @ApiModelProperty("机构类型，1全国，2区域，3城市，4项目，新增时必填")
    @NotNull(message = "机构类型")
    private Integer orgType;

    @Data
    @ApiModel
    public static class ChannelProjectDTO extends AbstractObject {

        @ApiModelProperty(value = "区域ID")
        private String areaId;

        @ApiModelProperty(value = "区域名称")
        private String areaName;

        @ApiModelProperty(value = "城市id")
        private String cityId;

        @ApiModelProperty(value = "城市名称")
        private String cityName;

        @ApiModelProperty(value = "城市id")
        private String realCityId;

        @ApiModelProperty(value = "城市名称")
        private String realCityName;

        @ApiModelProperty(value = "项目ID")
        private String projectId;

        @ApiModelProperty(value = "项目名称")
        private String projectName;
    }
}
