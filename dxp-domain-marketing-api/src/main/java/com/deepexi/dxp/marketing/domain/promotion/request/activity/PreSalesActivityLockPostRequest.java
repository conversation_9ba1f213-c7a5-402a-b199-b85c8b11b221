package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2020/3/12 11:02
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
@ToString
public class PreSalesActivityLockPostRequest extends SuperRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 订单第一次支付时间 用于预售活动的定金支付，或者全款支付 定金支付时间不能为空,如果是全款 当前时间等于全款支付时间
     */
    private Date firstPayTime;

    /**
     * 订单最后支付时间 用于预售活动的尾款支付
     */
    private Date endPayTime;

    /**
     * 多久时间前需要支付尾款 多久前需要支付尾款，全款支付时间当前时间可以为全款支付时间
     */
    private Date finalPaymentTime;

    /**
     * 0 锁定状态 1 支付完成状态
     */
    @ApiModelProperty(value = "0 锁定状态 1 支付完成状态 2 定金支付状态")
    private Integer status;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    @NotBlank(message = "订单号不可为null")
    private String orderNumber;

}
