package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityFissionReducePriceDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageShareDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.PromotionHisResourceDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "砍价首页信息返回对象", description = "砍价首页信息返回对象")
public class BargainHomePageInfoResponseDTO {

    /**
     * 活动详情
     */
    private PromotionActivityResponseDTO promotionActivity;
    /**
     * 奖品列表
     */
    private List<PromotionHisResourceDTO> prizeList;

    @ApiModelProperty(value = "活动页信息")
    private ActivityPageDTO activityPageDTO;

    @ApiModelProperty(value = "分享页信息")
    private ActivityPageShareDTO activityPageShareDTO;

    /**
     * 是否显示切换按钮
     */
    private Boolean isShowSwitch;

    /**
     * 发起者信息
     */
    private ActivityFissionReducePriceDTO fissionReducePrice;
}
