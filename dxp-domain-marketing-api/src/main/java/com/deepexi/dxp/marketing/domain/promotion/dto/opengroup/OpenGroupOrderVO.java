package com.deepexi.dxp.marketing.domain.promotion.dto.opengroup;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.GroupInfoDTO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 传输下单返回相关参数的DTO
 *
 * <AUTHOR> xianfeng.cai
 * @date 2019/11/29 14:08
 */
@Data
@ApiModel
public class OpenGroupOrderVO extends AbstractObject {

    @ApiModelProperty("拼团基本字段信息")
    private PromotionOpenGroupDetailResponseDTO promotionOpenGroupDetailDTO;

    @ApiModelProperty("拼团基本字段信息")
    private GroupInfoDTO groupInfoDTO;

    @ApiModelProperty("布尔结果返回")
    private Boolean boolResult;
}