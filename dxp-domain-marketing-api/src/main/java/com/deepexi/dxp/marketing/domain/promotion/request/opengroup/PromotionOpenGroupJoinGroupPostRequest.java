package com.deepexi.dxp.marketing.domain.promotion.request.opengroup;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户参团入参DTO
 *
 * <AUTHOR> xianfeng.cai
 * @date 2019/11/29 14:08
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionOpenGroupJoinGroupPostRequest extends SuperDTO {

    @ApiModelProperty("活动id")
    private Long activityId;
    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;
    @ApiModelProperty("参团id")
    private Long activityGroupId;
    @ApiModelProperty("参团人员")
    private Long userId;
    @ApiModelProperty("订单code")
    private String orderCode;
    @ApiModelProperty("下单数量")
    private Integer orderNum;

}