package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "用户所有菜单权限", description = "用户所有菜单权限")
public class MenuPermissionDTO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "权限字段代码")
    private String fieldId;

    @ApiModelProperty(value = "权限字段名称")
    private String fieldName;

    @ApiModelProperty(value = "权限字段代码")
    private String objectId;

    @ApiModelProperty(value = "权限对象名称")
    private String objectName;

    @ApiModelProperty(value = "权限字段代码")
    private String wdaId;
}
