package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
public class AccountQueryVo {
      @ApiModelProperty(value = "openId")
      private String openId;
      @ApiModelProperty(value = "头像")
      private String profilePicture;
      @ApiModelProperty(value = "昵称")
      private String nickname;
      @ApiModelProperty(value = "电话")
      private String mobile;
      @ApiModelProperty(value = "微信unionId")
      private String unionId;
      @ApiModelProperty(value = "注册渠道")
      private String registerChannel;

}
