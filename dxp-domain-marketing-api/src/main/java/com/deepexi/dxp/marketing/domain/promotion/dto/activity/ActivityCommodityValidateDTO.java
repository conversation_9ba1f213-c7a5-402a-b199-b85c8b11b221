package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel
public class ActivityCommodityValidateDTO extends AbstractObject implements Serializable {
    private static final long serialVersionUID = 8936183114746936328L;
    @ApiModelProperty("sku编码")
    private String skuCode;

    @ApiModelProperty("店铺ID")
    private String shopId;

    @ApiModelProperty("该商品是否已经匹配活动")
    private Boolean matchedActivity;

}
