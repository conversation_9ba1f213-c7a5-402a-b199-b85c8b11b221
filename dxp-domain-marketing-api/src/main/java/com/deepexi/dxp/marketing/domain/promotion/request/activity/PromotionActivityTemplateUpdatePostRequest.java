package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.util.domain.request.BaseExtRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 修改活动模板入参
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/21 10:16
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class PromotionActivityTemplateUpdatePostRequest extends BaseExtRequest {


    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String name;

    /**
     * 模板名字
     */
    @ApiModelProperty(value = "模板名字")
    private String code;

    /**
     * 模板描述
     */
    @ApiModelProperty(value = "模板描述")
    private String description;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 策略类型json数据
     */
    @ApiModelProperty(value = "策略类型json数据")
    private String strategy;

    /**
     * 限制json数据
     */
    @ApiModelProperty(value = "限制json数据")
    private String rulesOfLimit;


    /**
     * 自增主键
     */
    @ApiModelProperty(value = "自增主键")
    private Long id;

    /**
     * 租户编码
     */
    @ApiModelProperty(value = "租户编码")
    private String tenantId;

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    private Long appId;

    /**
     * 版本号，乐观锁
     */
    @ApiModelProperty(value = "版本号，乐观锁")
    private Long version;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 逻辑删除
     */
    @ApiModelProperty(value = "逻辑删除")
    private Integer deleted = 0;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
