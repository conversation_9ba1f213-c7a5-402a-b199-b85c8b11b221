package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 流程画布-条件节点—指标数据配置
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
@NoArgsConstructor
public class FlowCanvasConditionKpiItemsQuery extends SuperQuery {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 主键id列表
     */
    private List<Long> ids;

    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    private String nodeId;

    /**
     * 判断条件名称
     */
    private String name;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 指标ID
     */
    private Long kpiItemsId;

    /**
     * 指标名称
     */
    private String kpiItemsName;

    /**
     * 指标单位
     */
    private String kpiItemsUnit;

    /**
     * 计算符号
     */
    private String symbols;

    /**
     * 比对值
     */
    private String value;

    /**
     * 比对值-结束
     */
    private String valueEnd;

    public FlowCanvasConditionKpiItemsQuery (Long flowId, String tenantId, Long appId){
        this.flowId = flowId;
        this.setTenantId(tenantId);
        this.setAppId(appId);
    }
}
