package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 活动分组添加请求
 * <AUTHOR>
 */
@ApiModel(value = "活动分组添加入参", description = "活动分组添加入参")
@Data
public class ActivityGroupRequestDTO extends SuperDTO {
    /**
     * 活动专题名称
     */
    @ApiModelProperty(value = "活动专题名称",required = true)
    @NotBlank(message = "活动组名称不能为空")
    @Size(max = 20,message = "活动组名称不能超过20个字符")
    private String name;

    @ApiModelProperty(value = "活动列表",required = true)
    @NotNull(message = "活动不能为空")
    @Valid
    private List<ActivityGroupRelatedRequestDTO> activityList;

    /**
     * 投放渠道
     */
    @ApiModelProperty("投放渠道")
    private Integer channelType;

    @ApiModelProperty("userId")
    private String userId;
}
