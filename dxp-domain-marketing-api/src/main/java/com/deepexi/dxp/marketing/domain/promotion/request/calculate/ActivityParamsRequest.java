package com.deepexi.dxp.marketing.domain.promotion.request.calculate;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.util.pojo.AbstractObject;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import java.util.List;

/**
 * 参加活动详情，请求入参DTO
 *
 * <AUTHOR> xinjian.yao
 * @date 2019/11/25 10:32
 */

@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class ActivityParamsRequest extends AbstractObject {

    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("用户类型")
    private String userType;
    @ApiModelProperty("用户分组id List")
    private List<Long> groupIdList;
    @ApiModelProperty("渠道")
    private String clientTenant;

    @ApiModelProperty("租户")
    private String tenantId;
    @ApiModelProperty("应用")
    private Long appId;
    @ApiModelProperty("优惠券")
    private Long couponId;

    @ApiModelProperty("商品列表")
    private List<ActivityCommodityDTO> commodities;
    /**
     * 待验证的限制列表,默认是ALL=全部校验，NONE=不校验，COMMODITY=商品，NUMBER=数量，TENANT=渠道，USER=用户，SHOP=门店
     */
    @ApiModelProperty("待验证的限制列表")
    private List<String> limitList = Lists.newArrayList("ALL");

    @ApiModelProperty("商店Id")
    private Long shopId;
    @ApiModelProperty("活动id")
    private Long activityId;
    @ApiModelProperty("是否被邀请 ‘true’|‘false’")
    private String isInvited;

    /**
     * 查询的页码(-1 表示查询所有数据)
     */
    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;
}
