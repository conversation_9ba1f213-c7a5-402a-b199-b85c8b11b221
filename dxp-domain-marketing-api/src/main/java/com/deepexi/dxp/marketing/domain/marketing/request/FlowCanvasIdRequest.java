package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/18
 */
@Data
public class FlowCanvasIdRequest extends AbstractObject {

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true)
    private String tenantId;

    @ApiModelProperty(value = "应用Id", required = true)
    private Long appId;

    @ApiModelProperty(value = "主键id", required = true)
    @NotNull(message = "主键id不能为空")
    private Long id;

    @ApiModelProperty(value = "画布实例id列表")
    private List<Long> instIds;
}
