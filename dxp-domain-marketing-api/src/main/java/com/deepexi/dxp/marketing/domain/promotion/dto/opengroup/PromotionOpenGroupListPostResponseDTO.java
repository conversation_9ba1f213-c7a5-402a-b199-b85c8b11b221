package com.deepexi.dxp.marketing.domain.promotion.dto.opengroup;

import com.deepexi.util.domain.dto.BaseExtDTO;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 开团活动列表返回DTO
 *
 * <AUTHOR> xianfeng.cai
 * @date 2019/11/29 14:08
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionOpenGroupListPostResponseDTO extends BaseExtDTO {

    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;

    @ApiModelProperty("活动id")
    private Long activityId;
    @ApiModelProperty("参团id")
    private Long activityGroupId;
    @ApiModelProperty("成团人数")
    private Integer peopleNumber;
    @ApiModelProperty(value = "参团订单信息")
    private String orderDetail;
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;
    @ApiModelProperty(value = "拼团状态;0-待成团，1-已成团，2-超时取消")
    private Integer status;
    @ApiModelProperty("开团类型；1-普通团，2-老带新")
    private Integer groupType;
    @ApiModelProperty("活动价格")
    private BigDecimal activityPrice;
    @ApiModelProperty("团长价格")
    private BigDecimal discountPrice;
    @ApiModelProperty("团长优惠开关；1是 0否")
    private Integer discountFlag;

}