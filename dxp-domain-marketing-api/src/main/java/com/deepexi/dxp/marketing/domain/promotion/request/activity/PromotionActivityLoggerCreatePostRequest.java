package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.util.domain.request.BaseExtRequest;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * //促销活动日志
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/21 16:31
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
@ToString
public class PromotionActivityLoggerCreatePostRequest extends BaseExtRequest {

    /**
     * 订单第一次支付时间 用于预售活动的定金支付，或者全款支付
     */
    private Date firstPayTime;

    /**
     * 订单最后支付时间 用于预售活动的尾款支付
     */
    private Date endPayTime;
    /**
     * 用户活动记录code
     */
    private String code;
    /**
     * 多久时间前需要支付尾款
     */
    private Date finalPaymentTime;
    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    @NotNull(message = "活动id不可为空")
    private Long activityId;

    /**
     * 会员id
     */
    @ApiModelProperty(value = "会员id")
    @NotNull(message = "用户id不可为空")
    private Long userId;

    /**
     * 订单商品内容
     */
    @ApiModelProperty(value = "订单商品内容")
    private String orderDetail;

    /**
     * 订单商品内容
     */
    @ApiModelProperty(value = "订单商品内容")
    @NotNull(message = "订单商品不可为空")
    private Map<String, Object> orderDetailJson;


    /**
     * 0 锁定状态 1 支付完成状态
     */
    @ApiModelProperty(value = "0 锁定状态 1 支付完成状态")
    @NotNull(message = "状态不可为空")
    private Integer status;

    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    private String followNumber;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    @NotBlank(message = "订单号不可为null")
    private String orderNumber;


    /**
     * 用户类型
     */
    @ApiModelProperty(value = "用户类型")
    @NotNull(message = "用户类型不可为空")
    private Integer userType;

    /**
     * 自增主键
     */
    @ApiModelProperty(value = "自增主键")
    private Long id;

    /**
     * 租户编码
     */
    @ApiModelProperty(value = "租户编码")
    @NotBlank(message = "租户id不可为空")
    private String tenantId;

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    @NotNull(message = "应用id不可为空")
    private Long appId;

    /**
     * 版本号，乐观锁
     */
    @ApiModelProperty(value = "版本号，乐观锁")
    private Long version;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 逻辑删除
     */
    @ApiModelProperty(value = "逻辑删除")
    private Integer deleted = 0;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


    @ApiModelProperty(value = "优惠前价格")
    private BigDecimal prePrice;
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountsPrice;
    @ApiModelProperty(value = "是否使用优惠券")
    private String couponFlag;
    @ApiModelProperty(value = "用户手机号码")
    @NotBlank(message = "用户手机号码不可为空")
    private String userPhone;
    @ApiModelProperty(value = "订单商品数量")
    private Integer orderQty;
    @ApiModelProperty("需要积分")
    private Integer integralAll;
    @ApiModelProperty("参团id;为空=开团，有值=参团")
    private Long activityGroupId;
}