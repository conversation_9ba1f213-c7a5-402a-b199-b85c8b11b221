package com.deepexi.dxp.marketing.domain.promotion.request.specify;

import com.deepexi.util.domain.request.BaseExtRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 活动修改
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
@ToString
public class PromotionActivityUpdateRequestDTO extends BaseExtRequest {

    @NotNull(message = "活动ID不能为空")
    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("渠道id")
    private String tenantId;

    @ApiModelProperty("appId")
    private Long appId;
    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @NotNull(message = "模板id 不能为空")
    @ApiModelProperty(value = "模板id")
    private Integer paTemplateId;
    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    @NotNull(message = "活动状态不能为空")
    private Integer status;
}
