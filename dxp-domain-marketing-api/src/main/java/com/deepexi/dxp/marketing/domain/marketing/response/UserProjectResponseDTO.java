package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "用户项目列表返回对象", description = "用户项目列表返回对象")
public class UserProjectResponseDTO extends AbstractObject {

    @ApiModelProperty(value = "区域ID")
    private String areaId;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "真实区域编码")
    private String realAreaId;

    @ApiModelProperty(value = "真实区域名称")
    private String realAreaName;

    @ApiModelProperty(value = "城市ID")
    private String cityId;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "地址位置，城市ID")
    private String realCityId;

    @ApiModelProperty(value = "地理位置，城市名称")
    private String realCityName;

    @ApiModelProperty(value = "组织代码")
    private String orgId;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "分期-销售组织列表")
    private List<PorjectPeriodDTO> porjectPeriodList;

    @ApiModelProperty(value = "项目代码")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 分期-销售组织
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class PorjectPeriodDTO extends AbstractObject implements Serializable {

        @ApiModelProperty(value = "项目代码")
        private String projectId;

        @ApiModelProperty(value = "分期代码")
        private String periodId;

        @ApiModelProperty(value = "分期名称")
        private String periodName;

        @ApiModelProperty(value = "组织编码")
        private String saleOrgId;

        @ApiModelProperty(value = "组织名称")
        private String saleOrgName;
    }

}
