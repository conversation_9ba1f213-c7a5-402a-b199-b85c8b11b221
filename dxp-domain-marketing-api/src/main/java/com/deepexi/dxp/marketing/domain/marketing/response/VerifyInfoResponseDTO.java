package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 奖品领取/核销明细
 * <AUTHOR>
 */
@Data
@ApiModel(value = "抽奖活动详情奖品领取/核销明细返回对象", description = "抽奖活动详情奖品领取/核销明细返回对象")
public class VerifyInfoResponseDTO {
    /**
     * 奖品名称
     */
    @ApiModelProperty(value = "奖品名称")
    private String resourceName;

    @ApiModelProperty(value = "核销数量")
    private Integer writtenOffQuantity;

    @ApiModelProperty(value = "发放数量")
    private Integer issuedQuantity;

    @ApiModelProperty(value = "已领取数量")
    private Integer receivedQuantity;

    @ApiModelProperty(value = "剩余数量")
    private Integer remainingQuantity;

    @ApiModelProperty(value = "未核销数量")
    private Integer notWrittenOffQuantity;

    @ApiModelProperty(value = "已退款数量")
    private Integer refundedQuantity;

    @ApiModelProperty(value = "细分类别 0、手机话费 1、现金红包")
    private Integer thirdCategory;

    @ApiModelProperty(value = "细分类别 0、普通劵 1、房源劵 2、商品劵")
    private Integer couponCategory;

    @ApiModelProperty(value = "卡劵类型 0、代金劵 1、折扣劵")
    private Integer couponType;

    @ApiModelProperty(value = "资源类型 0、卡劵 1、第三方接入")
    private Integer type;

    @ApiModelProperty(value = "待领取数量")
    private Integer notClaimedQuantity;
}
