package com.deepexi.dxp.marketing.domain.promotion.query.specify;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;


@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class ActivityPromotionChannelQuery extends SuperQuery {

    @ApiModelProperty(value = "活动ID",required = true)
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     * 活动推广类型
     */
    @ApiModelProperty(value = "活动推广类型，0：活动推广；1：活动专题推广",required = true)
    @NotNull(message = "活动推广类型不能为空")
    private Integer promotionType;

}
