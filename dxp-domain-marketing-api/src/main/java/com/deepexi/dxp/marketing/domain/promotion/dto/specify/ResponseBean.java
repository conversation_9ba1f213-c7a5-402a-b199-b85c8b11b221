package com.deepexi.dxp.marketing.domain.promotion.dto.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@ApiModel("响应实体信息")
public class ResponseBean {

    @ApiModelProperty(value = "返回码")
    private int code;
    @ApiModelProperty(value = "返回消息")
    private String msg;
    @ApiModelProperty(value = "数据载体")
    private Object data;

    /**
     * 统一返回成功响应体
     * @param data
     * @return
     */
    public static ResponseBean ok(Object data){
        return ResponseBean.builder()
                .code(0)
                .msg("ok")
                .data(data).build();
    }

    /**
     * 统一返回成功响应体
     * @param msg
     * @return
     */
    public static ResponseBean error(String msg){
        return ResponseBean.builder()
                .code(1)
                .msg(msg)
                .data("").build();
    }

}
