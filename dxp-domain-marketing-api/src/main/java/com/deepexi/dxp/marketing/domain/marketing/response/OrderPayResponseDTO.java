package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "下单支付接口返回", description = "下单支付接口返回")
public class OrderPayResponseDTO {

    @ApiModelProperty(value = "支付平台的订单号，查询或退款时需要提供")
    private String orderNo;

    @ApiModelProperty(value = "签名，使用字段appId、timeStamp、nonceStr、package计算得出的签名值")
    private String paySign;

    @ApiModelProperty(value = "签名类型，默认为RSA，仅支持RSA")
    private String signType;

    @ApiModelProperty(value = "订单详情扩展字符串")
    private String packageRes;

    @ApiModelProperty(value = "随机字符串")
    private String nonceStr;

    @ApiModelProperty(value = "时间戳")
    private String timeStamp;

    @ApiModelProperty(value = "业务订单号")
    private String bizOrderNo;

    @ApiModelProperty(value = "业务订单id")
    private Long orderId;

    /* **************** h5相关 ******************/
    @ApiModelProperty(value = "h5Url")
    private String h5Url;

    /* **************** 抖音相关 ******************/
    @ApiModelProperty(value = "抖音返回")
    DyPayInfo dyPayInfo;
    /* **************** 抖音相关 ******************/
    @Data
    @ApiModel
    public static class DyPayInfo extends AbstractObject implements Serializable {
        @ApiModelProperty(value = "时间戳")
        private String timestamp;

        @ApiModelProperty(value = "版本")
        private String version;

        @ApiModelProperty(value = "签名")
        private String sign;

        @ApiModelProperty(value = "用户唯一标志")
        private String uid;

        @ApiModelProperty(value = "CNY")
        private String currency;

        @ApiModelProperty(value = "主题")
        private String subject;

        @ApiModelProperty(value = "内容")
        private String body;

        @ApiModelProperty(value = "商户号")
        private String merchant_id;

        @ApiModelProperty(value = "app_id")
        private String app_id;

        @ApiModelProperty(value = "签名类型")
        private String sign_type;

        @ApiModelProperty(value = "交易类型")
        private String trade_type;

        @ApiModelProperty(value = "产品编码")
        private String product_code;

        @ApiModelProperty(value = "产品类型")
        private String payment_type;

        @ApiModelProperty(value = "订单号码,基础交易服务所定义的订单号")
        private String out_order_no;

        @ApiModelProperty(value = "金额，单位是分")
        private int total_amount;

        @ApiModelProperty(value = "交易时间")
        private String trade_time;

        @ApiModelProperty(value = "有效交易时间")
        private String valid_time;

        @ApiModelProperty(value = "回调地址")
        private String notify_url;

        @ApiModelProperty(value = "微信h5支付地址")
        private String wx_url;

        @ApiModelProperty(value = "微信h5支付交易类型")
        private String wx_type;

        @ApiModelProperty(value = "支付风控参数")
        private String risk_info;
    }
}
