package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/12/12 16:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Api
@ApiModel
public class CouponCommodityDTO {
    @ApiModelProperty("商品")
    private List<ActivityCommodityDTO> commodities;

    @ApiModelProperty("活动叠加（是否允许使用优惠券）")
    private String coupon;

    @ApiModelProperty("优惠券id列表")
    private List<Long> couponIds;

}
