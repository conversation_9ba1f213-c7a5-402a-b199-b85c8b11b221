package com.deepexi.dxp.marketing.domain.promotion.dto.opengroup;

import com.deepexi.util.domain.dto.BaseExtDTO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 当前可参与的可拼团的商品列表 条件参数实体
 * 即：我的拼团列表返回DTO
 *
 * <AUTHOR> xianfeng.cai
 * @date 2019/11/29 14:08
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel
public class OpenGroupMineListResponseDTO extends BaseExtDTO {

    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;
    @ApiModelProperty("拼团id")
    private Long id;
    @ApiModelProperty("拼团活动Id")
    private Long activityId;
    @ApiModelProperty("拼团活动名称")
    private String activityName;
    @ApiModelProperty("参与时间")
    private String joinTime;
    @ApiModelProperty(value = "拼团状态;0-待成团，1-已成团，2-超时取消，3-订单取消关闭")
    private Integer status;

}