package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "活动分析指标对象", description = "活动分析指标对象")
public class IndexAnalysisDTO {

     @ApiModelProperty(value = "指标")
     private String indexPath;

     @ApiModelProperty(value = "累计次数")
     private Long totalNumber;

     @ApiModelProperty(value = "今日次数")
     private Long todayNumber;

     @ApiModelProperty(value = "上一天次数")
     private Long yesterdayNumber;

     @ApiModelProperty(value = "累计人数")
     private Long totalPeopleNumber;

     @ApiModelProperty(value = "今日人数")
     private Long todayPeopleNumber;

     @ApiModelProperty(value = "上一天人数")
     private Long yesterdayPeopleNumber;


     @ApiModelProperty(value = "核销人数")
     private Long totalWriteOffNumber;

     @ApiModelProperty(value = "今日核销人数")
     private Long todayWriteOffNumber;

     @ApiModelProperty(value = "上一天核销人数")
     private Long yesterdayWriteOffNumber;
}
