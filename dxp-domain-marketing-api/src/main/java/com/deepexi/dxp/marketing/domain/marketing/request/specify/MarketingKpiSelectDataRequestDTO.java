package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author：liuyang
 * @version：1.0.0
 * @date：2021-03-31 17:17
 */
@Data
@ApiModel
public class MarketingKpiSelectDataRequestDTO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "1,原生指标下拉框; 2派生指标下拉框", required = true)
    private Integer type;
}
