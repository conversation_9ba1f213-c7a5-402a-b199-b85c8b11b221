package com.deepexi.dxp.marketing.domain.promotion.request.calculate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 优惠券入参
 *
 * <AUTHOR> ming.zhong
 * @date created in 21:01 2019/12/3
 */
@ApiModel
@Data
public class CouponActivityParamsRequest {
    @NotNull
    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("用户类型")
    @NotNull
    private String userType;
    @NotNull
    @ApiModelProperty("用户分组id List")
    private List<Long> groupIdList;
    @ApiModelProperty("渠道")
    @NotNull
    private String clientTenant;

    @ApiModelProperty("租户")
    private String tenantId;
    @ApiModelProperty("应用")
    private Long appId;
    @NotNull
    @ApiModelProperty("优惠券")
    private Long couponId;
    @ApiModelProperty("商店id")
    private Long shopId;
    @NotNull
    @ApiModelProperty("来源id")
    private Long releaseId;
    /**
     * "来源类型：0-活动，1-会员 6-第三方"
     */
    @NotNull
    @ApiModelProperty("来源类型：0-活动，1-会员 6-第三方")
    private Integer releaseType;
    @ApiModelProperty("获取数量")
    private Integer amount = 1;

    @ApiModelProperty("是否被邀请 ‘true’|‘false’")
    private String isInvited;
}
