package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 商品活动参数
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/25 10:32
 */

@ApiModel
@Data
@ToString
public class ActivityParamsDTO {

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "unionId")
    private String unionId;
    @ApiModelProperty("用户类型")
    private String userType;
    @ApiModelProperty("用户分组id List")
    private List<Long> groupIdList;
    @ApiModelProperty("渠道")
    private String clientTenant;

    @ApiModelProperty("商品")
    private ActivityCommodityDTO commoditie;

    @ApiModelProperty("活动叠加（是否允许使用优惠券）")
    private String coupon;

    @ApiModelProperty("单日活动次数限制")
    private Integer dayNum;
    @ApiModelProperty("活动总次数限制")
    private Integer totalNum;

    /**商品活动购买个数*/
    @ApiModelProperty("商品活动购买个数")
    public Integer commoditieBuyNum = 0;
    /**
     * 参数传递
     */
    @ApiModelProperty("当前活动用户记录list")
    private List loggerDOList;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty("用户参与类型：0-发起者,1-参与者")
    private Integer userJoinType;
}
