package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.dxp.marketing.common.base.dto.SuperExtDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ComdityLimitDTO;
import com.deepexi.util.domain.request.BaseExtRequest;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 修改活动入参
 *
 * <AUTHOR> xin<PERSON>an.yao
 * @date 2019/11/21 14:26
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
@ToString
public class PromotionActivityUpdatePostRequest extends SuperExtDTO {
    @ApiModelProperty("0 全款可退 1 定金可退 2 不可退 （默认是0）")
    private Integer returnMoneyType;
    @ApiModelProperty("活动id")
    private Long activityId;
    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;
    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String activityName;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private Integer status;
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private Date endTime;

    @ApiModelProperty("商品限制")
    private ComdityLimitDTO commodity;
    @ApiModelProperty("活动策略的规则")
    private List<ActivityRuleDTO> activityRuleDTOList;
    @ApiModelProperty("渠道限制")
    private List<BaseActivityDTO> tenantLimit;
    @ApiModelProperty("会员限制")
    private List<BaseActivityDTO> userLimit;
    @ApiModelProperty("数量限制")
    private List<BaseActivityDTO> numberLimit;
    @ApiModelProperty("优惠券限制")
    private List<BaseActivityDTO> couponLimit;
    @ApiModelProperty("门店限制")
    private List<BaseActivityDTO> shopLimit;

    //===========华发===============

    @ApiModelProperty("抽奖限制")
    private List<BaseActivityDTO> luckyDrawLimit;

    @ApiModelProperty("砍价限制")
    private List<BaseActivityDTO> bargainLimit;

    @ApiModelProperty("助力限制")
    private List<BaseActivityDTO> assistLimit;

    @ApiModelProperty("表单限制")
    private List<BaseActivityDTO> formLimit;

    @ApiModelProperty("集卡限制")
    private List<BaseActivityDTO> cardCollectingLimit;

    @ApiModelProperty(value = "适用项目ids")
    @NotNull(message = "适用项目ids不能为空")
    private List<ActivityParticipationVO> projectIds;

//    @ApiModelProperty(value = "活动扩展信息")
//    @NotNull(message = "活动扩展信息不能为空")
//    @Valid
//    private ActivityExtVO activityExtVO;
    /**
     * 规则配置
     */
    @ApiModelProperty(value = "规则配置")
    @NotNull(message = "规则配置不能为空")
    @Valid
    private RuleConfigVO ruleConfigVO;

    @ApiModelProperty(value = "奖品配置")
    @NotNull(message = "奖品配置不能为空")
    @Valid
    private List<PrizeConfigVO> prizeList;

    /**
     * 界面配置
     */
    @ApiModelProperty(value = "活动页配置")
    @NotNull(message = "活动页配置不能为空")
    @Valid
    private ActivityPageVO activityPageVO;


    @ApiModelProperty(value = "分享页配置")
    @NotNull(message = "分享页配置不能为空")
    @Valid
    private ActivityPageShareVO activityPageShareVO;
    //===========华发===============

}
