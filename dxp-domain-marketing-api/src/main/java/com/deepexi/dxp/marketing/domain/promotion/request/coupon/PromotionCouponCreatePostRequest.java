package com.deepexi.dxp.marketing.domain.promotion.request.coupon;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.CouponLimitDTO;
import com.deepexi.util.domain.request.BaseExtRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠券新增入参
 *
 * <AUTHOR> ming.zhong
 * @date created in 14:46 2019/11/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PromotionCouponCreatePostRequest extends BaseExtRequest {
    /**
     * 优惠券code
     */
    private String code;
    /**
     * 优惠券名称
     */
    @ApiModelProperty(value = "优惠券名称")
    @NotBlank(message = "优惠券名称不能为空")
    private String couponName;

    /**
     * 优惠券类型
     */
    @ApiModelProperty(value = "优惠券类型")
    @NotBlank(message = "优惠券类型不能为空")
    private String couponType;

    /**
     * 代金券面值|折扣券折扣
     */
    @NotNull(message = "代金券面值|折扣券折扣不能为空")
    @ApiModelProperty(value = "代金券面值|折扣券折扣")
    private BigDecimal couponValue;

    /**
     * 是否启用 0 禁用 1 启用
     */
    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "是否启用 0 禁用 1 启用")
    private Integer status;

    /**
     * 使用条件适用金额
     */
    @NotNull(message = "使用条件适用金额 不能为空")
    @ApiModelProperty(value = "使用条件适用金额")
    private BigDecimal condition;

    /**
     * 租户隔离
     */
    @ApiModelProperty(value = "租户隔离")
    @NotBlank(message = "租户隔离不能为空")
    private String tenantId;

    /**
     * 应用隔离
     */
    @ApiModelProperty(value = "应用隔离")
    @NotNull(message = "appId 不能为空")
    private Long appId;

    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标记
     */
    private Integer deleted = 0;


    @ApiModelProperty("限制")
    @NotNull(message = "优惠券限制不能为空")
    private CouponLimitDTO couponLimit;
}
