package com.deepexi.dxp.marketing.domain.promotion.dto.opengroup;

import com.deepexi.util.domain.dto.BaseExtDTO;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 当前可参与的可拼团的商品列表对象
 *
 * <AUTHOR> xianfeng.cai
 * @date 2019/11/29 14:08
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class OpenGroupCommodityListResponseDTO extends BaseExtDTO {


    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;

    @ApiModelProperty("活动id")
    private Long activityId;
    @ApiModelProperty("参团id")
    private Long activityGroupId;
    @ApiModelProperty(value = "名称")
    private String activityName;
    @ApiModelProperty(value = "编码")
    private String activityCode;
    @ApiModelProperty(value = "描述")
    private String activityDescription;
    @ApiModelProperty(value = "活动状态 0 进行中")
    private Integer status;
    @ApiModelProperty(value = "开团类型；1-普通团，2-老带新")
    private Integer groupType;
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;


    @ApiModelProperty("商品sku编码")
    private String skuCode;
    @ApiModelProperty("成团人数")
    private Integer peopleNumber;
    @ApiModelProperty("拼团活动商品库存数量")
    private Integer inventoryNumber;
    @ApiModelProperty("开团价格")
    private BigDecimal activityPrice;
    @ApiModelProperty("团长价格")
    private BigDecimal discountPrice;
    @ApiModelProperty("团长优惠标识；1是0否")
    private Integer discountFlag;
    @ApiModelProperty("已成团数")
    private Integer alreadyGroup;
    @ApiModelProperty("拼团下单数")
    private Integer alreadyGroupOrderNum;
}