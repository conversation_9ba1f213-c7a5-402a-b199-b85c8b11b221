package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * CustomerFeedbackDTO---limits入参对象
 * <AUTHOR>
 */
@Data
public class FeedbackInfoVO {

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "港澳通行证")
    private String hkmCard;

    @ApiModelProperty(value = "所在地区")
    private String cityName;

    @ApiModelProperty(value = "门牌号")
    private String houseNum;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     *资源奖品ID
     */
    @ApiModelProperty(value = "房源名字")
    private String cardText;
}
