package com.deepexi.dxp.marketing.domain.promotion.query.opengroup;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 待成团分页查询入参
 *
 * <AUTHOR> xianfeng.cai
 * @date 2019/12/16 14:08
 */
@Data
@ApiModel
public class WaitCompleteGroupsPageQuery extends SuperQuery {

    @ApiModelProperty("活动Id")
    private Long activityId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 乐观锁
     */
    private Long version;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 更新时间
     */
    private Date updatedTime;
}