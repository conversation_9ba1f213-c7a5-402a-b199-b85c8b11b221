package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "活动项目", description = "活动项目")
public class ActivityParticipationGroupResponseDTO {


    @ApiModelProperty(value = "区域id")
    private String cityId;

    @ApiModelProperty(value = "区域名称")
    private String cityName;
    @ApiModelProperty(value = "项目列表")
    private List<ActivityParticipationItemResponseDTO> projectList;


    @Data
    @EqualsAndHashCode(callSuper =true )
    public static class ActivityParticipationItemResponseDTO extends AbstractObject implements Serializable {
        @ApiModelProperty(value = "项目id")
        private String projectId;

        @ApiModelProperty(value = "项目名称")
        private String projectName;

        @ApiModelProperty(value = "是否可跳转（0不可跳转，1可跳转）")
        private Integer isJumpable;
    }
}
