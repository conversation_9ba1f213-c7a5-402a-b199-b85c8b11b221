package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeLogDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/27
 */
@Data
public class CardCollectingHomeUserInfoResponseDTO {

    @ApiModelProperty(value = "用户参与记录")
    private ActivityPartakeLogDTO activityPartakeLogDTO;

    @ApiModelProperty(value = "用户卡片列表")
    List<ActivityFissionAssistResourceResponseDTO> cardList;

}
