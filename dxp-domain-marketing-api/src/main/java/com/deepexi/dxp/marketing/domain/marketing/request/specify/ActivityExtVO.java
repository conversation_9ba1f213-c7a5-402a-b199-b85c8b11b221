package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(value = "活动扩展入参对象", description = "活动扩展入参对象")
@Data
public class ActivityExtVO {

    @ApiModelProperty(value = "活动目标")
    @NotNull(message = "活动目标不能为空")
    private Integer activityGoal;

    @ApiModelProperty(value = "活动目标名称")
    private String activityGoalName;

    @ApiModelProperty(value = "指标路径")
    @NotNull(message = "指标路径不能为空")
    private Integer kpiPath;

    @ApiModelProperty(value = "活动类型（0大转盘，1九宫格）")
    //@NotNull(message = "活动类型不能为空")
    @Min(value = 0)
    @Max(value = 1,message = "活动类型输入有误")
    private Integer activityType;

    @ApiModelProperty(value = "是否热门（0否，1是）")
    @NotNull(message = "是否热门不能为空")
    @Min(value = 0)
    @Max(value = 1,message = "是否热门输入有误")
    private Integer isPopular;

    /* ****************营销类相关属性 start **************** */
    @ApiModelProperty(value = "活动关联组织id")
    private String orgId;

    @ApiModelProperty(value = "支付账户号")
    private String payNo;

    @ApiModelProperty(value="参与对象:1-全部用户")
    private Integer participants;

    @ApiModelProperty(value="资源属性:0-通用券,1-项目专属券")
    private Integer resourcesAttribute;

    @ApiModelProperty(value="提交表单获奖:0-关闭,1-开启")
    private Integer submitFormAward;
    /* ****************营销类相关属性 end **************** */

    /* ****************秒杀相关属性 end **************** */
    @ApiModelProperty(value="发布方式：0-手动发布，1-定时发布")
    private Integer publishType;

//    @ApiModelProperty(value = "发布时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date publishTime;

    @ApiModelProperty(value="填写信息登记时间：0-活动前填写，1-活动后填写")
    private Integer feedbackInfoWriteTime;

    @ApiModelProperty(value="信息登记内容")
    private List<EnrollmentInfoVO> feedbackInfo;

    /* ****************秒杀相关属性 end **************** */

    @ApiModelProperty(value="适用项目类型（1-选择项目，2全国项目）")
    private Integer projectType;

    @ApiModelProperty(value="发布渠道类型0置业通小程序，2抖音小程序")
    private String channelType;

    @ApiModelProperty(value="会员时间类型（1-不限（默认），2-活动期间）")
    private Integer memberTimeType;
    @ApiModelProperty(value="审核状态（0-未审核，1-审核通过，2-审核不通过）")
    private Integer auditStatus;
    @ApiModelProperty(value="审核时间")
    private String auditTime;
    @ApiModelProperty(value="审核不通过原因原因")
    private String rejectReason;
    @ApiModelProperty(value="审核人")
    private String auditor;
}
