package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ComdityLimitDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/10
 * @version  V1.0
 * @description 优惠券互斥量实体类：优惠券叠加活动
 */
@Data
@ApiModel
public class CouponFlagRequest {
    @ApiModelProperty("主键")
    @NotNull
    private Long id;

    @ApiModelProperty("时间限制")
    private ActivityRuleDTO timeLimit;

    @ApiModelProperty("商品限制")
    private ComdityLimitDTO commodity;

    @ApiModelProperty("优惠券叠加活动限制：true：叠加使用，false：不能叠加使用")
    private List<BaseActivityDTO> couponLimit;
}
