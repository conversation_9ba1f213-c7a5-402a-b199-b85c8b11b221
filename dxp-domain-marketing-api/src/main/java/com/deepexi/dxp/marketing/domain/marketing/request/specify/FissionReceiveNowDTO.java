package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 砍价立即领取请求dto
 * <AUTHOR>
 */
@Data
@ApiModel
public class FissionReceiveNowDTO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     *用户ID
     **/
    @ApiModelProperty(value = "用户Id")
    //@NotBlank(message = "用户昵称不能为空")
    private String userId;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    @NotBlank(message = "用户昵称不能为空")
    private String nickName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    @NotBlank(message = "用户名不能为空")
    private String userName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * unionId
     */
    @ApiModelProperty(value = "unionId")
    private String unionId;


    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty(value = "0、微信小程序  1、H5  2、抖音")
    @NotNull(message = "来源不能为空")
    private Integer type;

    @ApiModelProperty("发起人信息id（抽奖、秒杀、优惠券的用户参与记录表主键id）")
    @NotNull(message = "发起人信息id不能为空")
    private Long sponsorId;

    @ApiModelProperty("资源ID")
    private Long resourceId;
}
