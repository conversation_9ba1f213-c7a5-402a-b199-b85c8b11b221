package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 裂变排行榜
 */
@Data
@ApiModel(value = "裂变排行榜返回对象", description = "裂变排行榜返回对象")
public class FissionRankResponseDTO {

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "数量")
    private Integer cnt;

    @ApiModelProperty(value = "需要的总数")
    private Integer nft;
}
