package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发送激励请求接口（发送微信，小程序红包）
 * <AUTHOR>
 */
@Data
public class RechargeMobileRequest extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "请求流水号")
    private String reqNo;

    @ApiModelProperty(value = "用户电话")
    private String telNo;

    @ApiModelProperty(value = "活动ID")
    private String actId;

    @ApiModelProperty(value = "充值面额(非支付金额)1、2、5、10、20、30、50、100等")
    private float amount;


    @ApiModelProperty("用户在直连商户appId下的唯一标识")
    private String projectId;

    @ApiModelProperty("状态码（在通知充值结果时，会回传改值。调用方根据该值，确定请求是否属于本系统）")
    private String state;

    @ApiModelProperty(value = "回调地址")
    private String callBackUrl;
}
