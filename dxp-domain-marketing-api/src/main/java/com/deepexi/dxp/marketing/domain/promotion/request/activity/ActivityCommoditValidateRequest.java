package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityCommodityValidateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 校验商品是否已经存在活动中
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/12 11:04
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel
public class ActivityCommoditValidateRequest extends SuperRequest {
    private static final long serialVersionUID = -4515322461338343567L;
    /**
     * 自增主键
     */
    @ApiModelProperty("自增主键")
    private Long id;


    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;


    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;


    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updatedTime;

    /**
     * 商品信息
     */
    @ApiModelProperty("商品匹配活动列表")
    private List<ActivityCommodityValidateDTO> commodityList;
    /**
     * 模版ID
     */
    @ApiModelProperty("模版ID")
    private Integer paTemplateId;

    /**
     * 活动开始时间
     */
    @ApiModelProperty("活动开始时间")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty("活动结束时间")
    private Date endTime;

}
