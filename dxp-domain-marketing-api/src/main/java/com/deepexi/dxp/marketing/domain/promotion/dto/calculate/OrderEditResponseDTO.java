package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单计算返回
 *
 * <AUTHOR> xinjian.yao
 * @date 2019/12/2 16:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@ApiModel
public class OrderEditResponseDTO extends AbstractObject {
    @ApiModelProperty("有活动的商品")
    private List<ActivityResponseParamsDTO> activityList;

    @ApiModelProperty("没有活动的商品")
    private List<ActivityCommodityDTO> noActivityCommodityDTOList;

    @ApiModelProperty("订单原始金额")
    private BigDecimal orderDetailPrice;
    @ApiModelProperty("订单活动计算后总金额(不含优惠券)")
    private BigDecimal orderDiscountsPriceOnlyActivity;
    @ApiModelProperty("订单优惠后金额(含优惠券后)")
    private BigDecimal orderDiscountsPrice;
    @ApiModelProperty("优惠券优惠了多少钱")
    private BigDecimal couponDiscountsPrice;
    @ApiModelProperty("需要积分")
    private Integer integralAll;

}
