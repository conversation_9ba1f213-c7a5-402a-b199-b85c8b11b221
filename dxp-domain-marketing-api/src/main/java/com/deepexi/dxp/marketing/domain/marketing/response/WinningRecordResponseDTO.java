package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 中奖记录返回参数
 * <AUTHOR>
 */
@Data
@ApiModel(value = "中奖记录返回对象", description = "中奖记录返回对象")
public class WinningRecordResponseDTO {
    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String prizeResult;
}
