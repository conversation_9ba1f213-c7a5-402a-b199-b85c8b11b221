package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/12/29
 */
@Data
public class CardCollectingLotteryRequestDTO {

    @ApiModelProperty(value = "活动ID", required = true)
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    @ApiModelProperty(value = "手机号", required = true)
    @NotNull(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "活动参与记录id", required = true)
    @NotNull(message = "活动参与记录id不能为空")
    private Long partakeLogId;

    @ApiModelProperty(value = "中奖卡片id", required = true)
    @NotNull(message = "中奖卡片id不能为空")
    private Long id;

}
