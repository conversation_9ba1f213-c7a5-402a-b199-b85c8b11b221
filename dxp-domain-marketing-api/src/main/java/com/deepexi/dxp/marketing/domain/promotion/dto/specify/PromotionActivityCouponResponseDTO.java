package com.deepexi.dxp.marketing.domain.promotion.dto.specify;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityExtVO;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 优惠券活动详情
 */
@Data
public class PromotionActivityCouponResponseDTO extends AbstractObject implements Serializable {
    @ApiModelProperty("活动id")
    private Long id;

    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;
    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId;

    @ApiModelProperty("活动名称")
    private String name;

    @ApiModelProperty(value="活动开始时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value="活动结束时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endTime;


    @ApiModelProperty(value="扩展字段")
    private ExtendFieldDTO ext;

    @ApiModelProperty(value="活动页面")
    private ActivityPageCouponResponseDTO  activityPageRequestDTO;

    @ApiModelProperty(value="分享页")
    private ActivityPageShareCouponResponseDTO activityPageShareRequestDTO;


    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class ExtendFieldDTO extends ActivityExtVO implements Serializable {

        @ApiModelProperty(value="参与对象:1-全部用户")
        private Integer participants;

        @ApiModelProperty(value="资源属性:0-不限项目,1-指定项目")
        private Integer resourcesAttribute;

        @ApiModelProperty(value="投放渠道:0-不在置业通小程序展示,1-在置业通小程序-活动列表中展示")
        private Integer deliveryChannel;


        @ApiModelProperty(value="适用项目List")
        private List<ActivityParticipationResponseDTO> projectList;



        @ApiModelProperty(value="资源列表")
        private List<ResourceHisDetailResponseDTO> resourceDetailDTOList;

        @ApiModelProperty(value="登记信息")
        private List<RegistInfoResponseDTO> registInfoDTOList;
    }

    /**
     * 项目信息
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class ActivityParticipationResponseDTO extends AbstractObject implements Serializable {

        @ApiModelProperty(value="主键")
        private Long id;

        @ApiModelProperty(value="项目id")
        private String projectId;

        @ApiModelProperty(value="项目名称")
        private String projectName;

        @ApiModelProperty(value="项目编码")
        private String projectNumber;

        @ApiModelProperty(value="公司编码")
        private String saleOrgId;

        @ApiModelProperty(value="公司名称")
        private String saleOrgName;

    }

    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class RegistInfoResponseDTO extends AbstractObject implements Serializable {

        @ApiModelProperty("输入类型（0普通文本框，1手机号码，2所在地区，3身份证,4姓名）")
        private Integer type;

        @ApiModelProperty("输入标题")
        private String value;
    }
}
