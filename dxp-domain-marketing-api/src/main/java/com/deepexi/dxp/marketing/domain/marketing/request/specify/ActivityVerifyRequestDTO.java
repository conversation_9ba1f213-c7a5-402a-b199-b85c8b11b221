package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 核销请求参数
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class ActivityVerifyRequestDTO {

    @ApiModelProperty(value = "主键")
    //@NotNull(message = "核销ID不能为空")
    private Long id;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "核销码")
    private String code;

    @ApiModelProperty(value = "类型：0-后台核销,1-小程序端")
    private Integer type;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;
    private Map<String, Object> ext;
}
