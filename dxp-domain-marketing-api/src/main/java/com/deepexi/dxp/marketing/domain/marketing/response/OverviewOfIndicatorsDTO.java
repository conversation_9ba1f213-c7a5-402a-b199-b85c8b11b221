package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "活动分析指标概览返回对象", description = "活动分析指标概览返回对象")
public class OverviewOfIndicatorsDTO {

//    @ApiModelProperty(value = "浏览用户数")
//    private Integer browsingPeopleNumber;
//
//    @ApiModelProperty(value = "浏览次数")
//    private Integer browsingNumber;
//
//    @ApiModelProperty(value = "活动参与次数")
//    private Integer joinNumber;
//
//    @ApiModelProperty(value = "活动参与人数")
//    private Integer joinPeopleNumber;
//
//    @ApiModelProperty(value = "领取奖品人数")
//    private Integer prizeCollectionPeopleNumber;
//
//    @ApiModelProperty(value = "活动核销人数")
//    private Integer writeOffPeopleNumber;

     @ApiModelProperty(value = "指标")
     private String indexPath;
}
