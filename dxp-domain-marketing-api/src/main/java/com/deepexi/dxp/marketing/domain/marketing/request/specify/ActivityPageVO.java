package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 活动页入参对象
 * <AUTHOR>
 */
@ApiModel(value = "活动页入参对象", description = "活动页入参对象")
@Data
public class ActivityPageVO extends AbstractObject implements Serializable {
    @ApiModelProperty(value = "活动图标")
    @NotBlank(message = "活动图标不能为空")
    private String activityIconUrl;

    @ApiModelProperty(value = "活动介绍图（主播详情页背景）")
    @NotBlank(message = "活动介绍不能为空")
    private String activityDiagram;

    @ApiModelProperty(value = "活动规则图")
    @NotBlank(message = "活动规则图不能为空")
    private String activityRulesUrl;

    @ApiModelProperty(value = "底部按钮 1、项目详情, 2、联系销售,3、联系电话,4、分享,5、生成海报")
    @NotNull(message = "底部按钮不能为空")
    private List<Integer> bottomBtnTypes;

    @ApiModelProperty(value = "电话(底部按钮选择3时必填)")
    private String phone;

    @ApiModelProperty(value = "背景图")
    private String backGroundUrl;

    @ApiModelProperty("背景颜色")
    private String backGroundColor;
    @ApiModelProperty("按钮颜色")
    private String buttonColor;
    @ApiModelProperty("按钮描边颜色")
    private String buttonLineColor;
    @ApiModelProperty("按钮字体颜色")
    private String buttonTextColor;
    @ApiModelProperty(value = "卡片抽奖图")
    private String cardLotteryImg;
    @ApiModelProperty(value = "卡片背面图")
    private String cardBackImg;
    @ApiModelProperty("合成背景图")
    private String mergeBackgroundImg;
    @ApiModelProperty("合成卡片图")
    private String mergeCardImg;
    @ApiModelProperty("合成字体颜色")
    private String mergeTextColor;
    @ApiModelProperty("合成按钮图")
    private String mergeButtonImg;

    @ApiModelProperty(value = "邀请助力按钮文案")
    private String inviteAssistBtnText;

    @ApiModelProperty(value = "邀请助力按钮颜色 0 、蓝色,1、红色")
    private Integer inviteColorType;

    @ApiModelProperty(value = "排行榜 0关闭,1开启")
    private Integer isShowRanklist;

    @ApiModelProperty(value = "排行榜图片")
    private String ranklistUrl;


    @ApiModelProperty(value = "扩展属性:\"isShowHzbRank\": 1, *首页城市/项目排名\n" +
            "\"isShowHzbIcon\": 0, 主播头像\n" +
            "\"isShowHzbName\": 1,主播名称\n" +
            "\"isShowHzbInfo\": 1,主播介绍\n" +
            "\"isShowHzbRecom\": 0,推荐有礼\n" +
            "\"isShowHzbPhone\": 1 电话咨询" +
            "\"isShowHzbPhone\": 1 电话咨询")
    private Map<String, Object> ext;

    /**
     * 基础配置通用ID
     */
    @ApiModelProperty(value = "基础配置通用ID")
    private Long activityId;


    /**
     * 表单背景图 0、隐藏,1、显示
     */
    @ApiModelProperty(value = "表单背景图 0、隐藏,1、显示")
    private Integer isFormBackground;


    /**
     * 表单头图 0、隐藏,1、显示
     */
    @ApiModelProperty(value = "表单头图 0、隐藏,1、显示")
    private Integer isHead;

    /**
     * 表单头图
     */
    @ApiModelProperty(value = "表单头图")
    private String headUrl;


    /**
     * 底部按钮 1、项目详情, 2、联系销售,3、联系电话,4、分享,5、生成海报
     */
    @ApiModelProperty(value = "底部按钮 1、项目详情, 2、联系销售,3、联系电话,4、分享,5、生成海报,6、在线咨询")
    private String bottomBtnType;


    /**
     * 参与助力按钮文案
     */
    @ApiModelProperty(value = "参与助力按钮文案")
    private String partakeAssistText;

    /**
     * 参与助力按钮颜色 0 、蓝色,1、红色
     */
    @ApiModelProperty(value = "参与助力按钮颜色 0 、蓝色,1、红色")
    private Integer partakeColorType;


    /**
     * 0、基础配置,1、活动专题
     */
    @ApiModelProperty(value = "0、基础配置,1、活动专题")
    private Integer type;
}
