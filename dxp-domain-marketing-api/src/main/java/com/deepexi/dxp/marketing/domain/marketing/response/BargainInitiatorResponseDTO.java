package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 砍价活动响应
 * <AUTHOR>
 */
@ApiModel(value = "砍价活动返回对象", description = "砍价活动返回对象")
@Data
public class BargainInitiatorResponseDTO {
    /**
     * 活动状态：0=草稿，1=未开始，2=进行中，3=已完成，4=已终止
     */
    @ApiModelProperty(value = "`活动状态：0=草稿，1=未开始，2=进行中，3=已完成，4=已终止`")
    private Integer activityStatus;

}
