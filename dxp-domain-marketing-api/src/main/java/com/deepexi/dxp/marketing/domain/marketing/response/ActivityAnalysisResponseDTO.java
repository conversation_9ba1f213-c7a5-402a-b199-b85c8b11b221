package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "活动分析返回对象", description = "活动分析返回对象")
public class ActivityAnalysisResponseDTO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动模板")
    private String template;


    @ApiModelProperty(value = "活动访问次数")
    private Long accessNumber;

    @ApiModelProperty(value = "活动访问人数")
    private Long accessPeopleNumber;

    @ApiModelProperty(value = "活动分享次数")
    private Long shareNumber;

    @ApiModelProperty(value = "活动分享人数")
    private Long sharePeopleNumber;

    @ApiModelProperty(value = "活动参与次数")
    private Long joinNumber;

    @ApiModelProperty(value = "活动参与人数")
    private Long joinPeopleNumber;

    @ApiModelProperty(value = "活动留资人数")
    private Long lzPeopleNumber;

    @ApiModelProperty(value = "活动留资率")
    private BigDecimal lzRate;

    @ApiModelProperty(value = "活动核销人数")
    private Integer writeOffPeopleNumber;

    @ApiModelProperty(value = "活动期间到访人数")
    private Integer visitPeopleNumber;

    @ApiModelProperty(value = "活动转到访人数")
    private Integer goVisitPeopleNumber;

    @ApiModelProperty(value = "活动期间成交人数")
    private Integer dealPeopleNumber;

    @ApiModelProperty(value = "活动转成交人数")
    private Integer goDealPeopleNumber;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "用户手机号")
    private String phone;

    @ApiModelProperty(value = "创建时间")
    private String createTime;
}
