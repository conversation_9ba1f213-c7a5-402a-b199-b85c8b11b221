package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 参与活动详情入参
 */
@Data
public class ActivityHomeInfoRequest {

    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     * 用户昵称
     */
    @ApiModelProperty("手机号")
    private String phone;

    @NotNull
    @ApiModelProperty("用户参与类型：0-发起者,1-参与者")
    private Integer userJoinType;

    @ApiModelProperty(value = "城市ID")
    private String realCityId;
    @ApiModelProperty(value = "用户openid")
    private String userId;
}
