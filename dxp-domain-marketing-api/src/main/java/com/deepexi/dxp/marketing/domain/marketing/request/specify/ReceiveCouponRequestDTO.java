package com.deepexi.dxp.marketing.domain.marketing.request.specify;


import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 领券请求参数
 */

@ApiModel
@Data
public class  ReceiveCouponRequestDTO extends AbstractObject implements Serializable {

    @ApiModelProperty("用户昵称")
    @NotNull(message = "用户昵称不能为空")
    private String nickName;

    @ApiModelProperty("用户名称")
    @NotNull(message = "用户名称不能为空")
    private String userName;

    @ApiModelProperty("手机号")
    @NotNull(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty("活动id")
    @NotNull(message = "活动id不能为空")
    private Long activityId;

    @ApiModelProperty("历史优惠券/资源ID")
    @NotNull(message = "优惠券ID不能为空")
    private Long hisResourceId;

    @ApiModelProperty("0、微信小程序  1、H5  2、抖音")
    @NotNull(message = "渠道类型不能为空")
    private Integer type;

    @ApiModelProperty("unionId")
    private String unionId;


    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("openId")
    private String openId;

    @ApiModelProperty("userId")
    private String userId;
    /* ************************下单返回信息 start **************************** */

    @ApiModelProperty(value = "签名，使用字段appId、timeStamp、nonceStr、package计算得出的签名值")
    private String paySign;

    @ApiModelProperty(value = "签名类型，默认为RSA，仅支持RSA")
    private String signType;

    @ApiModelProperty(value = "订单详情扩展字符串")
    private String packageRes;

    @ApiModelProperty(value = "随机字符串")
    private String nonceStr;

    @ApiModelProperty(value = "时间戳")
    private String timeStamp;

    @ApiModelProperty(value = "h5Url")
    private String h5Url;
    /* ************************下单返回信息 end **************************** */

    /* **************** 抖音相关 ******************/
    @ApiModelProperty(value = "抖音返回")
    DyPayInfo dyPayInfo;
    /* **************** 抖音相关 ******************/
    @Data
    @ApiModel
    public static class DyPayInfo extends AbstractObject implements Serializable {
        @ApiModelProperty(value = "时间戳")
        private String timestamp;

        @ApiModelProperty(value = "版本")
        private String version;

        @ApiModelProperty(value = "签名")
        private String sign;

        @ApiModelProperty(value = "用户唯一标志")
        private String uid;

        @ApiModelProperty(value = "CNY")
        private String currency;

        @ApiModelProperty(value = "主题")
        private String subject;

        @ApiModelProperty(value = "内容")
        private String body;

        @ApiModelProperty(value = "商户号")
        private String merchant_id;

        @ApiModelProperty(value = "app_id")
        private String app_id;

        @ApiModelProperty(value = "签名类型")
        private String sign_type;

        @ApiModelProperty(value = "交易类型")
        private String trade_type;

        @ApiModelProperty(value = "产品编码")
        private String product_code;

        @ApiModelProperty(value = "产品类型")
        private String payment_type;

        @ApiModelProperty(value = "订单号码,基础交易服务所定义的订单号")
        private String out_order_no;

        @ApiModelProperty(value = "金额，单位是分")
        private int total_amount;

        @ApiModelProperty(value = "交易时间")
        private String trade_time;

        @ApiModelProperty(value = "有效交易时间")
        private String valid_time;

        @ApiModelProperty(value = "回调地址")
        private String notify_url;

        @ApiModelProperty(value = "微信h5支付地址")
        private String wx_url;

        @ApiModelProperty(value = "微信h5支付交易类型")
        private String wx_type;

        @ApiModelProperty(value = "支付风控参数")
        private String risk_info;
    }
}
