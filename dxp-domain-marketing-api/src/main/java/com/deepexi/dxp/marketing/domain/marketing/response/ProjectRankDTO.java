package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: HT
 * @CreateTime: 2022/5/11
 */
@Data
@ApiModel(value = "项目维度排行", description = "项目维度排行")
public class ProjectRankDTO extends CityRankDTO {

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;


}
