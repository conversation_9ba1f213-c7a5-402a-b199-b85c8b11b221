package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 不同类型的活动的参与次数
 *
 * <AUTHOR> xinjian.yao
 * @date 2020/3/16 15:44
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel
public class StatisticsUserActivityCountVO extends AbstractObject implements Serializable {


    private static final long serialVersionUID = 8630233388168736307L;
    @ApiModelProperty("不同类型的活动的参与次数")
    private List<StatisticsUserActivityCountDetailDTO> activityCountResponseDTOList;


}
