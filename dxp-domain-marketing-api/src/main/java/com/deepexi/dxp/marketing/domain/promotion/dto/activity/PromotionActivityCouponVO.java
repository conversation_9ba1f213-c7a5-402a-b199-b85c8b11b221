package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.dxp.marketing.domain.promotion.dto.coupon.PromotionCouponDetailPostResponseDTO;
import com.deepexi.util.domain.dto.BaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @description: 活动-优惠券分页查询返回
 * @author: xu.mingwei
 * @version: 1.0
 * @date: 2021/3/17 16:50
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionActivityCouponVO extends BaseDTO {
    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;
    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String activityName;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private Integer status;
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;
    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdPerson;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 优惠券信息
     */
    @ApiModelProperty("优惠券信息")
    private PromotionCouponDetailPostResponseDTO couponDetail;
    /**
     * 活动策略的规则
     */
    @ApiModelProperty(value = "活动策略的规则")
    private List<ActivityRuleDTO> activityRuleDTOList;
    /**
     * 渠道限制
     */
    @ApiModelProperty(value = "渠道限制")
    private List<BaseActivityDTO> tenantLimit;
    /**
     * 会员限制
     */
    @ApiModelProperty(value = "会员限制")
    private List<BaseActivityDTO> userLimit;

}
