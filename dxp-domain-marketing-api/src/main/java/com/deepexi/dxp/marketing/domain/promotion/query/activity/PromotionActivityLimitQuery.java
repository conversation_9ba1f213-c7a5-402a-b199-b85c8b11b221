package com.deepexi.dxp.marketing.domain.promotion.query.activity;

import com.deepexi.util.domain.dto.BaseExtDTO;
import com.deepexi.util.domain.query.BaseExtQuery;
import com.deepexi.util.domain.request.BaseExtRequest;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 促销活动限制查询入参
 *
 * <AUTHOR> xinji<PERSON>.yao
 * @date 2020/3/11 14:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class PromotionActivityLimitQuery extends BaseExtDTO {

    @ApiModelProperty("用户限制")
    private String userLimit;

    @ApiModelProperty("优惠券限制")
    private String couponLimit;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty("渠道类型==信息")
    @NotBlank(message = "渠道信息不能为空")
    private String tenantId;
    @ApiModelProperty("应用id")
    @NotNull(message = "应用id不能为空")
    private Long appId;
    @ApiModelProperty("活动状态")
    private List<Integer> activityStatusList;
    @ApiModelProperty("活动类型")
    private List<Integer> paTemplateList;
    /**
     * 查询的页码(-1 表示查询所有数据)
     */
    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;

    @ApiModelProperty("店铺id")
    private Long shopId;

    @ApiModelProperty("渠道id")
    private Long channelId;
}
