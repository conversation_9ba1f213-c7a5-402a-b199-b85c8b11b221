package com.deepexi.dxp.marketing.domain.promotion.query.coupon;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 用户优惠券表条件查询
 *
 * <AUTHOR> ming.zhong
 * @date created in 14:37 2019/12/2
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class PromotionCouponLoggerQuery extends SuperQuery {


    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("优惠券 id")
    private Long couponId;

    @ApiModelProperty("来源 id")
    private Long releaseId;

    @ApiModelProperty("订单 id")
    private String orderId;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("使用状态")
    private Integer status;

    @ApiModelProperty("使用状态列表")
    private List<Integer> statusList;

    @ApiModelProperty(value = "用户类型")
    private Integer userType;

    @ApiModelProperty(value = "release_time")
    private Integer releaseType;

    @ApiModelProperty(value = "amount")
    private Integer amount;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updatedTime;
}
