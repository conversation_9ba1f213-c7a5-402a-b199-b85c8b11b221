package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: HT
 * @CreateTime: 2022/5/11
 */
@Data
@NoArgsConstructor
public class CityRankDTO {

    @ApiModelProperty(value = "城市id")
    private String cityId;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "点赞数量")
    private Integer likesNum;

    public CityRankDTO(String cityId, String cityName) {
        this.cityId = cityId;
        this.cityName = cityName;
    }
}
