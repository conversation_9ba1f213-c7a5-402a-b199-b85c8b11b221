package com.deepexi.dxp.marketing.domain.promotion.query.specify;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class PromotionResourceQuery extends SuperQuery {

    @ApiModelProperty("资源名称")
    private String name;


    @ApiModelProperty("资源类型")
    private Integer type;

    private Integer whetherAll;
}
