package com.deepexi.dxp.marketing.domain.promotion.query.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;

@Data
@ApiModel("参与活动黑名单分页查询表单")
public class PromotionBlackListPageQuery {
    @ApiModelProperty(value = "城市ID")
    private String cityId;

    @ApiModelProperty(value = "关键字（匹配姓名或电话）")
    private String keyword;

    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;
}
