package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/9/21 14:52
 */
@Data
public class AgentInfoDTO {

    @ApiModelProperty(value = "agentId")
    private String agentId;
    @ApiModelProperty(value = "经纪人姓名")
    private String agentName;

    @ApiModelProperty(value = "经纪人注册手机号")
    private String agentMobile;

    @ApiModelProperty(value = "经纪人身份类别")
    private String agentTypeNo;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "实名认证日期")
    private Date verifiedDate;

    @ApiModelProperty(value = "黑名单标识")
    private String blackList;

    @ApiModelProperty(value = "冻结标识")
    private String frozenFlag;

    @ApiModelProperty(value = "身份证号")
    private String idCardNo;

    @ApiModelProperty(value = "身份证类型编码")
    private String idCardType;
}
