package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.FeedbackInfoVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "PromotionActivity返回对象", description = "PromotionActivity返回对象")
public class ActivityFormFeedbackResponseVO extends SuperVO {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @NotNull(message = "项目ID不能为空")
    private String projectId;
    /**
     *项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @NotBlank(message = "项目名称不能为空")
    @Length(max = 32, message = "项目名称不能超过32个字")
    private String projectName;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private String userId;
    /**
     * 微信昵称
     */
    @ApiModelProperty(value = "微信昵称")
    private String nickName;
    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;
    private String fullPhone;
    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动ID不能为空")
    private Long activityId;
    /**
     *资源奖品ID
     */
    @ApiModelProperty(value = "资源奖品ID")
    @NotNull(message = "资源奖品ID不能为空")
    private Long resourceId;
    /**
     * 0、表单活动,1、其它活动
     */
    @ApiModelProperty(value = "活动类型")
    @NotNull(message = "活动类型不能为空")
    @Range(min = 0,max = 1 ,message = "活动类型不合法")
    private Integer activityType;
    /**
     * unionId
     */
    @ApiModelProperty(value = "unionId")
    private String unionId;
    /**
     * 0、微信小程序 1、H5  2、抖音
     */
    @ApiModelProperty(value = "来源")
    @NotNull(message = "来源不能为空")
    @Range(min = 0,max = 3 ,message = "来源不合法")
    private Integer type;

    @ApiModelProperty(value = "用户表单数据")
    private Map<String,Object> limits;

    @ApiModelProperty(value = "用户表单数据")
    private FeedbackInfoVO feedbackVO;


    @ApiModelProperty(value = "活动访问人数")
    private Long accessPeopleNumber;

    @ApiModelProperty(value = "活动参与人数")
    private Long joinPeopleNumber;

    @ApiModelProperty(value = "活动留资人数")
    private Long lzPeopleNumber;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;
}
