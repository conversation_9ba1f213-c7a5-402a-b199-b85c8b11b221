package com.deepexi.dxp.marketing.domain.marketing.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 账户表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@Accessors(chain = true)
@ApiModel(value="AccountInfo对象", description="账户表")
public class AccountInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "客户主档Id")
    private Long customerMainId;

    @ApiModelProperty(value = "账户来源")
    private String sourceCode;

    @ApiModelProperty(value = "账户来源名称 置业通 客服小程序(优家) 股份企微 物业企微 优生活小程序 优生活APP")
    private String sourceName;

    @ApiModelProperty(value = "union_id")
    private String unionId;

    @ApiModelProperty(value = "用户id/open_id")
    private String openId;

    @ApiModelProperty(value = "注册电话")
    private String telephone;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "性别")
    private Integer gender;

    @ApiModelProperty(value = "头像地址")
    private String pictureLink;

    @ApiModelProperty(value = "app id")
    private String appId;

    @ApiModelProperty(value = "是否删除")
    private Boolean deleted;

}
