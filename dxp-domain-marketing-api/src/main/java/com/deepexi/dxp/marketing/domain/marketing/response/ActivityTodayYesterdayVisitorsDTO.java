package com.deepexi.dxp.marketing.domain.marketing.response;


import lombok.Data;

/**
 * 神策统计总数量，今日数量，明日数量
 * <AUTHOR>
 */
@Data
public class ActivityTodayYesterdayVisitorsDTO {

    /**
     * 活动id
     */
    private String distinctId;

    /**
     * 神策事件名
     */
    private String event;
    /**
     * 活动id
     */
    private String activityId;

    /**
     * 统计总数量
     */
    private Long totalNumber;
    /**
     * 今日统计数量
     */
    private Long todayNumber;
    /**
     * 昨日统计数量
     */
    private Long yesterdayNumber;


    /**
     * 统计总人数
     */
    private Long totalPeopleNumber;
    /**
     * 今日统计总人数
     */
    private Long todayPeopleNumber;
    /**
     * 昨日统计总人数
     */
    private Long yesterdayPeopleNumber;

}
