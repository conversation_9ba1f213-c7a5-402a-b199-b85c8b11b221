package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 拼团活动基本返回信息
 *
 * <AUTHOR> xianfeng.cai
 * @date 2019/11/28 11:10
 */
@Data
@ApiModel
public class GroupInfoDTO extends AbstractObject {

    @ApiModelProperty(value = "开团类型；1-普通团，2-老带新")
    private Integer groupType;
    @ApiModelProperty("活动价格")
    private BigDecimal activityPrice;
    @ApiModelProperty("团主优惠标识 1是 0否")
    private Integer discountFlag;
    @ApiModelProperty("团主价格")
    private BigDecimal discountPrice;
    @ApiModelProperty("参团人数")
    private Integer peopleNumber;
    @ApiModelProperty("有效时间;计量单位:秒(s)")
    private Long duration;
    @ApiModelProperty("活动库存数量")
    private Integer inventoryNumber;
    @ApiModelProperty("活动商品信息")
    private ActivityCommodityDTO activityCommodityDTO;
}
