package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * 创建活动DTO
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/21 14:08
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
@ToString
public class PromotionActivityListPostRequest extends AbstractObject {

    @ApiModelProperty("0 全款可退 1 定金可退 2 不可退 （默认是0）")
    private Integer returnMoneyType;
    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private Integer status;


    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private Date findTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private Date endTime;


    /**
     * 活动限制id
     */
    @ApiModelProperty(value = "活动限制id")
    private Integer limitId;

    /**
     * 活动策略id
     */
    @ApiModelProperty(value = "活动策略id")
    private Integer strategyId;


    /**
     * 0 有用优惠券 1 不能用优惠券
     */
    @ApiModelProperty(value = "0 有用优惠券 1 不能用优惠券")
    private Boolean couponsFlag;

    /**
     * 自增主键
     */
    @ApiModelProperty(value = "自增主键")
    private Long id;

    /**
     * 租户编码
     */
    @ApiModelProperty(value = "租户编码")
    private String tenantId;

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    private Long appId;

    /**
     * 版本号，乐观锁
     */
    @ApiModelProperty(value = "版本号，乐观锁")
    private Long version;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 逻辑删除
     */
    @ApiModelProperty(value = "逻辑删除")
    private Integer deleted = 0;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}