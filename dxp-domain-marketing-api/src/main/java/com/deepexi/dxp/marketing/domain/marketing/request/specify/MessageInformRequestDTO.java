package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "短信通知请求对象", description = "短信通知请求对象")
@Data
public class MessageInformRequestDTO {
    /**
     * 电话号码
     */
    private String phone;
    /**
     * 模板Id
     */
    private String templateId;
    /**
     * 模板参数
     */
    private String templateContent;
}
