package com.deepexi.dxp.marketing.domain.promotion.request.coupon;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import com.deepexi.dxp.marketing.common.base.request.SuperRequestExt;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 创建用户优惠券表
 *
 * <AUTHOR> ming.zhong
 * @date created in 14:36 2019/12/2
 */
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class PromotionCouponLoggerCreatePostRequest extends SuperRequestExt {

    private static final long serialVersionUID = 1L;

    /**
     * 用户优惠券code
     */
    private String code;
    /**
     * 优惠券起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    /**
     * 优惠券结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    @ApiModelProperty("优惠券 id")
    @NotNull(message = "优惠券id不能为空")
    private Long couponId;

    @ApiModelProperty("订单id")
    private String orderId;

    @ApiModelProperty("来源 id ")
    @NotNull(message = "来源id不能为空")
    private Long releaseId;

    @ApiModelProperty("来源类型：0-活动，1-会员 6-第三方")
    @NotNull(message = "来源类型不能为空")
    private Integer releaseType;

    @ApiModelProperty("发放数量 默认为1")
    private Integer amount;

    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @ApiModelProperty("使用状态 0 未使用 1已使用 2 已失效")
    @NotNull(message = "使用状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "用户类型")
    @NotNull(message = "用户类型不能为空")
    private Integer userType;

    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标记
     */
    private Integer deleted = 0;

    /**
     * 乐观锁
     */
    @ApiModelProperty("乐观锁")
    private Long version;
}
