package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageShareVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动专题分析dto
 * <AUTHOR>
 */
@ApiModel(value = "活动专题分析返回参数", description = "活动专题分析返回参数")
@Data
public class ActivityTopicAnalysisResponseDTO extends SuperVO {
    /**
     * 活动专题名称
     */
    @ApiModelProperty("活动专题访问次数")
    private Long visitorsNumber;

    @ApiModelProperty("活动专题访问人数")
    private Long visitorsPeopleNumber;

    @ApiModelProperty("专题分享次数")
    private Long shareNumber;

    @ApiModelProperty("专题分享人数")
    private Long sharePeopleNumber;
}
