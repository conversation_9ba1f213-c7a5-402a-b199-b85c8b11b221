package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@ApiModel(value = "退款返回参数", description = "退款返回参数")
@Data
public class WxRefundsResponseDTO {

    @ApiModelProperty(value = "200代表成功，其余失败。如果失败，请在msg里面填入具体内容")
    private String code;

    @ApiModelProperty(value = "msg")
    private String msg;

    @ApiModelProperty(value = "业务信息字段，json格式")
    private  WxRefundsResponseResult data;

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("退款返回数数")
    public static class WxRefundsResponseResult extends AbstractObject implements Serializable {

        @ApiModelProperty(value = "支付平台唯一订单号")
        private String orderNo;

        @ApiModelProperty(value = "退款金额")
        private Integer refund;

        @ApiModelProperty(value = "原业务订单号")
        private String bizOrderNo;

        @ApiModelProperty(value = "退款状态，如果是SUCCESS，则不会再异步通知")
        private String status;
    }
}
