package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.baomidou.mybatisplus.annotation.TableField;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "场景码组织机构参数", description = "场景码组织机构参数")
public class MiniOrgRequestDTO  extends AbstractObject {

    @ApiModelProperty(value = "区域ID")
    private String areaId;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "城市ID")
    private String cityId;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "城市ID")
    private String realCityId;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String realCityName;

    @ApiModelProperty(value = "项目代码")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;
}
