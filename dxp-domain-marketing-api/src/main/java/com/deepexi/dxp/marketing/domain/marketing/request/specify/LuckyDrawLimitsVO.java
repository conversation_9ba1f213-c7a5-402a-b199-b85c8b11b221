package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 规则配置入参对象
 * <AUTHOR>
 */
@ApiModel(value = "规则配置入参对象", description = "规则配置入参对象")
@Data
public class LuckyDrawLimitsVO {
    /**
     * 参与类型
     */
    @ApiModelProperty(value = "参与类型（0按天计算，1按人计算）")
    @NotNull(message = "参与类型不能为空")
    @Size(max = 1,message = "参与类型输入有误")
    private Integer joinType;

    /**
     * 每日/每人抽奖次数
     */
    @ApiModelProperty(value = "每日/每人可抽奖次数")
    @Size(max = 10,message = "限制输入1-10")
    private Integer dailyDrawNumber;

    /**
     * 分享可额外抽奖次数
     */
    @ApiModelProperty(value = "分享可额外抽奖次数")
    @Size(max = 10,message = "限制输入1-10")
    private Integer shareDrawNumber;

    /**
     * 每天/每人可分享次数
     */
    @ApiModelProperty(value = "每天/每人可分享次数")
    @Size(min = 1,max = 10,message = "限制输入1-10")
    private Integer dailyShareNumber;

    @ApiModelProperty(value = "奖品配置")
    @NotNull(message = "奖品配置不能为空")
    @Valid
    private List<PrizeConfigVO> prizeList;

    @ApiModelProperty(value = "信息登记")
    @NotNull(message = "信息登记不能为空")
    @Valid
    private List<EnrollmentInfoVO> feedbackInfo;

}
