package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityParticipationVO;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Data
@ApiModel(value = "ActivityOrder返回对象", description = "ActivityOrder返回对象")
public class ActivityOrderResponseVO extends SuperVO {


    /**
     *活动ID
    **/
    @ApiModelProperty(value = "活动ID")
    private Long activityId;
    /**
     *活动图标
    **/
    private String activityIconUrl;
    /**
     *资源ID
    **/
    @ApiModelProperty(value = "资源ID")
    private Long resourceId;
    /**
     *用户ID
    **/
    @ApiModelProperty(value = "用户ID")
    private String userId;
    /**
     *订单编码
    **/
    @ApiModelProperty(value = "订单编码")
    private String code;
    /**
     *支付金额
    **/
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payMoney;
    /**
     *支付时间
    **/
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date payTime;
    /**
     *支付状态
    **/
    @ApiModelProperty(value = "支付状态")
    private Integer status;

    /**
     *unionId
    **/
    @ApiModelProperty(value = "unionId")
    private String unionId;
    /**
     *0、微信小程序  1、H5  2、抖音
    **/
    @ApiModelProperty(value = "0、微信小程序  1、H5  2、抖音")
    private Integer type;

    @ApiModelProperty(value = "活动名称")
    private String activityName;
    @ApiModelProperty(value="活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date activityStartTime;

    @ApiModelProperty(value="活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date activityEndTime;

    @ApiModelProperty(value="活动扩展字段")
    private Map<String, Object> ext;

    @ApiModelProperty(value = "优惠券面值")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "支付平台订单号")
    private String wxOrderNo;

    @ApiModelProperty(value = "订单关闭时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "资源名称")
    private String resourceName;

    @ApiModelProperty(value = "原价")
    private String costPrice;

    @ApiModelProperty(value = "折扣价")
    private String discountPrice;

    @ApiModelProperty(value = "资源类型 0、卡劵 1、第三方接入")
    private Integer resourceType;

    @ApiModelProperty(value = "细分类别 0、普通劵 1、房源劵 2、商品劵")
    private Integer couponCategory;

    private String nickName;

    private String phone;

    private String userName;

    private String openId;

    private String projectId;


    private String paySign;

    @ApiModelProperty(value = "签名类型，默认为RSA，仅支持RSA")
    private String signType;

    @ApiModelProperty(value = "订单详情扩展字符串")
    private String packageRes;

    @ApiModelProperty(value = "随机字符串")
    private String nonceStr;

    @ApiModelProperty(value = "时间戳")
    private String timeStamp;

    @ApiModelProperty(value = "资源图片")
    private String url;

    @ApiModelProperty(value = "卡劵类型 0、代金劵 1、折扣劵")
    private Integer couponType;

    @ApiModelProperty(value = "h5Url")
    private String h5Url;

    @ApiModelProperty(value = "paTemplateId")
    private Integer paTemplateId;

    private List<ActivityParticipationVO> projectInfoList;
    /* **************** 抖音相关 ******************/
    @ApiModelProperty(value = "抖音返回")
    DyPayInfo dyPayInfo;
    /* **************** 抖音相关 ******************/
    @Data
    @ApiModel
    public static class DyPayInfo extends AbstractObject implements Serializable {
        @ApiModelProperty(value = "时间戳")
        private String timestamp;

        @ApiModelProperty(value = "版本")
        private String version;

        @ApiModelProperty(value = "签名")
        private String sign;

        @ApiModelProperty(value = "用户唯一标志")
        private String uid;

        @ApiModelProperty(value = "CNY")
        private String currency;

        @ApiModelProperty(value = "主题")
        private String subject;

        @ApiModelProperty(value = "内容")
        private String body;

        @ApiModelProperty(value = "商户号")
        private String merchant_id;

        @ApiModelProperty(value = "app_id")
        private String app_id;

        @ApiModelProperty(value = "签名类型")
        private String sign_type;

        @ApiModelProperty(value = "交易类型")
        private String trade_type;

        @ApiModelProperty(value = "产品编码")
        private String product_code;

        @ApiModelProperty(value = "产品类型")
        private String payment_type;

        @ApiModelProperty(value = "订单号码,基础交易服务所定义的订单号")
        private String out_order_no;

        @ApiModelProperty(value = "金额，单位是分")
        private int total_amount;

        @ApiModelProperty(value = "交易时间")
        private String trade_time;

        @ApiModelProperty(value = "有效交易时间")
        private String valid_time;

        @ApiModelProperty(value = "回调地址")
        private String notify_url;

        @ApiModelProperty(value = "微信h5支付地址")
        private String wx_url;

        @ApiModelProperty(value = "微信h5支付交易类型")
        private String wx_type;

        @ApiModelProperty(value = "支付风控参数")
        private String risk_info;
    }
}