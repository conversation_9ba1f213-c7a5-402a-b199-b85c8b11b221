package com.deepexi.dxp.marketing.domain.marketing.request.specify;


import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 领券请求参数
 */

@ApiModel
@Data
public class PartakeLogToOrderExtDTO extends AbstractObject implements Serializable {

    @ApiModelProperty("用户昵称")
    @NotNull(message = "用户昵称不能为空")
    private String nickName;

    @ApiModelProperty("用户名称")
    @NotNull(message = "用户名称不能为空")
    private String userName;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("活动id")
    @NotNull(message = "活动id不能为空")
    private Long activityId;

    @ApiModelProperty("历史优惠券/资源ID")
    @NotNull(message = "历史优惠券ID不能为空")
    private Long hisResourceId;

    @ApiModelProperty("0、微信小程序  1、H5  2、抖音")
    @NotNull(message = "渠道类型不能为空")
    private Integer type;

    @ApiModelProperty("unionId")
    private String unionId;


    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("openId")
    private String openId;

    @ApiModelProperty("用户信息登记")
    private ActivityFormFeedbackDTO activityFormFeedbackDTO;

    /* ************************下单返回信息 start **************************** */

    @ApiModelProperty(value = "签名，使用字段appId、timeStamp、nonceStr、package计算得出的签名值")
    private String paySign;

    @ApiModelProperty(value = "签名类型，默认为RSA，仅支持RSA")
    private String signType;

    @ApiModelProperty(value = "订单详情扩展字符串")
    private String packageRes;

    @ApiModelProperty(value = "随机字符串")
    private String nonceStr;

    @ApiModelProperty(value = "时间戳")
    private String timeStamp;
    /* ************************下单返回信息 end **************************** */

}
