package com.deepexi.dxp.marketing.domain.promotion.query.specify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024/12/16 14:57
 */
@Data
public class VerifyOrderPageQuery {

    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;
    @ApiModelProperty(value = "活动ID")
    private List<Long> activityIdList;
    /** 核销状态 */
    private Integer verifyStatus;

    /** 订单状态 */
    private Integer status;

    /** 核销开始时间 */
    private LocalDate startTime;

    /** 核销结束时间 */
    private LocalDate endTime;

    /** 用户昵称，手机，活动名称模糊查询 */
    private String keyword;

    /** 发布渠道 */
    @NotBlank
    private String deliveryChannel;

    /** 项目ID */
    private String projectId;

    @ApiModelProperty("0-不需要押金，1-需要押金")
    private Integer depositPaid;
    public LocalDate getEndTime() {
        if (endTime != null) {
            return endTime.plusDays(1);
        }
        return null;
    }

}
