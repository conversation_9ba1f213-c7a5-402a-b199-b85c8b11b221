package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import com.deepexi.dxp.marketing.common.base.dto.SuperExtDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ComdityLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.TagItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 创建活动DTO
 *
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/11/21 14:08
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionActivityCreatePostRequest extends SuperExtDTO {
    @ApiModelProperty("0 全款可退 1 定金可退 2 不可退 （默认是0）")
    private Integer returnMoneyType;
    @ApiModelProperty("活动id")
    private Long activityId;
    @ApiModelProperty("渠道id")
    @NotBlank(message = "渠道id不能为空")
    private String tenantId;
    @ApiModelProperty("appId")
    @NotNull(message = "appId不能为空")
    private Long appId;
    /**
     * 模板id 活动从模板来的 所以不能为空
     */
    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    @NotNull(message = "模板id不能为空")
    private Integer paTemplateId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotBlank(message = "活动名称不能为空")
    private String activityName;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    @NotNull(message = "活动状态不能为空")
    private Integer status;
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @NotNull(message = "活动开始时间不能为空")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @NotNull(message = "活动结束时间不能为空")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("创建人")
    private String createdPerson;
    @ApiModelProperty("商品限制")
    @NotNull(message = "商品限制不能为空")
    private ComdityLimitDTO commodity;
    @ApiModelProperty("活动策略的规则")
    @NotNull(message = "活动策略的规则不能为空")
    private List<ActivityRuleDTO> activityRuleDTOList;
    @ApiModelProperty("渠道限制")
    @NotNull(message = "渠道限制不能为空")
    private List<BaseActivityDTO> tenantLimit;
    @ApiModelProperty("会员限制")
    @NotNull(message = "会员限制不能为空")
    private List<BaseActivityDTO> userLimit;
    @ApiModelProperty("数量限制")
    @NotNull(message = "数量限制不能为空")
    private List<BaseActivityDTO> numberLimit;
    @ApiModelProperty("优惠券限制")
    @NotNull(message = "优惠券限制不能为空")
    private List<BaseActivityDTO> couponLimit;

    @ApiModelProperty("门店限制")
    @NotNull(message = "门店限制不能为空")
    private List<BaseActivityDTO> shopLimit;


    //===========华发===============

    @ApiModelProperty("抽奖限制")
    private List<BaseActivityDTO> luckyDrawLimit;
    @ApiModelProperty("砍价限制")
    private List<BaseActivityDTO> bargainLimit;

    @ApiModelProperty("助力限制")
    private List<BaseActivityDTO> assistLimit;

    @ApiModelProperty("表单限制")
    private List<BaseActivityDTO> formLimit;
    @ApiModelProperty("集卡限制")
    private List<BaseActivityDTO> cardCollectingLimit;

    @ApiModelProperty(value = "适用项目列表")
    @NotNull(message = "适用项目列表不能为空")
    private List<ActivityParticipationVO> projectIds;

//    @ApiModelProperty(value = "活动扩展信息")
//    @NotNull(message = "活动扩展信息不能为空")
//    @Valid
//    private ActivityExtVO activityExtVO;
    /**
     * 规则配置
     */
    @ApiModelProperty(value = "规则配置")
    @NotNull(message = "规则配置不能为空")
    @Valid
    private RuleConfigVO ruleConfigVO;

    @ApiModelProperty(value = "奖品配置")
    @NotNull(message = "奖品配置不能为空")
    @Valid
    private List<PrizeConfigVO> prizeList;

    /**
     * 界面配置
     */
    @ApiModelProperty(value = "活动页配置")
    @NotNull(message = "活动页配置不能为空")
    @Valid
    private ActivityPageVO activityPageVO;

    @ApiModelProperty(value = "集卡活动卡片配置")
    private List<ActivityCardVO> activityCardList;

    @ApiModelProperty(value = "分享页配置")
    @NotNull(message = "分享页配置不能为空")
    @Valid
    private ActivityPageShareVO activityPageShareVO;
    //===========华发===============

    @ApiModelProperty("userId")
    private String userId;
    @ApiModelProperty(value = "活动标签")
    private List<TagItemDTO> tags;
}