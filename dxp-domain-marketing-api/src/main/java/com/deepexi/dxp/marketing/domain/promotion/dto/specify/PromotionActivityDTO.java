package com.deepexi.dxp.marketing.domain.promotion.dto.specify;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@ApiModel("活动DTO")
@Data
public class PromotionActivityDTO implements Serializable {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value="模板ID")
    private Integer paTemplateId;

    @ApiModelProperty(value="名称")
    private String name;

    @ApiModelProperty(value="编码")
    private String code;

    @ApiModelProperty(value="项目类型")
    private Integer projectType;

    @ApiModelProperty(value="适用项目")
    private String projects;

    @ApiModelProperty(value="活动状态")
    private Integer status;

    @ApiModelProperty(value="上架状态")
    private Integer upperStatus;

    @ApiModelProperty(value="活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value="活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("组织机构类型")
    private Integer orgType;

    @ApiModelProperty("场景码")
    private String sceneCode;

    @ApiModelProperty(value="投放渠道")
    private String deliveryChannel;

    @ApiModelProperty(value="应用隔离")
    private Long appId;

    @ApiModelProperty(value="扩展字符串")
    private String extStr;
    @ApiModelProperty(value="扩展字段")
    private Map<String, Object> ext = new HashMap<>();
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;
}
