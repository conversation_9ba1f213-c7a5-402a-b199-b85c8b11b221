package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 立即领取请求dto
 * <AUTHOR>
 */
@Data
@ApiModel
public class ReceiveNowDTO {
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目ID")
    //@NotBlank(message = "请选择项目！")
    private String projectId;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    /**
     * 项目城市
     */
    @ApiModelProperty(value = "项目城市")
    private String projectCity;

    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     *用户ID
     **/
    @ApiModelProperty(value = "用户Id")
    private String userId;

    /**
     * 奖品信息主键id
     **/
    @ApiModelProperty(value = "奖品信息主键id")
    @NotNull(message = "奖品信息主键id不能为空")
    private Long id;

    /**
     *用户ID
     **/
    @ApiModelProperty(value = "奖品id")
    @NotNull(message = "奖品id不能为空")
    private Long resourceId;
    /**
     *用户ID
     **/
    @ApiModelProperty(value = "奖品名称")
//    @NotNull(message = "奖品名称不能为空")
    private String resourceName;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    private String idCard;

    /**
     * 所在地区
     */
    @ApiModelProperty(value = "所在地区")
    private String area;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String detaAddress;

    /**
     * unionId
     */
    @ApiModelProperty(value = "unionId")
    private String unionId;


    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty(value = "0、微信小程序  1、H5  2、抖音")
    private Integer type;


    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty(value = "中奖id")
    @NotNull(message = "中奖id不能为空")
    private Long winLotteryId;
}
