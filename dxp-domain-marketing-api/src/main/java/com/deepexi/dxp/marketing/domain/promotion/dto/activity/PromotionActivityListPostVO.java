package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.domain.dto.BaseExtDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 创建活动DTO
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/21 14:08
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class PromotionActivityListPostVO extends BaseExtDTO {
    @ApiModelProperty("0 全款可退 1 定金可退 2 不可退 （默认是0）")
    private Integer returnMoneyType;
    @ApiModelProperty("活动id")
    private Long activityId;
    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;
    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId;
    @ApiModelProperty(value = "模板name")
    private String paTemplateName;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String activityName;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private Integer status;
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;

    @ApiModelProperty("创建人")
    private String createdPerson;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;

    @ApiModelProperty("活动备注")
    private String remark;
    @ApiModelProperty("商品限制")
    private ComdityLimitDTO commodity;
    @ApiModelProperty("活动策略的规则")
    private List<ActivityRuleDTO> activityRuleDTOList;
    @ApiModelProperty("渠道限制")
    private List<BaseActivityDTO> tenantLimit;
    @ApiModelProperty("会员限制")
    private List<BaseActivityDTO> userLimit;
    @ApiModelProperty("数量限制")
    private List<BaseActivityDTO> numberLimit;
    @ApiModelProperty("优惠券限制")
    private List<BaseActivityDTO> couponLimit;
    @ApiModelProperty("门店限制")
    private List<BaseActivityDTO> shopLimit;

    //

    /**
     * 0 有用优惠券 1 不能用优惠券
     */
    @ApiModelProperty(value="0 有用优惠券 1 不能用优惠券")
    private Boolean couponsFlag;

    /**
     * 适用项目
     */
    @ApiModelProperty(value="适用项目")
    private String projects;


    @ApiModelProperty(value="领取方式:0-免费领取,1-付费领取")
    private List<Integer> receiveModeList;

}