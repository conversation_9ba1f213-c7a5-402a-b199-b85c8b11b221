package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.domain.query.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class HisResourceRequestDTO extends BaseQuery implements Serializable {

    @ApiModelProperty("活动id")
    @NotNull(message = "活动id不能为空")
    private Long activityId;

    @ApiModelProperty("领取方式")
    //@NotNull(message = "领取方式不能为空")
    private Integer receiveMode;

    @ApiModelProperty("助力资源类型:0-助力阶梯,1-分享获奖,2-帮忙助力获奖")
    private Integer fissonResourceType;
}
