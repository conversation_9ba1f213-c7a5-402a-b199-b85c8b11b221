package com.deepexi.dxp.marketing.domain.promotion.query.specify;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class PromotionHisResourceQuery extends SuperQuery {

    @ApiModelProperty("资源名称")
    private List<Long> ids;

    @ApiModelProperty("资源名称")
    private String name;

    @ApiModelProperty("活动id")
    private Long activityId;


    @ApiModelProperty("资源类型")
    private Integer type;


    @ApiModelProperty(value = "奖品id")
    private Long resourceId;

    @ApiModelProperty(value = "活动ids")
    private List<Long> activityIds;

    @ApiModelProperty(value = "领取方式")
    private Integer  receiveMode;
}
