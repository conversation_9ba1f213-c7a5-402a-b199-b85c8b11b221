package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 上架ID限制
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/9/21 14:21
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
@ToString
public class UpIdDTO extends BaseActivityDTO {

    private static final long serialVersionUID = -8755171163661387694L;

    @ApiModelProperty("上架ID")
    private String upId;

}
