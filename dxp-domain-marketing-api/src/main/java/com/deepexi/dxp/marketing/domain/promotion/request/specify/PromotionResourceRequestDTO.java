package com.deepexi.dxp.marketing.domain.promotion.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel
public class PromotionResourceRequestDTO extends AbstractObject {


    @ApiModelProperty("主键")
    private Long id;
    /**
     * 资源类型 0、卡劵 1、第三方接入
     */

    @ApiModelProperty(value = "资源类型",required = true)
    @NotNull(message = "资源类型不能为空")
    private Integer type;
    /**
     * 细分类别 0、普通劵 1、房源劵 2、商品劵
     */
    @ApiModelProperty("细分类别")
    private Integer couponCategory;
    /**
     * 第三方细分类别 0、手机话费 1、现金红包
     */
    @ApiModelProperty("第三方细分类别")
    private Integer thirdCategory;
    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称",required = true)
    @NotNull(message = "名称不能为空")
    @Length(min = 1, max = 20, message = "名称长度不能超过20个字符")
    private String name;
    /**
     * 卡劵类型 0、代金劵 1、折扣劵
     */
    @ApiModelProperty("卡劵类型")
    private Integer couponType;
    /**
     * 卡劵面值
     */
    @ApiModelProperty("卡劵面值")
    private BigDecimal couponValue;
    /**
     * 资源图片
     */
    @ApiModelProperty(value = "资源图片",required = true)
    @NotNull(message = "图片不能为空")
    @Length(min = 1, max = 500, message = "图片地址超长")
    private String url;
    /**
     * 使用时间类型 0 、不限制 1、指定时间 2、有效天数
     */
    @ApiModelProperty("使用时间类型")
    private Integer validTimeType;

    @ApiModelProperty("礼品券发放方式")
    private Integer grantWay;

    /**
     * 有效期开始时间
     */
    @ApiModelProperty("有效期开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validStartTime;
    /**
     * 有效期结束时间
     */
    @ApiModelProperty("有效期结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validEndTime;
    /**
     * 有效天数
     */
    @ApiModelProperty("有效天数")
    private Integer validDay;
    /**
     * 使用规则
     */

    @ApiModelProperty("使用规则")
    @Length(max = 5000, message = "使用规则最多只能输入5000个字符")
    private String useRule;
    /**
     * 房源名称
     */
    @Length(max = 20, message = "房源名称最多只能输入20个字符")
    @ApiModelProperty("房源名称")
    private String houseName;
    /**
     * 房源面积
     */
    @Length(max = 20, message = "房源面积最多只能输入20个字符")
    @ApiModelProperty("房源面积")
    private String houseVolume;
    /**
     * 房源信息
     */
    @ApiModelProperty("房间信息")
    @Length(max = 20, message = "房间信息最多只能输入20个字符")
    private String houseMessage;
    /**
     * 原价
     */
    @ApiModelProperty("原价")
    private String costPrice;
    /**
     * 折扣价
     */
    @ApiModelProperty("折扣价")
    private String discountPrice;

    @ApiModelProperty("原单价或原总价类型:0-原单价,1-原总价")
    private Integer costPriceType;

    @ApiModelProperty("折后总价及折后单价类型:0-折后单价,1-折后总价")
    private Integer discountPriceType;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String itemName;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private String tenantId;
    /**
     * 应用id
     */
    @ApiModelProperty("appId")
    private Long appId;
    /**
     * 优惠卷code
     */
    @ApiModelProperty("优惠卷code")
    private String code;

    @ApiModelProperty("创建人")
    private String createdBy;


    @ApiModelProperty("更新人")
    private String updatedBy;
}
