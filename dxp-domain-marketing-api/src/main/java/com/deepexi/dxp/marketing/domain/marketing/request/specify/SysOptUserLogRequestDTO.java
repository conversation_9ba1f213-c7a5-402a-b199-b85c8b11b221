package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "用户信息登记，上报信息登记参数", description = "用户信息登记，上报信息登记参数")
public class SysOptUserLogRequestDTO extends AbstractObject {


    private Integer accountType;//3.0 add

    @ApiModelProperty(value = "活动id,直播id")
    private Integer actId;

    @ApiModelProperty(value = "活动名称，直播名称")
    private String actName;


    private String agentId;//3.0 add

    /*@ApiModelProperty(value = "aicall客轮次")
    private Integer aiCallCount;*/ //3.0 del

    @ApiModelProperty(value = "appId")
    private String appId;

    private String cityId;//3.0 add 对应表单城市ID

    private String cityName; // 3.0 add 对应城市名称

    /*@ApiModelProperty(value = "触达批次id")
    private Integer batchId;*/ //3.0 del

    private Integer customerPotentialChannelId;//3.0 add

    /*@ApiModelProperty(value = "callStatus")
    private Integer callStatus;*/ // 3.0 del

    @ApiModelProperty(value = "来源类型，1：潜客池，2：后台导入，3：工作台新增")
    private Integer dataType;// 3.0 add

    /*@ApiModelProperty(value = "createTime")
    private String createTime;

    @ApiModelProperty(value = "结束时间 浏览或aicall用")
    private String endTime;*/ //3.0 del

    @ApiModelProperty(value = "头像")
    private String headImgUrl;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "ip")
    private String ip;

   /* @ApiModelProperty(value = "是否分配 1分配")
    private Integer isAllot;

    @ApiModelProperty(value = "是否是粉丝 0否，1是")
    private Integer isFans;

    @ApiModelProperty(value = "是否有效 1有效 0 无效")
    private Integer isValid;*/ //3.0 del

   private String  mes; //3.0 add

    @ApiModelProperty(value = "手机号")
    private String mobile;//表单填的

    private String name;//3.0 add //表单填

    @ApiModelProperty(value = "昵称")
    private String nickname;

    @ApiModelProperty(value = "openId")
    private String openId;

    @ApiModelProperty(value = "来源页面id")
    private Integer pageId;

    @ApiModelProperty(value = "来源页面标题")
    private String pageTitle;

    @ApiModelProperty(value = "华发项目id")
    private String projectId;
    /*
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "role")
    private Integer role;

    @ApiModelProperty(value = "如是projectId")
    private Integer rsProjectId;

    @ApiModelProperty(value = "销售姓名")
    private String salerName;

    @ApiModelProperty(value = "销售open_id")
    private String salerOpenId;

    @ApiModelProperty(value = "销售类型")
    private Integer salerRole;*/ // 3.0 del

    private String remark; //3.0 add

    private String saleTeamId;//3.0 add

    private String saleTeamName;//3.0 add

    @ApiModelProperty(value = "销售userKey")
    private String saleUserKey;//3.0 add

    private String sex;//3.0 add


    @ApiModelProperty(value = "来源 0:h5 1:微信小程序 2 活动 3直播 4安居客")//3.0 update feebacktype:0、微信小程序 1、H5  2、抖音
    private Integer source;

   /* @ApiModelProperty(value = "转介来源项目id")
    private Integer sourceProjectId;

    @ApiModelProperty(value = "转介来源项目名称")
    private String sourceProjectName;

    @ApiModelProperty(value = "转介来源销售名称")
    private String sourceSalerName;

    @ApiModelProperty(value = "转介来源销售openid")
    private String sourceSalerOpenId;*/ //3.0 del

    @ApiModelProperty(value = "来源方式:1:阅读、2:分享、3:去电、4:留电、5:关注、6:问卷、7:直播、8:活动、" +
            "9:aicall客、10:话费、11:流量、12:置业通授权、13:置业通浏览、14:转介、15:天猫16百度小程序埋点 " +
            "17百度小程序授权上报 18安居客 19房天下 20百度 21腾讯 22新浪乐居 23贝壳24 搜狐 25一物一码")
    private Integer sourceType;

    @ApiModelProperty(value = "转介来源详情表id")
    private Integer transferDetailsId;//3.0 add

    /*@ApiModelProperty(value = "开始时间 浏览或aicall用")
    private String startTime;*/ //3.0 del

    @ApiModelProperty(value = "unionId")
    private String unionId;

    /*@ApiModelProperty(value = "userId")
    private String userId;

    @ApiModelProperty(value = "销售userKey")
    private String userKey;*/ //3.0 del
}
