package com.deepexi.dxp.marketing.domain.marketing.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "裂变活动助力/砍价返回对象", description = "裂变活动助力/砍价返回对象")
public class ActivityFissionLogResponseDTO extends SuperVO {

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "活动ID")
    private String userName;

    /**
     * 用户电话
     */
    @ApiModelProperty(value = "用户电话")
    private String phone;

    /**
     * 砍价金额
     */
    @ApiModelProperty(value = "砍价金额")
    private BigDecimal money;

    /**
     * unionId
     */
    @ApiModelProperty(value = "unionId")
    private String unionId;

    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty(value = "0、微信小程序  1、H5  2、抖音")
    private Integer type;

    @ApiModelProperty(value = "能力值")
    private Integer  helpNumSet;

    @ApiModelProperty(value = "助力获取数值定义")
    private String assistNumDefinition;

    @ApiModelProperty(value = "微信头像")
    private String avatar;

    @ApiModelProperty(value = "助力获取的奖品id")
    private Long assistResourceId;

    @ApiModelProperty(value = "助力获取的奖品名称")
    private String assistResourceName;

    @ApiModelProperty(value="备注")
    private String remark;
}
