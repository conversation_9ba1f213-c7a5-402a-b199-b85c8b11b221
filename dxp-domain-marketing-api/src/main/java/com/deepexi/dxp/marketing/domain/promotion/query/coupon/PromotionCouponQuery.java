package com.deepexi.dxp.marketing.domain.promotion.query.coupon;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠券查询
 *
 * <AUTHOR> ming.zhong
 * @date created in 14:35 2019/11/28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class PromotionCouponQuery extends AbstractObject {

    @ApiModelProperty(value = "优惠券id批量查询")
    private List<Long> ids;
    @ApiModelProperty(value = "优惠券id查询")
    private Long id;
    /**
     * 优惠券名称
     */
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 优惠券类型
     */
    @ApiModelProperty(value = "优惠券类型")
    private String couponType;

    /**
     * 代金券面值|折扣券折扣
     */
    @ApiModelProperty(value = "代金券面值|折扣券折扣")
    private BigDecimal couponValue;

    /**
     * 是否启用 0 禁用 1 启用
     */
    @ApiModelProperty(value = "是否启用 0 禁用 1 启用")
    private Integer status;

    /**
     * 使用条件适用金额
     */
    @ApiModelProperty(value = "使用条件适用金额")
    private BigDecimal condition;

    /**
     * 使用限制json
     */
    @ApiModelProperty(value = "使用限制json")
    private String limits;
    /**
     * 查询的页码(-1 表示查询所有数据)
     */
    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;

    /**
     * 租户隔离
     */
    @ApiModelProperty(value = "租户隔离")
    private String tenantId;

    /**
     * 应用隔离
     */
    @ApiModelProperty(value = "应用隔离")
    private Long appId;
}
