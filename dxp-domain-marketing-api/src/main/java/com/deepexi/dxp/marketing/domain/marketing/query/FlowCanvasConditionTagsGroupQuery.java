package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 流程画布-条件节点—标签客群配置
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
@NoArgsConstructor
public class FlowCanvasConditionTagsGroupQuery extends SuperQuery {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 主键id列表
     */
    private List<Long> ids;

    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    private String nodeId;

    /**
     * 标签或客群，1:标签;2:客群
     */
    private Long type;

    /**
     * 判断条件名称
     */
    private String name;

    /**
     * 计算符号
     */
    private String symbols;

    /**
     * 标签ID或客群ID
     */
    private Long tagId;

    /**
     * 标签或客群名称
     */
    private String tagName;

    public FlowCanvasConditionTagsGroupQuery (Long flowId, String tenantId, Long appId){
        this.flowId = flowId;
        this.setTenantId(tenantId);
        this.setAppId(appId);
    }
}
