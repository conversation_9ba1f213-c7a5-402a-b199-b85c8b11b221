package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.LocationSignRuleDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 签到结果
 */
@Data
@ApiModel
public class LocationSignResponseDTO implements Serializable {

    @ApiModelProperty(value = "打卡地点")
    private String location;

    @ApiModelProperty(value = "获得的奖品规则")
    private List<LocationSignRuleVO> applyRules;

}
