package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "通用营销系统用户组织返回对象", description = "通用营销系统用户组织返回对象")
public class CommonOrgResponseDTO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "组织ID")
    private String orgId;

    @ApiModelProperty(value = "组织名称")
    private String orgName;
}
