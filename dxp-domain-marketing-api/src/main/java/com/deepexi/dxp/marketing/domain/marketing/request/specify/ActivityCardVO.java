package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 活动页入参对象
 * <AUTHOR>
 */
@ApiModel(value = "集卡活动卡片入参对象", description = "集卡活动卡片入参对象")
@Data
public class ActivityCardVO extends AbstractObject implements Serializable {

    @ApiModelProperty("卡片名称")
    private String name;
    @ApiModelProperty("卡片图片")
    private String img;


}
