package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动专题分析dto
 * <AUTHOR>
 */
@ApiModel(value = "活动专题分析返回参数", description = "活动专题分析返回参数")
@Data
public class ActivityTopicAnalysisActivityInfoResponseDTO extends SuperVO {
    /**
     * 活动专题名称
     */
    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("适用项目")
    private String projects;

    @ApiModelProperty("活动访问人数")
    private Long visitorsPeopleNumber;

    @ApiModelProperty("活动分享人数")
    private Long sharePeopleNumber;

    @ApiModelProperty("活动参与人数")
    private Long joinPeopleNumber;

    @ApiModelProperty("活动核销人数")
    private Long writeOffPeopleNumber;
}
