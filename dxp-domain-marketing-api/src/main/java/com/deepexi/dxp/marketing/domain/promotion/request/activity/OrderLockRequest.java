package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityLoggerCreatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.coupon.PromotionCouponLoggerCreatePostRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 订单锁定入参
 *
 * <AUTHOR> xin<PERSON>an.yao
 * @date 2019/12/17 11:43
 */
@Data
@ApiModel
public class OrderLockRequest {
    @ApiModelProperty("活动锁定接口")
    @NotNull(message = "活动锁定数据不可为空")
    @Valid
    List<PromotionActivityLoggerCreatePostRequest> activityList;


    @ApiModelProperty("优惠券锁定接口")
    @Valid
    List<PromotionCouponLoggerCreatePostRequest> couponList;


}
