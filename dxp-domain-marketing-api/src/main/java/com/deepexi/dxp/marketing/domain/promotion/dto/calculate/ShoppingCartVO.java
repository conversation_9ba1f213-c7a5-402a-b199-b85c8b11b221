package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车返回list
 *
 * <AUTHOR> xinjian.yao
 * @date 2019/12/2 16:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class ShoppingCartVO extends AbstractObject {


    @ApiModelProperty("有活动的商品")
    private List<ActivityResponseParamsDTO> activityList;

    @ApiModelProperty("没有活动的商品")
    private List<ActivityCommodityDTO> activityCommodityDTOList;

    @ApiModelProperty("订单原始金额")
    private BigDecimal orderDetailPrice;

    @ApiModelProperty("订单优惠后金额")
    private BigDecimal orderDiscountsPrice;




}
