package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperExtVO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHisDetailResponseDTO;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 抽奖、秒杀、优惠券的用户参与记录表
 * <AUTHOR>
 */
@Data
@ApiModel(value = "用户抽奖记录返回对象", description = "用户抽奖记录返回对象")
public class ActivityPartakeLogResponseDTO extends SuperExtVO {
    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phone;
    private String fullPhone;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    private String idCard;

    /**
     * 所在地区
     */
    @ApiModelProperty(value = "所在地区")
    private String area;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String detaAddress;

    /**
     * 中奖结果
     */
    @ApiModelProperty(value = "中奖结果")
    private String prizeResult;

    /**
     * 兑奖码
     */
    @ApiModelProperty(value = "兑奖码")
    private String code;

    /**
     * 领奖时间
     */
    @ApiModelProperty(value = "领奖时间")
    private String getTime;

    /**
     * 核销时间
     */
    @ApiModelProperty(value = "核销时间")
    private String verifyTime;
    /**
     * 核销人
     */
    @ApiModelProperty(value = "核销人")
    private String verifyBy;

    /**
     * 活动订单id
     */
    @ApiModelProperty(value = "活动订单id")
    private String orderId;

    /**
     * 活动订单编号
     */
    @ApiModelProperty(value = "活动订单No")
    private String orderNo;

    /**
     * 奖品ID
     */
    @ApiModelProperty(value = "奖品ID")
    private Long resourceId;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date payTime;

    /**
     *  支付金額
     */
    @ApiModelProperty(value = "支付金額")
    private BigDecimal payMoney;

    /**
     * unionId
     */
    @ApiModelProperty(value = "unionId")
    private String unionId;

    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty(value = "0、微信小程序  1、H5  2、抖音")
    private Integer type;

    /**
     * 微信头像URL
     */
    @ApiModelProperty(value = "微信头像URL")
    private String avatar;

    /**
     * 所需助力人数
     */
    @ApiModelProperty(value = "所需助力人数")
    private Integer needFissonCount;

    /**
     * 当前已助力人数
     */
    @ApiModelProperty(value = "当前已助力人数")
    private Integer currentFissonCount;

    /**
     * 当前已砍价价格
     */
    @ApiModelProperty(value = "当前已砍价价格")
    private BigDecimal currentFissonPrice;


    /**
     * 剩余助力时长（剩余砍价时长）
     */
    @ApiModelProperty(value = "剩余助力时长（剩余砍价时长）")
    private Date fissonEndTime;

    /**
     * 助力砍价状态 0、助力中（砍价中），1、助力成功（砍价成功）,2、助力失败（砍价失败）
     */
    @ApiModelProperty(value = "助力砍价状态 0、助力中（砍价中），1、助力成功（砍价成功）,2、助力失败（砍价失败）")
    private Integer fissonStatus;

    /**
     * 裂变活动类型：0、点击类助力活动；1、砍价活动
     */
    @ApiModelProperty(value = "裂变活动类型：0、点击类助力活动；1、砍价活动")
    private Integer fissonType;

    /**
     * 礼品券发放方式:0-线上邮寄,1-线下核销
     */
    @ApiModelProperty(value = "礼品券发放方式:0-线上邮寄,1-线下核销")
    private String grantWay;

    /**
     * 登记明细
     */
    @ApiModelProperty(value = "登记明细")
    private ActivityFormFeedbackDTO feedback;


    @ApiModelProperty(value = "发放时间（yyyy-MM-dd）")
    private String issuanceTime;


    @ApiModelProperty(value = "资源信息")
    private ResourceHisDetailResponseDTO resourceHisDetailResponseDTO;

    @ApiModelProperty(value = "获得礼品")
    private String resourceNames;

    @ApiModelProperty(value = "助力剩余时长")
    private String surplusTimeFission;

    @ApiModelProperty(value = "序号")
    private Integer serialNum;


    @ApiModelProperty(value = "活动访问人数")
    private Long accessPeopleNumber;

    @ApiModelProperty(value = "活动参与人数")
    private Long joinPeopleNumber;

    @ApiModelProperty(value = "活动留资人数")
    private Long lzPeopleNumber;


    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "视频Id")
    private Long videoId;

    @ApiModelProperty(value = "视频封面路径")
    private String coverUrl;

    @ApiModelProperty(value = "项目名称")
    private String videoTitle;

    @ApiModelProperty(value = "视频路径")
    private String videoUrl;

    @ApiModelProperty(value = "账号简介")
    private String accountInfo;

    @ApiModelProperty(value = "顾问key")
    private String userKey;
}
