package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/12/4 20:54
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel
public class CommodityActivityVO extends AbstractObject {
    @ApiModelProperty("sku编码")
    private String upId;
    @ApiModelProperty("sku编码")
    private String skuId;

    @ApiModelProperty("商品下的活动编码")
    private List<PromotionActivityListPostVO> promotionActivityListPostResponseDTOList;
}
