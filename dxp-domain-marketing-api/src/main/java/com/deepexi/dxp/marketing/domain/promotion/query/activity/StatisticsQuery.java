package com.deepexi.dxp.marketing.domain.promotion.query.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 查询用户数据入参
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2020/3/16 15:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class StatisticsQuery extends AbstractObject implements Serializable {


    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    private Long userId;
    @ApiModelProperty("用户类型")
    @NotNull(message = "用户类型不能为空，如果是会员 请传0")
    private Integer userType;

    @ApiModelProperty("渠道类型==信息")
    @NotBlank(message = "渠道信息不能为空")
    private String tenantId;
    @ApiModelProperty("应用id")
    @NotNull(message = "应用id不能为空")
    private Long appId;
}
