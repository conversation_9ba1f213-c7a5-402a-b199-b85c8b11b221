package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHisDetailResponseDTO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "助力可领取奖品", description = "助力可领取奖品")
public class ActivityFissionAssistResourceResponseDTO extends AbstractObject implements Serializable {

    private Long id;

    @ApiModelProperty(value = "资源ID")
    private Long resourceId;

    @ApiModelProperty(value = "是否领取:0-未领取，1-已领取")
    private Integer received = 0;

    @ApiModelProperty(value = "阶梯号")
    private Integer ladderSort;

    @ApiModelProperty(value = "资源名称")
    private String name;

    @ApiModelProperty(value = "资源url")
    private String url;

    @ApiModelProperty(value = "partakeLogId")
    private Long partakeLogId;

    @ApiModelProperty(value = "type")
    private Integer type;

    @ApiModelProperty(value = "phone")
    private String phone;

    @ApiModelProperty(value = "activityId")
    private Long activityId;

    @ApiModelProperty(value = "资源信息")
    private ResourceHisDetailResponseDTO resourceHisDetailResponseDTO;

    @ApiModelProperty(value = "当前剩余资源数量")
    private Long currentQuantity = 0L;

    @ApiModelProperty(value = "是否达到阶梯:0-未达到，1-已达到")
    private Integer isCompleted = 0;

}
