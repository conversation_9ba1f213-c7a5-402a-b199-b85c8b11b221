package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "活动项目返回对象", description = "活动项目返回对象")
public class ActivityParticipationDTO {
    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;

    /**
     * 项目代码
     */
    @ApiModelProperty(value = "项目代码")
    private String projectId;

    /**
     * 区域ID
     */
    @ApiModelProperty(value = "区域ID")
    private String areaId;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaName;


    /**
     * 城市ID
     */
    @ApiModelProperty(value = "城市ID")
    private String cityId;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    /**
     * 城市ID
     */
    @ApiModelProperty(value = "城市ID")
    private String realCityId;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String realCityName;

    /**
     * 组织代码
     */
    @ApiModelProperty(value = "组织代码")
    private String orgId;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String orgName;

    /**
     * 分期列表
     */
    @ApiModelProperty(value = "分期列表")
    private String porjectPeriodList;

    /**
     * 	岗位编码
     */
    @ApiModelProperty(value = "岗位编码")
    private String positionId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目序号
     */
    @ApiModelProperty(value = "项目序号")
    private String projectNumber;

    /**
     * 销售组织代码
     */
    @ApiModelProperty(value = "销售组织代码")
    private String saleOrgId;
    /**
     * 销售组织名称
     */
    @ApiModelProperty(value = "销售组织名称")
    private String saleOrgName;

    /**
     * 用户别名
     */
    @ApiModelProperty(value = "用户别名")
    private String userAlias;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;
}
