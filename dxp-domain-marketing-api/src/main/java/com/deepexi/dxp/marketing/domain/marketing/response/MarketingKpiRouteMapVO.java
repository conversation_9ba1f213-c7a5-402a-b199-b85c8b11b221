package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 指标路径图VO
 * @Author: HuangBo.
 * @Date: 2020/6/18 10:33
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Api("指标图路径")
public class MarketingKpiRouteMapVO extends SuperVO {


    /**
     * 路径图名称
     */
    @ApiModelProperty(value = "路径图名称" ,required = true)
    @NotBlank(message = "路径图名称不能为空")
    @Length(max = 20, message = "路径图名称不能超过20个字符")
    private String name;

    /**
     * 1:启用; 2:禁用
     */
    @ApiModelProperty(value = "1,启用,2.禁用")
    @Range(min = 1,max = 2 ,message = "状态值不合法")
    private Integer status;

    /**
     * 路径图节点信息数据(xml字符)
     */
    @ApiModelProperty(value = "路径图节点信息数据" ,required = true)
    @NotBlank(message = "路径图节点信息数据不能为空")
    private String mapData;

    @ApiModelProperty(value = "描述")
    @Length(max = 200, message = "路径图描述不能超过200个字符")
    private String remark;

    /**
     * 路径图节点集合
     */
    @ApiModelProperty(value = "路径图节点集合" ,required = true)
    @NotEmpty(message = "路径图节点集合不能为空")
    private List<MarketingKpiRouteMapNodeVO> mapNodeList;

}
