package com.deepexi.dxp.marketing.domain.promotion.request.specify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
public class PromotionBlackListCreateRequest {
    @NotBlank(message = "地理城市ID不能为空")
    @ApiModelProperty(value = "地理城市ID", required = true)
    private String cityId;

    @NotBlank(message = "地理城市名称不能为空")
    @ApiModelProperty(value = "地理城市名称", required = true)
    private String cityName;

    @NotBlank(message = "用户名不能为空")
    @ApiModelProperty(value = "用户名", required = true)
    private String name;

    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty(value = "手机号", required = true)
    private String phone;

    @NotNull(message = "禁止报名期限不能为空")
    @ApiModelProperty(value = "禁止报名期限（天）", required = true)
    @Min(1)
    private Integer restrictionDays;

    @NotNull(message = "禁止报名开始日期不能为空")
    @ApiModelProperty(value = "禁止报名开始日期", required = true)
    private LocalDate startDate;

    @ApiModelProperty(value = "限制原因说明")
    private String restrictionReason;
    @ApiModelProperty(value="投放渠道")
    private String deliveryChannel = "6";
}
