package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 模板限制
 *
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/11/22 16:19
 */
@Data
@ApiModel
public class BaseTemplateLimitDTO {
    @ApiModelProperty("模板id")
    private String id;
    @ApiModelProperty("配置的名称")
    private String value;


}
