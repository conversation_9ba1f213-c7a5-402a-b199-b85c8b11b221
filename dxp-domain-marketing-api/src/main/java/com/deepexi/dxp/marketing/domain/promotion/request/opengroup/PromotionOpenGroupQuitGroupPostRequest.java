package com.deepexi.dxp.marketing.domain.promotion.request.opengroup;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户退团入参DTO
 *
 * <AUTHOR> xianfeng.cai
 * @date 2019/11/29 14:08
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionOpenGroupQuitGroupPostRequest extends SuperDTO {

    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;
    @ApiModelProperty("退团id")
    private Long activityGroupId;
    @ApiModelProperty("退团用户Id")
    private Long userId;

}