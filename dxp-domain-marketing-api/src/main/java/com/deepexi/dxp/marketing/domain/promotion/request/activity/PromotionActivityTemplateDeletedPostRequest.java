package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 活动模板删除入参
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/21 10:29
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class PromotionActivityTemplateDeletedPostRequest extends AbstractObject {
    /**
     * 自增主键
     */
    @ApiModelProperty(value = "自增主键")
    private Long id;

    /**
     * 租户编码
     */
    @ApiModelProperty(value = "租户编码")
    private String tenantId;

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    private Long appId;

    /**
     * 版本号，乐观锁
     */
    @ApiModelProperty(value = "版本号，乐观锁")
    private Long version;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 逻辑删除
     */
    @ApiModelProperty(value = "逻辑删除")
    private Integer deleted = 0;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;



}
