package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 创建活动DTO
 * <AUTHOR>
 * @version 1.0
 * @date 2020-08-17 17:19
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionActivityUpdateStatusRequest extends AbstractObject {

    @ApiModelProperty(value = "id",required = true)
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态",required = true)
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 是否定时结束
     */
    @ApiModelProperty(value = "是否定时结束，0-否  1-是",required = true)
    @NotNull(message = "定时结束标识不能为空")
    private Integer isSchedule;

}