package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 流程画布-执行-更换属性
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
@NoArgsConstructor
public class FlowCanvasExecuteChangePropertyQuery extends SuperQuery {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 主键id列表
     */
    private List<Long> ids;

    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    private String nodeId;

    /**
     * 属性code
     */
    private String propertyCode;

    /**
     * 属性名称
     */
    private String propertyName;

    /**
     * 更换的属性值
     */
    private String propertyValue;

    public FlowCanvasExecuteChangePropertyQuery (Long flowId, String tenantId, Long appId){
        this.flowId = flowId;
        this.setTenantId(tenantId);
        this.setAppId(appId);
    }
}
