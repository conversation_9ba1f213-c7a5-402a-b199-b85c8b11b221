package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "活动分析详情返回对象", description = "活动分析详情返回对象")
public class ActivityAnalysisDetailResponseDTO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动模板")
    private String template;

    @ApiModelProperty(value="活动开始时间")
    private String startTime;

    @ApiModelProperty(value="活动结束时间")
    private String endTime;


    @ApiModelProperty(value = "指标概览")
//    private OverviewOfIndicatorsDTO overviewOfIndicators;
    private List<IndexPathResponseDTO> overviewOfIndicators;

    @ApiModelProperty(value = "指标路径")
    private List<IndexPathResponseDTO> indexPathResponseDTO;


    @ApiModelProperty(value = "活动趋势")
    private List<ActivityTrendResponseDTO> activityTrendResponseDTO;

    @ApiModelProperty(value = "渠道分布")
    private List<ChannelDistributionResponseDTO> channelDistributionList;

    @ApiModelProperty(value = "城市分布")
    private List<CityDistributionResponseDTO> cityDistributionList;

    @ApiModelProperty(value = "活动期间到访人数")
    private Integer visitPeopleNumber;

    @ApiModelProperty(value = "活动转到访人数")
    private Integer goVisitPeopleNumber;

    @ApiModelProperty(value = "活动期间成交人数")
    private Integer dealPeopleNumber;

    @ApiModelProperty(value = "活动转成交人数")
    private Integer goDealPeopleNumber;
}
