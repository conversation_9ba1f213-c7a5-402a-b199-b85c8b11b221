package com.deepexi.dxp.marketing.domain.promotion.dto.specify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 标签参数
 * @date 2024/11/5 10:30
 */
@Data
public class TagItemDTO {
    @ApiModelProperty("字典主键ID")
    private Long id;
    @ApiModelProperty("字典名称")
    private String name;
    @ApiModelProperty("父级字典名称【字典类别】")
    private String parentName;
}
