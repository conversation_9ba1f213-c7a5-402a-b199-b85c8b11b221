package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 砍价参与者请求参数
 * <AUTHOR>
 */
@ApiModel(value = "BargainParticipantRequestDTO入参对象", description = "BargainParticipantRequestDTO入参对象")
@Data
public class BargainParticipantRequestDTO {

    /**
     * 创建人
     */
    @ApiModelProperty("参与用户")
    @NotNull(message = "参与用户ID不能为空")
    private String userId;


    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动ID不能为空")
    private Long activityId;


    /**
     * 用户昵称
     */
    @ApiModelProperty("用户昵称")
    @NotNull(message = "用户昵称不能为空")
    private String nickName;

    /**
     * 用户昵称
     */
    @ApiModelProperty("用户名")
    @NotNull(message = "用户名不能为空")
    private String userName;

    /**
     * 用户昵称
     */
    @ApiModelProperty("手机号")
    @NotNull(message = "手机号不能为空")
    private String phone;

    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty("0、微信小程序  1、H5  2、抖音")
    @NotNull(message = "渠道类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "unionId")
    private String unionId;
}
