package com.deepexi.dxp.marketing.domain.promotion.query.usercredit;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;


/**
 * 充值赠送活动下单检查入参对象.
 *
 * <AUTHOR> fengjun
 * @date 2020/03/16 14:08
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionUserCreditCheckQuery extends SuperQuery {

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long id;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id，当前不校验店铺信息，后续迭代需要使用该值缩小数据范围")
    private Long shopId;

    /**
     * 渠道id
     */
    @ApiModelProperty(value = "渠道id", required = true)
    @NotNull(message = "渠道id不能为空！")
    private Long channelId;

    /**
     * 会员id
     */
    @ApiModelProperty(value = "会员id", required = true)
    @NotNull(message = "会员id不能为空！")
    private Long memberId;

    /**
     * 会员分组id集合
     */
    @ApiModelProperty(value = "会员分组id集合")
    private List<Long> menberGroupId;

    /**
     * 主键 idList
     */
    @ApiModelProperty("主键idList")
    private List<Long> idList;

    /**
     * version
     */
    @ApiModelProperty("version")
    private Long version;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updatedTime;

}