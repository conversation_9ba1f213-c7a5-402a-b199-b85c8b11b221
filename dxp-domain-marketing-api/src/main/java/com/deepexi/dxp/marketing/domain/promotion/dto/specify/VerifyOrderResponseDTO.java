package com.deepexi.dxp.marketing.domain.promotion.dto.specify;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024/12/16 15:00
 */
@Data
public class VerifyOrderResponseDTO {

    @ApiModelProperty("订单编码")
    private String code;

    @ApiModelProperty("核销编码")
    private String verifyCode;

    @ApiModelProperty("用户昵称")
    private String nickName;

    @ApiModelProperty("用户手机号")
    private String phone;

    @ApiModelProperty("活动名称")
    private String name;

    @ApiModelProperty("订单状态")
    private Integer status;

    @ApiModelProperty("支付时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" ,timezone="GMT+8")
    private Date payTime;

    @ApiModelProperty("数量")
    private Integer total;

    @ApiModelProperty("支付金额")
    private BigDecimal payMoney;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("核销状态")
    private Integer verifyStatus;

    @ApiModelProperty("核销人")
    private String verifyBy;
    @ApiModelProperty("0-不需要押金，1-需要押金")
    private Integer depositPaid;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" ,timezone="GMT+8")
    @ApiModelProperty("核销时间")
    private Date verifyTime;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" ,timezone="GMT+8")
    @ApiModelProperty("创建时间")
    private Date createdTime;
}
