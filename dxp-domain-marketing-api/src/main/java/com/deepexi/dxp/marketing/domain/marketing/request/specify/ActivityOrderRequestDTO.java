package com.deepexi.dxp.marketing.domain.marketing.request.specify;



import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.OrderPayResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;


@ApiModel(value = "ActivityOrderRequestDTO入参对象", description = "ActivityOrderRequestDTO返回对象")
@Data
public class ActivityOrderRequestDTO  extends SuperDTO {



    /**
     *活动ID
    **/
    @ApiModelProperty(value = "活动ID",required = true)
    private Long activityId;
    /**
     *资源ID
    **/
    @ApiModelProperty(value = "资源ID",required = true)
    private Long resourceId;
    /**
     *用户ID
    **/
    @ApiModelProperty(value = "用户ID",required = true)
    private String userId;

    @ApiModelProperty(value = "用户昵称",required = true)
    private String nickName;

    @ApiModelProperty(value = "用户名称",required = true)
    private String userName;

    @ApiModelProperty(value = "手机号",required = true)
    private String phone;

    @ApiModelProperty("身份证")
    private String idCard;

    /**
     *订单编码
    **/
    @ApiModelProperty(value = "订单编码")
    private String code;
    /**
     *支付金额
    **/
    @ApiModelProperty(value = "支付金额",required = true)
    private BigDecimal payMoney;

    /**
     *支付状态
    **/
    @ApiModelProperty(value = "支付状态")
    private Integer status;
    /**
     *拓展字段
    **/
    @ApiModelProperty(value = "拓展字段")
    private Map<String, Object> ext;
    /**
     *unionId
    **/
    @ApiModelProperty(value = "unionId")
    private String unionId;
    /**
     *0、微信小程序  1、H5  2、抖音
    **/
    @ApiModelProperty(value = "0、微信小程序  1、H5  2、抖音",required = true)
    private Integer type;

    @ApiModelProperty(value = "支付平台返回的订单号")
    private String wxOrderNo;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    //支付需要用到的信息
    @ApiModelProperty(value = "下单支付返回信息")
    private OrderPayResponseDTO orderPayResponseDTO;

}