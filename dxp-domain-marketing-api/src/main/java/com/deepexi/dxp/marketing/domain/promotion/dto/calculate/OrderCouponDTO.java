package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/12/14 10:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class OrderCouponDTO extends AbstractObject {
    /**
     * 主键
     */
    @ApiModelProperty(value = "优惠券id")
    private Long id;
    /**
     * 优惠券名称
     */
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;
    /**
     * 优惠券编码
     */
    @ApiModelProperty(value = "优惠券编码")
    private String couponCode;

}
