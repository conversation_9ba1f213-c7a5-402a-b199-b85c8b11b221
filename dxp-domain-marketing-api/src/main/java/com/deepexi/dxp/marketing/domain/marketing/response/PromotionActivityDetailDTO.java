package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.dto.SuperExtDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageShareVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityParticipationVO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHisDetailResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.TagItemDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 创建活动VO
 * <AUTHOR>
 * @version 1.0
 * @date 2020-08-17 17:19
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionActivityDetailDTO extends SuperExtDTO {
    @ApiModelProperty("活动id")
    private Long activityId;
    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;
    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String activityName;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private Integer status;
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;

    /**
     * 活动参数次数
     */
    @ApiModelProperty(value = "活动参数次数")
    private Integer activityJoinQty;

    @ApiModelProperty("渠道限制")
    private List<BaseActivityDTO> tenantLimit;
    @ApiModelProperty("会员限制")
    private List<BaseActivityDTO> userLimit;
    @ApiModelProperty("数量限制")
    private List<BaseActivityDTO> numberLimit;

    //==============================华发=================================
    @ApiModelProperty("优惠券活动限制")
    private List<BaseActivityDTO> couponLimit;

    @ApiModelProperty("抽奖限制")
    private List<BaseActivityDTO> luckyDrawLimit;

    @ApiModelProperty("砍价限制")
    private List<BaseActivityDTO> bargainLimit;

    @ApiModelProperty("助力限制")
    private List<BaseActivityDTO> assistLimit;

    @ApiModelProperty("表单限制")
    private List<BaseActivityDTO> formLimit;

    @ApiModelProperty("集卡限制")
    private List<BaseActivityDTO> cardCollectingLimit;

    @ApiModelProperty(value = "适用项目列表")
    private List<ActivityParticipationVO> projectInfoList;

    //优惠券配置信息
    private List<ResourceHisDetailResponseDTO> prizeList;


    /**
     * 界面配置
     */
    @ApiModelProperty(value = "活动页配置")
    private ActivityPageVO activityPageVO;


    @ApiModelProperty(value = "分享页配置")
    private ActivityPageShareVO activityPageShareVO;

    @ApiModelProperty(value = "活动目标名称")
    private String activityTargetName;

    @ApiModelProperty(value = "指标路径名称")
    private String marketingKpiRouteMapName;

    //==============================华发=================================

    //专门给移动端展示的项目列表，以地理城市分组
    List<ActivityParticipationGroupResponseDTO> frontProjectList;

    @ApiModelProperty(value = "活动标签")
    private List<TagItemDTO> tags;

}
