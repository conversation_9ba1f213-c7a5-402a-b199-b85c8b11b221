package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/11/22 16:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class ActivityRuleDTO extends AbstractObject {
    @ApiModelProperty("规则的条件")
    private List<BaseActivityDTO> condition;
    @ApiModelProperty("规则的结果")
    private List<UpIdDTO> operation;
    @ApiModelProperty("规则的优先级")
    private String sort;
    @ApiModelProperty("规则的类型")
    private String strategyType;
}
