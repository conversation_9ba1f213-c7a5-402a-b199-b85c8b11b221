package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR> xinjian.yao
 * @date 2020/3/16 16:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class StatisticsUserActivityCountDetailDTO extends AbstractObject implements Serializable {
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户类型")
    private Integer userType;

    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId;
    @ApiModelProperty(value = "模板name")
    private String paTemplateName;

    @ApiModelProperty("活动参与的次数")
    private Integer activitySize;

}
