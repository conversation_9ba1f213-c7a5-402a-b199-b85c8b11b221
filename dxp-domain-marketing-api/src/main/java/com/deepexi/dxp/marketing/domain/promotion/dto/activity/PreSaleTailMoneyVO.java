package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 活动过期的List
 *
 * <AUTHOR> xinjian.yao
 * @date 2020/3/19 18:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class PreSaleTailMoneyVO extends AbstractObject {

    @ApiModelProperty("活动过期的List")
    private List<PreSaleTailDateDTO> activityIdList;

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ApiModel
    public static class PreSaleTailDateDTO extends AbstractObject implements Serializable {

        @ApiModelProperty("活动id")
        private Long activityId;

        /**
         * 订单号
         */
        @ApiModelProperty("订单号码")
        private String orderNumber;
        /**
         * 租户隔离
         */
        @ApiModelProperty("租户id")
        private String tenantId;

        /**
         * 应用隔离
         */
        @ApiModelProperty("应用id")
        private Long appId;
    }
}
