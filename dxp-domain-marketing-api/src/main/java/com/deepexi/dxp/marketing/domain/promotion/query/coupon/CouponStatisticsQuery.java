package com.deepexi.dxp.marketing.domain.promotion.query.coupon;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户优惠券统计查询
 *
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/12/20 10:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class CouponStatisticsQuery extends AbstractObject {

    @ApiModelProperty("优惠券 id")
    private Long couponId;

    @ApiModelProperty("使用状态 0 未使用 1已使用 2 已失效")
    private Integer status;
}
