package com.deepexi.dxp.marketing.domain.promotion.dto.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 活动页
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ActivityPageCouponResponseDTO extends AbstractObject implements Serializable {

    @ApiModelProperty("活动页主键")
    private Long id;

    @ApiModelProperty("活动图标")
    private String activityIconUrl;


    @ApiModelProperty("活动图")
    private String activityDiagram;

    @ApiModelProperty("活动规则图")
    private String activityRulesUrl;

    @ApiModelProperty("底部按钮 1、项目详情, 2、联系销售,3、联系电话,4、分享,5、生成海报")
    private String bottomBtnType;

    @ApiModelProperty("联系电话")
    private String phone;
}
