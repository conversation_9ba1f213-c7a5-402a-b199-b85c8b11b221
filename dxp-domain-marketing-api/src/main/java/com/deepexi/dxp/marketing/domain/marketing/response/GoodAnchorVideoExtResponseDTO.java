package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: HT
 * @CreateTime: 2022/5/12
 */
@Data
@ApiModel(value = "好主播个人及作品信息返回对象", description = "好主播个人及作品信息返回对象")
@Builder
public class GoodAnchorVideoExtResponseDTO implements Serializable {

    private static final long serialVersionUID = -5696571249162467372L;

    @ApiModelProperty(value = "个人信息")
    private GoodAnchorInfoVO goodAnchorInfoVO;

    @ApiModelProperty(value = "作品集合")
    private List<VideoResponseDTO> videoResponseDTOList;

    @ApiModelProperty(value = "活动信息")
    private PromotionActivityDetailDTO actDTO;

}
