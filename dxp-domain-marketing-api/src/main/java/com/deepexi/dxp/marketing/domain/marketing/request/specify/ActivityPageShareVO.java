package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Map;

/**
 * 分享页入参对象
 * <AUTHOR>
 */
@ApiModel(value = "分享页入参对象", description = "分享页入参对象")
@Data
public class ActivityPageShareVO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "分享标题",required = true)
    @NotBlank(message = "分享标题不能为空")
    @Length(max = 20,message = "分享标题不能超过20个字符")
    private String shareTitle;

    @ApiModelProperty(value = "分享内容")
    //@NotBlank(message = "分享内容不能为空")
    @Length(max = 50,message = "分享内容不能超过50个字符")
    private String shareContent;

    @ApiModelProperty(value = "分享图标",required = true)
    @NotBlank(message = "分享图标不能为空")
    private String shareIconUrl;

    @ApiModelProperty(value = "海报标题",required = true)
    @NotBlank(message = "海报标题不能为空")
    @Length(max = 20,message = "海报标题不能超过20个字符")
    private String posterTitle;

    @ApiModelProperty(value = "分享海报",required = true)
    @NotBlank(message = "分享海报不能为空")
    private String sharePosterUrl;

    private Map<String, Object> ext;

    /**
     * 基础配置通用ID
     */
    @ApiModelProperty(value = "基础配置通用ID")
    private Long activityId;

    /**
     * 0、活动专题，1、活动
     */
    @ApiModelProperty(value = "0、活动专题，1、活动")
    private Integer type;
}
