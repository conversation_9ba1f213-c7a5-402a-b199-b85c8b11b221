package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import lombok.Data;

/**
 * 微信小程序订阅消息发送
 * <AUTHOR>
 */
@Data
public class SendTemplateNewsRequestDTO {
    /**
     * 用户openid
     */
    private String userOpenId;
    /**
     * 	模板id
     */
    private String templateId;
    /**
     * 模板参数，JSON格式
     */
    private String templateParam;
    /**
     * 跳转页面路径
     */
    private String jumpUrl;
    /**
     * 小程序渠道
     */
    private String mpFrom;
    /**
     * 创建人
     */
    private String createId;


    //优生活
    public static final String MP_FROM_USH = "USH";

    //置业通
    public static final String MP_FROM_ZYT = "ZYT";
}
