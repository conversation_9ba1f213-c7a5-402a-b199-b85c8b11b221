package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHisDetailResponseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 优惠券活动详情信息返回
 */
@Data
@ApiModel(value = "优惠券活动详情信息返回对象", description = "优惠券活动信息返回对象")
public class CouponActivityResponseDTO extends SuperVO {
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    @ApiModelProperty(value = "活动类型")
    private Integer paTemplateId;

    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private Integer status;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value="活动开始时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value="活动结束时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "活动扩展信息")
    private ActivityExtVO ext;

    @ApiModelProperty(value = "适用项目")
    private List<String> projectList;

    /**
     * 优惠券配置项目(前端列表展示)
     */
    @ApiModelProperty(value = "适用项目列表")
    private List<ActivityParticipationVO> projectInfoList;

    /**
     * 规则配置
     */
    @ApiModelProperty(value = "规则配置")
    private RuleConfigVO ruleConfigVO;


    //优惠券配置信息
    private List<ResourceHisDetailResponseDTO> prizeList;


    /**
     * 界面配置
     */
    @ApiModelProperty(value = "活动页配置")
    private ActivityPageVO activityPageVO;


    @ApiModelProperty(value = "分享页配置")
    private ActivityPageShareVO activityPageShareVO;

    @ApiModelProperty(value = "活动目标名称")
    private String activityTargetName;

    @ApiModelProperty(value = "指标路径名称")
    private String marketingKpiRouteMapName;
}
