package com.deepexi.dxp.marketing.domain.promotion.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 资源信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ResourceHisDetailRequestDTO extends AbstractObject implements Serializable {
    @ApiModelProperty(value="资源id")
    private Long resourceId;

    @ApiModelProperty("资源发放数量")
    private Integer resourceIssuedNumber;

    @ApiModelProperty("每人限制领取/购买次数")
    private Integer limitTimes;

    @ApiModelProperty("领取方式:0-免费领取,1-付费领取")
    private Integer receiveMode;

    @ApiModelProperty("购买价格")
    private BigDecimal purchasePrice;
}
