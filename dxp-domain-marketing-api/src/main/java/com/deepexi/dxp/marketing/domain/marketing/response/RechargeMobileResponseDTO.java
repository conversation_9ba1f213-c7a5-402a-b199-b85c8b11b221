package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value = "话费充值返回参数", description = "话费充值返回参数")
@Data
public class RechargeMobileResponseDTO {

    @ApiModelProperty(value = "200代表成功，其余失败。如果失败，请在msg里面填入具体内容")
    private String code;

    @ApiModelProperty(value = "msg")
    private String msg;

    @ApiModelProperty(value = "业务信息字段，json格式")
    private RechargeMobileResponseResult data;

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("话费充值返回数据")
    public static class RechargeMobileResponseResult extends AbstractObject implements Serializable {

        @ApiModelProperty(value = "手机号码")
       private String mobile;

       @ApiModelProperty(value = "充值金额")
       private String amount;

       @ApiModelProperty(value = "充值面额")
       private String cardNum;

       @ApiModelProperty(value = "套餐名称")
       private String cardName;

       @ApiModelProperty(value = "充值状态 : 0充值中 1成功 9撤销/失败")
       private String result;

       @ApiModelProperty(value = "活动id")
       private String activityId;

       @ApiModelProperty(value = "openId")
       private String openId;

       @ApiModelProperty(value = "unionId")
       private String unionId;

       @ApiModelProperty(value = "订单号")
       private String orderNo;
    }
}
