package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/12/6 11:01
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel
public class ReturnSalesCommodityDTO extends AbstractObject implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("上架id")
    private Long upId;

    @ApiModelProperty("商品类目id")
    private String categoryId;

    @ApiModelProperty("品牌id")
    private String brandId;

    @ApiModelProperty("该sku的数量")
    private Integer number;
}
