package com.deepexi.dxp.marketing.domain.promotion.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class ActivityPageShareCreateRequestDTO  extends AbstractObject implements Serializable {

    @ApiModelProperty("分享标题")
    private String shareTitle;

    @ApiModelProperty("分享内容")
    private String shareContent;

    @ApiModelProperty("分享图标")
    private String shareIconUrl;

    @ApiModelProperty("海报标题")
    private String posterTitle;

    @ApiModelProperty("分享海报")
    private String sharePosterUrl;

    @ApiModelProperty("类型：0、活动专题，1、活动")
    private Integer type;

}
