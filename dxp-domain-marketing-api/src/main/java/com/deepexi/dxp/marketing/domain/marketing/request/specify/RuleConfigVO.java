package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 规则配置入参对象
 * <AUTHOR>
 */
@ApiModel(value = "规则配置入参对象", description = "规则配置入参对象")
@Data
public class RuleConfigVO {
    /**
     * 参与类型
     */
    @ApiModelProperty(value = "参与类型（0按天计算，1按人计算）")
   // @NotNull(message = "参与类型不能为空")
    @Max(value = 1,message = "参与类型输入有误")
    @Min(value = 0)
    private Integer joinType;

    /**
     * 每日/每人抽奖次数
     */
    @ApiModelProperty(value = "每日/每人可抽奖次数")
    @Max(value = 10,message = "限制输入1-10")
    @Min(value = 1)
    private Integer dailyDrawNumber;

    /**
     * 分享可额外抽奖次数
     */
    @ApiModelProperty(value = "分享可额外抽奖次数")
    @Max(value = 10,message = "限制输入1-10")
    @Min(value = 1)
    private Integer shareDrawNumber;

    /**
     * 每天/每人可分享次数
     */
    @ApiModelProperty(value = "每天/每人可分享次数")
    @Max(value = 10,message = "限制输入1-10")
    @Min(value = 1)
    private Integer dailyShareNumber;

    @ApiModelProperty(value = "信息登记")
    @NotNull(message = "信息登记不能为空")
    private List<EnrollmentInfoVO> feedbackInfo;


    /* ******************* 砍价活动相关属性 start ******************* */
    @ApiModelProperty("砍价最低价")
    private BigDecimal lowestPrice;

    @ApiModelProperty("砍价有效期")
    private Integer validPeriod;

    @ApiModelProperty("帮砍人数")
    private Integer helperNumber;

    @ApiModelProperty("发起砍价次数")
    private Integer launchTimes;

    @ApiModelProperty("用户帮砍次数")
    private Integer helpTimes;
    /* ******************* 砍价活动相关属性 end ******************* */
}
