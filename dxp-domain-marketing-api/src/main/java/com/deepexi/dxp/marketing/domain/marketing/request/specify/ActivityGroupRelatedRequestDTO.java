package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 活动分组中间表添加请求
 * <AUTHOR>
 */
@ApiModel(value = "活动分组中间表添加入参", description = "活动分组中间表添加入参")
@Data
public class ActivityGroupRelatedRequestDTO {
    /**
     * 活动专题名称
     */
    @ApiModelProperty(value = "活动/活动组id",required = true)
    @NotNull(message = "活动/活动组id不能为空")
    private Long activityId;

    @ApiModelProperty("展示图片")
    private String imageUrl;

    @ApiModelProperty(value = "活动排序",required = true)
    @NotNull(message = "活动排序不能为空")
    private Integer sort;

    @ApiModelProperty(value = "类型（0活动组，1活动）",required = true)
    @NotNull(message = "活动类型不能为空")
    private Integer type;
}
