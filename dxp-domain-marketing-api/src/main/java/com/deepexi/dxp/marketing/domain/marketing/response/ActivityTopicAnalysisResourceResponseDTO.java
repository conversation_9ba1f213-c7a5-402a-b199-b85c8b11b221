package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 活动专题分析资源数据dto
 * <AUTHOR>
 */
@ApiModel(value = "活动专题分析资源数据返回参数", description = "活动专题分析资源数据返回参数")
@Data
public class ActivityTopicAnalysisResourceResponseDTO extends SuperVO {

    /**
     * 资源类型 0、卡劵 1、第三方接入
     */
    @ApiModelProperty(value = "资源类型 0、卡劵 1、第三方接入")
    private Integer type;

    @ApiModelProperty("资源名称")
    private String name;

    @ApiModelProperty("资源图片")
    private String url;

    @ApiModelProperty("资源明细")
    private Integer resourceInfo;

    @ApiModelProperty("领取人数")
    private Integer gotCount;

    @ApiModelProperty("核销人数")
    private Integer writeOffPeopleNumber;

    @ApiModelProperty("细分类别 0、普通劵 1、房源劵 2、商品劵")
    private Integer couponCategory;

    @ApiModelProperty("细分类别 0、手机话费 1、现金红包")
    private Integer thirdCategory;

    @ApiModelProperty("卡劵类型 0、代金劵 1、折扣劵")
    private Integer couponType;

    @ApiModelProperty("卡劵面值")
    private BigDecimal couponValue;

    @ApiModelProperty("折扣价")
    private String discountPrice;

    @ApiModelProperty("原价")
    private String costPrice;

    @ApiModelProperty("房源名称")
    private String houseName;
}
