package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 历史资源信息dto
 * <AUTHOR>
 */
@Data
@ApiModel
public class PromotionHisResourceDTO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 原资源id
     */
    @ApiModelProperty(value = "原资源id")
    private Long resourceId;
    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;
    /**
     * 资源类型 0、卡劵 1、第三方接入
     */
    @ApiModelProperty(value = "资源类型 0、卡劵 1、第三方接入")
    private Integer type;
    /**
     * 细分类别 0、普通劵 1、房源劵 2、商品劵
     */
    @ApiModelProperty(value = "细分类别 0、普通劵 1、房源劵 2、商品劵")
    private Integer couponCategory;
    /**
     * 细分类别 0、手机话费 1、现金红包
     */
    @ApiModelProperty(value = "细分类别 0、手机话费 1、现金红包")
    private Integer thirdCategory;
    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String name;
    /**
     * 卡劵类型 0、代金劵 1、折扣劵
     */
    @ApiModelProperty(value = "卡劵类型 0、代金劵 1、折扣劵")
    private Integer couponType;
    /**
     * 卡劵面值
     */
    @ApiModelProperty(value = "卡劵面值")
    private BigDecimal couponValue;
    /**
     * 资源图片
     */
    @ApiModelProperty(value = "资源图片")
    private String url;
    /**
     * 使用时间类型 0 、不限制 1、指定时间 2、有效天数
     */
    @ApiModelProperty(value = "使用时间类型 0 、不限制 1、指定时间 2、有效天数")
    private Integer validTimeType;

    @ApiModelProperty(value = "发放方式")
    private Integer grantWay;
    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间")
    private Date validStartTime;
    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间")
    private Date validEndTime;
    /**
     * 有效天数
     */
    @ApiModelProperty(value = "有效天数")
    private Integer validDay;
    /**
     * 使用规则
     */
    @ApiModelProperty(value = "使用规则")
    private String useRule;
    /**
     * 房源名称
     */
    @ApiModelProperty(value = "房源名称")
    private String houseName;
    /**
     * 房源面积
     */
    @ApiModelProperty(value = "房源面积")
    private String houseVolume;
    /**
     * 房源信息
     */
    @ApiModelProperty(value = "房源信息")
    private String houseMessage;
    /**
     * 原价
     */
    @ApiModelProperty(value = "原价")
    private String costPrice;
    /**
     * 折扣价
     */
    @ApiModelProperty(value = "折扣价")
    private String discountPrice;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;
    /**
     * 优惠卷code
     */
    @ApiModelProperty(value = "优惠卷code")
    private String code;
    /**
     * 拓展字段
     */
    @ApiModelProperty(value = "ext")
    private String ext;


    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Long version;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Integer deleted = 0;

    /**
     * 资源发放数量
     */
    @ApiModelProperty(value = "资源发放数量")
    private Integer issuedQuantity;

    /**
     * 每日/每人发放上限
     */
    @ApiModelProperty(value = "每日/每人发放上限")
    private Integer issuanceCap;

    /**
     * 中奖率
     */
    @ApiModelProperty(value = "中奖率")
    private Double oddsOfWinning;

    /**
     * 限制（1限制用户中奖1次，0不限中奖次数）
     */
    @ApiModelProperty(value = "限制（1限制用户中奖1次，0不限中奖次数）")
    private Integer limitType;

    @ApiModelProperty(value = "购买金额")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "领取方式:0-免费领取,1-付费领取")
    private Integer receiveMode;

    /**
     * 资源剩余数量
     */
    @ApiModelProperty(value = "remaining_quantity")
    private Integer remainingQuantity;

    /**
     * 每人限制领取/购买次数
     */
    @ApiModelProperty(value = "limit_times")
    private Integer limitTimes;


    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;


    @ApiModelProperty(value = "助力资源类型")
    private Integer fissonResourceType;

    @ApiModelProperty(value = "项目类型:0-活动项目,1-系统项目")
    private Integer sysType;

    @ApiModelProperty(value = "助力人数")
    private Integer fissonCount;

    /**
     * 阶梯排序号
     */
    @ApiModelProperty(value = "阶梯排序号")
    private Integer ladderSort;
}
