package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Class: ProcessDeleteDTO
 * @Description: 删除流程定义DTO
 * @Author: lizhongbao
 * @Date: 2020/3/31
 **/
@Data
@ApiModel
public class ProcessDeleteRequest extends AbstractObject {

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true)
    @NotNull(message = "租户id不能为空")
    private String tenantId;

    @ApiModelProperty(value = "应用id", required = true)
    private String appId;

    /**
     * 关联项ID
     */
    @ApiModelProperty(value = "自动营销ID/模型ID", required = true)
    @NotNull(message = "itemId不能为空")
    private String itemId;

    @ApiModelProperty(value = "1 流程画布", required = true)
    @NotNull(message = "itemType不能为空")
    private String itemType;
}
