package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 分享请求dto
 * <AUTHOR>
 */
@Data
@ApiModel
public class ShareLuckyDrawRequestDTO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     *用户ID
     **/
    @ApiModelProperty(value = "用户Id")
    //@NotBlank(message = "用户Id不能为空")
    private String userId;


    /**
     * 用户昵称
     */
    @ApiModelProperty("用户昵称")
    private String nickName;

    /**
     * 用户昵称
     */
    @ApiModelProperty("用户名")
    private String userName;

    /**
     * 用户昵称
     */
    @ApiModelProperty("手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty("0、微信小程序  1、H5  2、抖音")
    @NotNull(message = "渠道类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "unionId")
    private String unionId;

    @ApiModelProperty(value = "助力活动ID")
    private Long partakeLogId;
}
