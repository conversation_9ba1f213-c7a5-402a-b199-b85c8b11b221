package com.deepexi.dxp.marketing.domain.promotion.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 前端创建场景码
 */
@Data
@ApiModel
public class OneCodeSceneCreateRequestDTO extends AbstractObject {
    @ApiModelProperty(value = "推广类型:0、活动推广,1、活动专题推广",required = true)
    @NotNull(message = "推广类型不能为空")
    private Integer promotionType;

    @ApiModelProperty(value = "活动id",required = true)
    @NotNull(message = "活动id不能为空")
    private Long activityId;
}
