package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车返回list
 *
 * <AUTHOR> xinjian.yao
 * @date 2020/3/11 16:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class ShoppingCartShopAspectVO extends AbstractObject {


    @ApiModelProperty("店铺的活动")
    private List<ShopAvtivity> shopAvtivityList;


    @ApiModelProperty("订单原始金额")
    private BigDecimal orderDetailPrice;

    @ApiModelProperty("订单优惠后金额")
    private BigDecimal orderDiscountsPrice;

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ApiModel
    public static class ShopAvtivity extends AbstractObject {
        @ApiModelProperty("店铺id")
        private Long shopId;

        @ApiModelProperty("有活动的商品")
        private List<ActivityResponseParamsDTO> activityList;

        @ApiModelProperty("没有活动的商品")
        private List<ActivityCommodityDTO> notActivityCommodityDTOList;
    }


}
