package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class WhiteListRequestDto {
    @NotNull
    @ApiModelProperty("活动id")
    private Long activityId;
    @NotBlank
    @ApiModelProperty("用户unionId")
    private String unionId;
    @ApiModelProperty("有效期，单位秒，不传则默认为活动期间")
    private Integer ttl;
    @ApiModelProperty("操作类型：1-添加（默认），2-删除")
    private Integer optionType;
}