package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.common.base.dto.SuperExtDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 抽奖、秒杀、优惠券的用户参与记录DTO
 * <AUTHOR>
 */
@ApiModel(value = "ActivityPartakeLogDTO入参对象", description = "ActivityPartakeLogDTO返回对象")
@Data
public class ActivityPartakeLogDTO extends SuperExtDTO {
    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    private String idCard;

    /**
     * 所在地区
     */
    @ApiModelProperty(value = "所在地区")
    private String area;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String detaAddress;

    /**
     * 中奖结果
     */
    @ApiModelProperty(value = "中奖结果")
    private String prizeResult;

    /**
     * 兑奖码
     */
    @ApiModelProperty(value = "兑奖码")
    private String code;

    /**
     * 领奖时间
     */
    @ApiModelProperty(value = "领奖时间")
    private String getTime;

    /**
     * 核销时间
     */
    @ApiModelProperty(value = "核销时间")
    private String verifyTime;
    /**
     * 核销人
     */
    @ApiModelProperty(value = "核销人")
    private String verifyBy;

    /**
     * 活动订单id
     */
    @ApiModelProperty(value = "活动订单id")
    private Long orderId;

    /**
     * 平台业务订单号
     */
    @ApiModelProperty(value = "平台业务订单号")
    private String orderNo;

    /**
     * 奖品ID
     */
    @ApiModelProperty(value = "奖品ID")
    private Long resourceId;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    /**
     *  支付金額
     */
    @ApiModelProperty(value = "支付金額")
    private BigDecimal payMoney;

    /**
     * unionId
     */
    @ApiModelProperty(value = "union_id")
    private String unionId;

    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty(value = "type")
    private Integer type;

    /**
     * 微信头像URL
     */
    @ApiModelProperty(value = "微信头像URL")
    private String avatar;

    /**
     * 所需助力人数
     */
    @ApiModelProperty(value = "所需助力人数/最大签到次数/报名人数")
    private Integer needFissonCount;

    /**
     * 当前已助力人数
     */
    @ApiModelProperty(value = "当前已助力人数/当前已签到次数")
    private Integer currentFissonCount;

    /**
     * 当前已砍价价格
     */
    @ApiModelProperty(value = "当前已砍价价格")
    private BigDecimal currentFissonPrice;


    /**
     * 剩余助力时长（剩余砍价时长）
     */
    @ApiModelProperty(value = " 剩余助力时长（剩余砍价时长）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date fissonEndTime;

    /**
     * 助力砍价状态 0、助力中（砍价中），1、助力成功（砍价成功）,2、助力失败（砍价失败）
     */
    @ApiModelProperty(value = "助力砍价状态 0、助力中（砍价中），1、助力成功（砍价成功）,2、助力失败（砍价失败）")
    private Integer fissonStatus;

    /**
     * 裂变活动类型：0、点击类助力活动；1、砍价活动
     */
    @ApiModelProperty(value = "裂变活动类型：0、点击类助力活动；1、砍价活动")
    private Integer fissonType;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     *项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     *视频Id
     */
    @ApiModelProperty(value = "视频Id")
    private Long videoId;
    /**
     *视频封面路径
     */
    @ApiModelProperty(value = "视频封面路径")
    private String coverUrl;
    /**
     *视频标题
     */
    @ApiModelProperty(value = "视频标题")
    private String videoTitle;
    /**
     *视频路径
     */
    @ApiModelProperty(value = "视频路径")
    private String videoUrl;

    @ApiModelProperty(value = "当天参与次数")
    private Integer todayActCnt = 0;

}
