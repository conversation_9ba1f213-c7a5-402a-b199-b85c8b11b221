package com.deepexi.dxp.marketing.domain.promotion.request.specify;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityExtVO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 活动新增
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
@ToString
public class PromotionCouponActivityUpdateRequestDTO extends AbstractObject implements Serializable{
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;
    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId;

    @ApiModelProperty("活动名称")
    private String name;

    @ApiModelProperty(value="活动开始时间")
    private Date startTime;

    @ApiModelProperty(value="活动结束时间")
    private Date endTime;


    @ApiModelProperty(value="扩展字段")
    private ExtendFieldDTO ext;

    @ApiModelProperty(value="活动页面")
    private ActivityPageUpdateRequestDTO activityPageRequestDTO;

    @ApiModelProperty(value="分享页")
    private ActivityPageShareUpdateRequestDTO activityPageShareRequestDTO;



    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class ExtendFieldDTO extends ActivityExtVO implements Serializable {

        @ApiModelProperty(value="参与对象:1-全部用户")
        private Integer participants;

        @ApiModelProperty(value="资源属性:0-不限项目,1-指定项目")
        private Integer resourcesAttribute;

        @ApiModelProperty(value="投放渠道:0-不在置业通小程序展示,1-在置业通小程序-活动列表中展示")
        private Integer deliveryChannel;

        @ApiModelProperty(value="适用项目")
        private List<String> projectIdList;

        @ApiModelProperty(value="资源列表")
        private List<ResourceHisDetailRequestDTO> resourceDetailDTOList;

        @ApiModelProperty(value="登记信息")
        private List<RegistInfoDTO> registInfoDTOList;
    }
    /**
     * 登记信息
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class RegistInfoDTO extends AbstractObject implements Serializable {

        @ApiModelProperty("输入类型（0普通文本框，1手机号码，2所在地区，3身份证,4姓名）")
        @NotNull(message = "输入类型不能为空")
        private Integer type;

        @ApiModelProperty("输入标题")
        @Length(max = 5,message = "限制最多5个字符")
        private String value;
    }
}
