package com.deepexi.dxp.marketing.domain.promotion.dto.usercredit;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 充值赠送活动下单检查校验结果.
 *
 * <AUTHOR> feng<PERSON>
 * @date 2020/03/12 17:08
 */
@Data
@ApiModel
public class UserCreditCheckDTO {

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long avtivityId;

    /**
     * 是否通过校验
     */
    @ApiModelProperty("是否通过校验")
    private Boolean isAvailable;
}
