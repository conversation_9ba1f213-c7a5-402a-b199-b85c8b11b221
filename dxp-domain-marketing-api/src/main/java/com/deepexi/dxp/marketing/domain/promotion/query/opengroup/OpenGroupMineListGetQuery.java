package com.deepexi.dxp.marketing.domain.promotion.query.opengroup;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 当前可参与的可拼团的商品列表 条件参数实体
 * 即：我的拼团列表入参
 *
 * <AUTHOR> xianfeng.cai
 * @date 2019/11/29 14:08
 */
@Data
@ApiModel
public class OpenGroupMineListGetQuery extends AbstractObject {

    @ApiModelProperty("渠道id")
    private String tenantId;
    @ApiModelProperty("appId")
    private Long appId;

    @ApiModelProperty("参团用户Id")
    private Long userId;
    @ApiModelProperty(value = "拼团状态;0-待成团，1-已成团，2-超时取消，3-订单取消关闭")
    private Integer status;

}