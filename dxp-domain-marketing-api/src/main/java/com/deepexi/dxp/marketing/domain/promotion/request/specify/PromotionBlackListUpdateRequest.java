package com.deepexi.dxp.marketing.domain.promotion.request.specify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
public class PromotionBlackListUpdateRequest {

    @NotNull
    @ApiModelProperty("主键ID")
    private Integer id;

    @NotNull(message = "禁止报名期限不能为空")
    @ApiModelProperty(value = "禁止报名期限（天）", required = true)
    @Min(1)
    private Integer restrictionDays;

    @NotNull(message = "禁止报名开始日期不能为空")
    @ApiModelProperty(value = "禁止报名开始日期", required = true)
    private LocalDate startDate;

    @ApiModelProperty(value = "限制原因说明")
    private String restrictionReason;
}
