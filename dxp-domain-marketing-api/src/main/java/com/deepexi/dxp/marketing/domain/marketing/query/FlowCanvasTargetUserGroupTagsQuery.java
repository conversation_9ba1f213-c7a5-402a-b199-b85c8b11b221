package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/11
 */
@Data
public class FlowCanvasTargetUserGroupTagsQuery extends SuperQuery {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 主键id列表
     */
    private List<Long> ids;

    /**
     * 圈人目标id
     */
    private Long targetUserId;

    /**
     * 圈人目标Id列表
     */
    private List<Long> targetUserIds;

    /**
     * 关联id
     */
    private Long refId;
}
