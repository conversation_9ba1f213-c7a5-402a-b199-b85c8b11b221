package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "支付下单请求对象", description = "支付下单请求对象")
@Data
public class OrderPayRequestDTO {

    @ApiModelProperty(value = "公众号或小程序id")
    private String appId;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "订单描述")
    private String description;

    @ApiModelProperty(value = "用户在直连商户appid下的唯一标识")
    private String openid;

    @ApiModelProperty(value = "业务系统内部订单号")
    private String bizOrderNo;

    @ApiModelProperty(value = "业务系统回调支付通知地址")
    private String bizNotifyUrl;


    @ApiModelProperty(value = "订单金额，单位分")
    private Integer amount;

    @ApiModelProperty(value = "订单有效的分钟数")
    private Integer expireMinutes;


    @ApiModelProperty(value = "微信支付交易类型,当需要提供微信支付时，必填。可固定传 MWEB")
    private String tradeType;

    @ApiModelProperty(value = "用户的客户端IP，支持IPv4和IPv6")
    private String clientIp;

}
