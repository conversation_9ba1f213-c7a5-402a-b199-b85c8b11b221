package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 优惠券历史资源信息json字段公用类
 * <AUTHOR>
 */
@Data
public class CouponHisResourceJsonVO {

    @ApiModelProperty(value="资源id")
    private Long resourceId;

    @ApiModelProperty("资源发放数量")
    private Integer resourceIssuedNumber;

    @ApiModelProperty("每人限制领取/购买次数")
    private Integer limitTimes;

    @ApiModelProperty("领取方式:0-免费领取,1-付费领取")
    private Integer receiveMode;

    @ApiModelProperty("购买价格")
    private BigDecimal purchasePrice;
}
