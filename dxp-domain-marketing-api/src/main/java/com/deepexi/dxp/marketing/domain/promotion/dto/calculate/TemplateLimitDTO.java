package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/11/22 16:19
 */
@Data
@ToString
@ApiModel
public class TemplateLimitDTO {

    private List<BaseTemplateLimitDTO> commodityLimit;
    private List<BaseTemplateLimitDTO> userLimit;
    private List<BaseTemplateLimitDTO> numberLimit;
    private List<BaseTemplateLimitDTO> couponLimit;
    private List<BaseTemplateLimitDTO> tenantLimit;


}
