package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 砍价活动发起者表
 * <AUTHOR>
 */
@ApiModel(value = "砍价活动发起者DTO", description = "砍价活动发起者DTO")
@Data
public class ActivityFissionReducePriceDTO extends SuperDTO {
    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 用户电话
     */
    @ApiModelProperty(value = "用户电话")
    private String phone;

    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源id")
    private Long resourceId;

    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String resourceName;

    /**
     * 商品路径
     */
    @ApiModelProperty(value = "商品路径")
    private String resourceUrl;

    /**
     * 商品售价
     */
    @ApiModelProperty(value = "商品售价")
    private BigDecimal resourcePrice;

    /**
     * 砍价状态  0、砍价中，1、砍价成功,2、砍价失败
     */
    @ApiModelProperty(value = "砍价状态  0、砍价中，1、砍价成功,2、砍价失败")
    private Integer status;

    /**
     * 最低价
     */
    @ApiModelProperty(value = "最低价")
    private BigDecimal minimumPrice;

    /**
     * 当前价
     */
    @ApiModelProperty(value = "当前价")
    private BigDecimal currentPrice;
    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    private String activityStartTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String activityEndTime;

    /**
     * unionId
     */
    @ApiModelProperty(value = "unionId")
    private String unionId;

    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty(value = "0、微信小程序  1、H5  2、抖音")
    private Integer type;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
}
