package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageShareVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeLogDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.LocationSignTypeDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHisDetailResponseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;
import java.util.List;

/**
 * 参与活动详情返回对象
 */
@Data
public class ActivityHomeInfoResponseDTO {

    @ApiModelProperty(value = "活动详情")
    private PromotionActivityResponseDTO promotionActivity;

    @ApiModelProperty(value = "奖励配置列表")
    private List<ResourceHisDetailResponseDTO> prizeList;

    @ApiModelProperty(value = "活动页配置")
    private ActivityPageVO activityPageVO;

    @ApiModelProperty(value = "分享页配置")
    private ActivityPageShareVO activityPageShareVO;

    @ApiModelProperty(value = "用户参与情况")
    private ActivityPartakeLogDTO activityPartakeLogDTO;

    @ApiModelProperty(value = "专门给移动端展示的项目列表，以地理城市分组")
    private List<ActivityParticipationGroupResponseDTO> frontProjectList;

    @ApiModelProperty(value = "总用户参与数量")
    private Integer totalCnt = 0;
    @ApiModelProperty(value = "用户扫码数量")
    private long totalScanCnt = 0;
    @ApiModelProperty(value = "当前用户扫码签到次数")
    private Integer userScanCnt = 0;

    @ApiModelProperty(value = "助力列表/已签到明细")
    private List<ActivityFissionLogResponseDTO> fissionLogs;
    @ApiModelProperty(value = "签到地点列表")
    private List<LocationSignTypeDTO> locations;
    @ApiModelProperty(value = "签到地点城市列表")
    private Collection<CityRankDTO> cities;
    @ApiModelProperty(value = "单次打卡获奖规则")
    private List<LocationSignRuleVO> singleRules;
    @ApiModelProperty(value = "多次打卡获奖规则")
    private List<LocationSignRuleVO> multiRules;
}
