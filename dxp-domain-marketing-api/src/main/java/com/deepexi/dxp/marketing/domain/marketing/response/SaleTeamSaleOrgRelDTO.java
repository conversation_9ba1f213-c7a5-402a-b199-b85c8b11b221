package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SaleTeamSaleOrgRelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "案场id")
    private String saleTeamId;

    @ApiModelProperty(value = "案场名称")
    private String saleTeamName;

    @ApiModelProperty(value = "项目编码")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "城市id(行政)")
    private String realCityId;

    @ApiModelProperty(value = "城市名称(行政)")
    private String realCityName;

    @ApiModelProperty(value = "区域id（行政）")
    private String realAreaId;

    @ApiModelProperty(value = "区域名称（行政）")
    private String realAreaName;

}
