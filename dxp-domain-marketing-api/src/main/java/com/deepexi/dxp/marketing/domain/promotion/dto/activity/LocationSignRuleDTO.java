package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * 位置签到打卡规则类
 */
@Data
@NoArgsConstructor
public class LocationSignRuleDTO extends AbstractObject {
    @ApiModelProperty(value = "打卡总次数")
    private int totalSignNumber;

    @ApiModelProperty(value = "分类地点要求打卡次数，数组中的每个元素对应一个分类，0-不限，n-具体限制的次数")
    private int[] subSignNumber;

    @ApiModelProperty(value = "可获得抽奖次数")
    private int perGetDrawNumber;

    @ApiModelProperty(value = "对应抽奖的活动ID")
    private Long luckDrawId;
    @ApiModelProperty(value = "对应抽奖的活动名称")
    private String luckDrawName;

    @ApiModelProperty(value = "可获得次数，0-不限，n-具体限制的次数")
    private int totalGetNumber;

    /**
     * 校验规则
     * @param subTotalSignNumber 每个区域的打卡次数统计
     * @param totalGetNumber 最多可获得次数
     * @return
     */
    public boolean checkRule(int[] subTotalSignNumber, int totalGetNumber) {
        //计算打卡总次数，如果小于总次数，则不满足规则
        int userTotalSignNumber = Arrays.stream(subTotalSignNumber).reduce(0, Integer::sum);
        if (userTotalSignNumber < totalSignNumber) {
            return false;
        }
        //需要考虑用户已经抽过奖的情况，先看总数是否满足（应抽奖次数要大于已抽奖次数）
        int shouldGetNumber = userTotalSignNumber/totalSignNumber;
        if (shouldGetNumber <= totalGetNumber) {
            return false;
        }

        for (int i = 0; i < subSignNumber.length; i++) {
            if (subSignNumber[i] == 0) {
                continue;
            }
            //再看每个分类地点的次数是否满足
            int shouldGetSubNumber = subTotalSignNumber[i]/subSignNumber[i];
            //应抽奖次数要大于已抽奖次数
            if (shouldGetSubNumber <= totalGetNumber) {
                return false;
            }
        }
        return (this.totalGetNumber == 0 || totalGetNumber < this.totalGetNumber);
    }
}
