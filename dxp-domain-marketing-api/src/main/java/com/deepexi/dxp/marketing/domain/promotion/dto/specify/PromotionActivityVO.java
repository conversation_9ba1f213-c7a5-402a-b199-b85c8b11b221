package com.deepexi.dxp.marketing.domain.promotion.dto.specify;

import com.deepexi.dxp.marketing.common.base.vo.SuperExtVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class PromotionActivityVO extends SuperExtVO implements Serializable {

    @ApiModelProperty("0 全款可退 1 定金可退 2 不可退 （默认是0）")
    private Integer returnMoneyType;



    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value="模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId;

    /**
     * 名称
     */
    @ApiModelProperty(value="名称")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value="编码")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty(value="描述")
    private String description;

    /**
     * 活动状态
     */
    @ApiModelProperty(value="活动状态 0 进行中")
    private Integer status;

    @ApiModelProperty(value="上架状态 0-下架1-上架")
    private Integer upperStatus;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value="活动开始时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value="活动结束时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endTime;

    /**
     * 应用隔离
     */
    @ApiModelProperty(value="应用隔离")
    private Long appId;


    /**
     * 0 有用优惠券 1 不能用优惠券
     */
    @ApiModelProperty(value="0 有用优惠券 1 不能用优惠券")
    private Boolean couponsFlag;

    /**
     * 创建人：限制50个字符
     */
    @ApiModelProperty(value="创建人")
    private String createdPerson;

    /**
     * 适用项目
     */
    @ApiModelProperty(value="适用项目")
    private String projects;


    @ApiModelProperty(value="领取方式:0-免费领取,1-付费领取")
    private List<Integer> receiveModeList;

    @ApiModelProperty(value="活动图标")
    private String activityIconUrl;

    @ApiModelProperty("活动页面配置")
    private ActivityPageDTO activityPageDTO;

    @ApiModelProperty("奖品列表")
    private List<ResourceHisDetailResponseDTO> prizeList;

    //<-------------------裂变类活动所需值字段 start-------------------------->
    @ApiModelProperty(value = "砍价资源图片")
    private String url;

    @ApiModelProperty(value = "销售价")
    private String sellingPrice;

    @ApiModelProperty(value = "砍价有效期")
    private Integer validPeriod;

    @ApiModelProperty(value = "砍价最低价")
    private BigDecimal lowestPrice;

    @ApiModelProperty("所需帮砍人数")
    private Integer helperNumber;

    @ApiModelProperty("已发起人数")
    private Integer initiatorsNumber;

    @ApiModelProperty("已成功人数")
    private Integer successNumber;
    //<-------------------裂变类所需值字段 end-------------------------->

    @ApiModelProperty("反馈数")
    private Integer feedbackNum;

    @ApiModelProperty(value="最新反馈时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date newFeedBackTime;

    @ApiModelProperty("场景码")
    private String sceneCode;


    @ApiModelProperty("组织机构类型")
    private Integer orgType;
    @ApiModelProperty("标签列表")
    private List<TagItemDTO> tags;
    @ApiModelProperty(value = "总用户参与数量")
    private Long totalCnt;
    @ApiModelProperty(value="置顶时间，越近越靠前")
    private Date topTime;
}
