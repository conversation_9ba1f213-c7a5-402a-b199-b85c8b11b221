package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 位置签到打卡地点类
 */

@Data
public class LocationSignTypeDTO {
    @ApiModelProperty(value = "名称")
    private String name;
    private List<LocationSignItemDTO> items;

    @Data
    public static class LocationSignItemDTO {
        @ApiModelProperty(value = "城市id")
        private String cityId;
        @ApiModelProperty(value = "城市名称")
        private String cityName;
        @ApiModelProperty(value = "名称")
        private String name;
        @ApiModelProperty(value = "经度")
        private Double longitude;
        @ApiModelProperty(value = "纬度")
        private Double latitude;
        @ApiModelProperty(value = "项目id")
        private String projectId;
        @ApiModelProperty(value = "项目名称")
        private String projectName;
        @ApiModelProperty(value = "是否已打卡")
        private boolean checked;

        /**
         * 获取经纬度
         */
        public String getLocation() {
            return longitude + "_" + latitude;
        }
    }
}

