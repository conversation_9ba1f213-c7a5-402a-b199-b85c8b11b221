package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "模板短信发送接口请求对象", description = "模板短信发送接口请求对象")
@Data
public class TemplateSendRequestDTO {
    /**
     * 电话号码
     */
    private String mobile;
    /**
     * 模板Id
     */
    private String templateCode;
    /**
     * 模板参数
     */
    private String templateParam;
    /**
     * 创建人
     */
    private String createId;

    @Data
    public static class Param{
        /**
         * 用户名
         */
        private String username;
        /**
         * 资源名称
         */
        private String resource;
        /**
         * 核销码
         */
        private String code;
    }
}
