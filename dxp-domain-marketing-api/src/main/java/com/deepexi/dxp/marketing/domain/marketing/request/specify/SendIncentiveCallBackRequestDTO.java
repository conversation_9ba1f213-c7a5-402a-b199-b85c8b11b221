package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 发送激励回调请求接口（发送小程序红包）
 * <AUTHOR>
 */
@Data
public class SendIncentiveCallBackRequestDTO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    @ApiModelProperty(value = "资源ID")
    @NotNull(message = "资源ID不能为空")
    private Long hisResourceId;

    @ApiModelProperty(value = "用户电话")
    @NotEmpty(message = "用户电话不能为空")
    private String phone;

    @ApiModelProperty(value = "支付平台唯一订单号")
    @NotEmpty(message = "支付平台唯一订单号不能为空")
    private String orderNo;
}
