package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author：liuyang
 * @version：1.0.0
 * @date：2021-03-31 17:08
 */
@Data
@ApiModel
public class MarketingKpiCheckNameRequest extends AbstractObject implements Serializable {


    @ApiModelProperty(value = "名称", required = true)
    private String name;

    @ApiModelProperty(value = "旧名称", required = false)
    private String oldName;

}
