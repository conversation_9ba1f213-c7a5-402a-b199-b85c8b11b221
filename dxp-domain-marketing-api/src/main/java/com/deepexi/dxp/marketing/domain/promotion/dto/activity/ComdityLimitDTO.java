package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/22 16:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class ComdityLimitDTO extends AbstractObject {

    @ApiModelProperty("品牌")
    private List<BaseActivityDTO> brandCode;
    @ApiModelProperty("所有商品")
    private List<BaseActivityDTO> allCommodity;
    @ApiModelProperty("商品限制类型id")
    private List<SkuCodeBaseDTO> skuList;
    @ApiModelProperty("类目")
    private List<BaseActivityDTO> frontCategory;

}
