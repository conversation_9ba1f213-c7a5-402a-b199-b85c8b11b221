package com.deepexi.dxp.marketing.domain.promotion.dto.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 活动推广方式
 */
@Data
public class ActivityPromotionChannelResponseDTO extends AbstractObject implements Serializable {

    @ApiModelProperty("主键")
    private Long id;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private String tenantId;
    /**
     * 接入方id
     */
    @ApiModelProperty("接入方id")
    private Long appId;
    /**
     * 活动ID
     */
    @ApiModelProperty("活动ID")
    private Long activityId;
    /**
     * 活动推广类型
     */
    @ApiModelProperty("活动推广类型")
    private Integer promotionType;
    /**
     * 0、微信小程序,1、微信内网页,2、抖音小程序
     */
    @ApiModelProperty("渠道类型:0、微信小程序,1、微信内网页,2、抖音小程序")
    private Integer type;
    /**
     * 链接
     */
    @ApiModelProperty("链接")
    private String url;
    /**
     * 渠道名称
     */
    @ApiModelProperty("渠道名称")
    private String channelName;
    /**
     * 二维码
     */
    @ApiModelProperty("二维码")
    private String qrCode;
    /**
     * 小程序码
     */
    @ApiModelProperty("小程序码")
    private String miniProgramCode;

    @ApiModelProperty("是否自定义:0-默认1-自定义")
    private Integer custom;

    @ApiModelProperty("是否可删除:0可以-1不可以,有关联数据")
    private Integer deleteType = 0;

    @ApiModelProperty("码类型")
    private String  codeType;

    @ApiModelProperty("小程序活动页连接")
    private String miniUrl;

    @ApiModelProperty("scene")
    private String scene;
}
