package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * //接送或者返回的商品
 *
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/11/25 16:50
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ActivityCommodityDTO extends AbstractObject implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("sku编码")
    private String skuCode;

    @ApiModelProperty("商品主键id")
    private Long skuId;

    @ApiModelProperty("商品所属店铺id")
    @NotNull(message = "店铺id不能为空")
    private Long shopId;

    @ApiModelProperty("上架id")
    @NotNull(message = "上架id不能为空")
    private Long upId;

    @ApiModelProperty("渠道id")
    @NotNull(message = "商品的上架渠道不能为空")
    private Long channelId;

    @ApiModelProperty("品牌")
    @NotBlank(message = "商品的品牌不能为空")
    private String brandId;

    @ApiModelProperty("类目")
    @NotBlank(message = "商品的类目不能为空")
    private String categoryId;

    @ApiModelProperty("原始的单品价格")
    @NotNull(message = "商品的价格不能为空")
    private BigDecimal detailPrice;

    @ApiModelProperty("优惠后的总价格")
    private BigDecimal discountsPriceAll;

    @ApiModelProperty("折扣")
    private BigDecimal discount;

    @ApiModelProperty("折扣")
    private BigDecimal couponDiscount;

    @ApiModelProperty("活动总折扣金额")
    private BigDecimal subtractPriceAll;

    @ApiModelProperty("优惠券总折扣金额")
    private BigDecimal couponSubtractPriceAll;

    @ApiModelProperty("优惠券信息(获得了该优惠券均摊)")
    private List<OrderCouponDTO> commodityCouponDTOList;

    @ApiModelProperty("ActivityId")
    private Long activityId;

    @ApiModelProperty("是否放弃使用活动，true=是不使用,false=使用，即activityId不传，false会返回默认活动")
    private boolean nonuseActivity;

    @ApiModelProperty("ActivityName")
    private String activityName;

    @ApiModelProperty("商品数量")
    @NotNull(message = "商品数量不能为空")
    private Integer skuAmount;

    @ApiModelProperty("单个需要积分")
    private Integer integral;

    @ApiModelProperty("扣减库存类型")
    private String stockType;

    /**
     * 没活动原因编码,枚举：NoActivityMsgEnum，-1=活动限制校验不通过，-2=指定活动不在商品可参与活动列表
     */
    @ApiModelProperty("没活动原因编码,枚举：NoActivityMsgEnum，-1=活动限制校验不通过，-2=指定活动不在商品可参与活动列表")
    private int noActivityCode;

    @ApiModelProperty("没活动原因")
    private String noActivityMsg;
}
