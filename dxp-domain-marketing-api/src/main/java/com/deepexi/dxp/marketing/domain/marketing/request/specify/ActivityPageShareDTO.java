package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 分享页
 * <AUTHOR>
 */

@ApiModel(value = "ActivityPageShareDTO入参对象", description = "ActivityPageShareDTO返回对象")
@Data
public class ActivityPageShareDTO extends SuperDTO {

private static final long serialVersionUID = 1L;


    /**
     * 基础配置通用ID
     */
    @ApiModelProperty(value = "基础配置通用ID")
    private Long activityId;

    /**
     * 分享标题
     */
    @ApiModelProperty(value = "分享标题")
    private String shareTitle;

    /**
     * 分享内容
     */
    @ApiModelProperty(value = "分享内容")
    private String shareContent;

    /**
     * 分享图题
     */
    @ApiModelProperty(value = "分享图题")
    private String shareIconUrl;

    /**
     * 海报标题
     */
    @ApiModelProperty(value = "海报标题")
    private String posterTitle;

    /**
     * 分享海报
     */
    @ApiModelProperty(value = "分享海报")
    private String sharePosterUrl;

    /**
     * 0、活动专题，1、活动
     */
    @ApiModelProperty(value = "0、活动专题，1、活动")
    private Integer type;
}