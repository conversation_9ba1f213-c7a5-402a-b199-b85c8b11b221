package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "活动和活动组列表返回对象", description = "活动和活动组列表返回对象")
public class ActivityAndActivityGroupResponseDTO  extends SuperVO {
    /**
     * 活动或活动组id + 类型唯一id
     */
    @ApiModelProperty("活动或活动组id + 类型唯一id")
    private String tId;
    /**
     * 活动或活动组名称
     */
    @ApiModelProperty("活动或活动组名称")
    private String name;

    /**
     * 活动状态：0=草稿，1=未开始，2=进行中，3=已完成，4=已终止
     */
    @ApiModelProperty(value = "`活动状态：0=草稿，1=未开始，2=进行中，3=已完成，4=已终止`")
    private Integer status;


    @ApiModelProperty("展示图片")
    private String pictures;

    @ApiModelProperty("类型（0活动组，1活动）")
    private Integer type;
}
