package com.deepexi.dxp.marketing.domain.promotion.query.activity;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @description: 查询优惠券-活动列表
 * @author: huimingway
 * @version: 1.0.0
 * @date: 2021-03-17 4:33 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class PromotionActivityCouponQuery extends SuperQuery {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    List<Long> ids;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    String activityName;
    /**
     * 活动类型
     */
    @ApiModelProperty("活动类型")
    private List<Integer> paTemplateList;


    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private List<Integer> statusList;


    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endTime;
    /**
     * 领取方式: handle
     * com.deepexi.dxp.marketing.enums.activity.strategy.condition.CouponConditionEnum
     */
    @ApiModelProperty(value = "领取方式: handle")
    private String strategyCondition;
    /**
     * 活动对象: allMember
     * com.deepexi.dxp.marketing.enums.activity.limit.PATemplateUserEnum
     */
    @ApiModelProperty(value = "活动对象: allMember")
    private String userLimit;
}
