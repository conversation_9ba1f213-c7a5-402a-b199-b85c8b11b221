package com.deepexi.dxp.marketing.domain.marketing.request.specify;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 派生指标公式明细项VO
 * @Author: HuangBo.
 * @Date: 2020/5/15 15:58
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingKpiFormulaVO extends SuperVO {

    /**
     * 派生指标ID
     */
    @ApiModelProperty(value = "派生指标ID",required = true)
    @NotNull(message = "派生指标不能为空")
    private Long kpiId;

    @ApiModelProperty(value = "派生指标名称")
    private String kpiName;

    /**
     * 选择的原生指标或派生指标ID
     */
    @ApiModelProperty(value = "选择的原生指标或派生指标ID",required = true)
    @NotNull(message = "原生指标或派生指标ID不能为空")
    private Long quoteKpiId;

    @ApiModelProperty(value = "选择的原生指标或派生指标名称")
    private String quoteKpiName;

    /**
     * 计算公式连接符号
     */
    @ApiModelProperty(value = "计算公式连接符号",required = true)
    @NotNull(message = "计算公式连接符号")
    private String symbols;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private int sort;

}
