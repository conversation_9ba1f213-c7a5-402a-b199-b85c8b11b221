package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.util.pojo.AbstractObject;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class ExcelExportActVerifyResponseDTO extends AbstractObject implements Serializable {
    private String title;
    private String fileName;
    private String[] headerKey;
    private String[] heardList;
    private List<Map<String, String>> dataList;
    private String sheetName = "sheet1";
}
