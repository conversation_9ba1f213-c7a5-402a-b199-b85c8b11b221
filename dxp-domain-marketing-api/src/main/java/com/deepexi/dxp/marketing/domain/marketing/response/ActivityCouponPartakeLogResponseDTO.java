package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.FeedbackInfoVO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHisDetailResponseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠券活动优惠券参与明细
 * <AUTHOR>
 */
@Data
@ApiModel(value = "优惠券活动优惠券参与明细返回对象", description = "优惠券活动优惠券参与明细返回对象")
public class ActivityCouponPartakeLogResponseDTO extends SuperVO {
    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    private String idCard;

    /**
     * 所在地区
     */
    @ApiModelProperty(value = "所在地区")
    private String area;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String detaAddress;

    /**
     * 中奖结果
     */
    @ApiModelProperty(value = "中奖结果")
    private String prizeResult;

    /**
     * 兑奖码
     */
    @ApiModelProperty(value = "兑奖码")
    private String code;

    /**
     * 领奖时间
     */
    @ApiModelProperty(value = "领奖时间")
    private String getTime;

    /**
     * 核销时间
     */
    @ApiModelProperty(value = "核销时间")
    private String verifyTime;
    /**
     * 核销人
     */
    @ApiModelProperty(value = "核销人")
    private String verifyBy;

    /**
     * 活动订单id
     */
    @ApiModelProperty(value = "活动订单编号")
    private String orderNo;

    /**
     * 奖品ID
     */
    @ApiModelProperty(value = "奖品ID")
    private Long resourceId;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date payTime;

    /**
     *  支付金額
     */
    @ApiModelProperty(value = "支付金額")
    private BigDecimal payMoney;

    /**
     * unionId
     */
    @ApiModelProperty(value = "unionId")
    private String unionId;

    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty(value = "0、微信小程序  1、H5  2、抖音")
    private Integer type;


    @ApiModelProperty(value = "资源信息")
    private ResourceHisDetailResponseDTO resourceHisDetailResponseDTO;

    /**
     * 登记明细
     */
    @ApiModelProperty(value = "登记明细")
    private FeedbackInfoVO feedback;


    /**
     * 微信头像URL
     */
    @ApiModelProperty(value = "微信头像URL")
    private String avatar;

    /**
     * 所需助力人数
     */
    @ApiModelProperty(value = "所需助力人数")
    private Integer needFissonCount;

    /**
     * 当前已助力人数
     */
    @ApiModelProperty(value = "当前已助力人数")
    private Integer currentFissonCount;

    /**
     * 当前已砍价价格
     */
    @ApiModelProperty(value = "当前已砍价价格")
    private BigDecimal currentFissonPrice;


    /**
     * 剩余助力时长（剩余砍价时长）
     */
    @ApiModelProperty(value = "剩余助力时长（剩余砍价时长）")
    private Date fissonEndTime;

    /**
     * 助力砍价状态 0、助力中（砍价中），1、助力成功（砍价成功）,2、助力失败（砍价失败）
     */
    @ApiModelProperty(value = "助力砍价状态 0、助力中（砍价中），1、助力成功（砍价成功）,2、助力失败（砍价失败）")
    private Integer fissonStatus;

    /**
     * 裂变活动类型：0、点击类助力活动；1、砍价活动
     */
    @ApiModelProperty(value = "裂变活动类型：0、点击类助力活动；1、砍价活动")
    private Integer fissonType;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;
}
