package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 原生指标和派生指标DTO
 * @Author: HuangBo.
 * @Date: 2020/5/15 15:42
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingKpiDTO extends SuperDTO {

    /**
     * 原生指标
     */
    public final static Integer ORIGINAL_TYPE = 1;
    /**
     * 派生指标
     */
    public final static Integer DERIVATIVE_TYPE = 2;


    /**
     * 编码
     */
    private String code;

    /**
     * 指标类型(1:原生指标; 2:派生指标)
     */
    private int type;

    /**
     * 任务目标名称
     */
    private String name;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 排序
     */
    private int sort;

    /**
     * 更新频率类型(1:实时，2:间隔)，只针对原生指标
     */
    private int frequencyType;

    /**
     * 更新频率
     */
    private String frequency;

    /**
     * 1:启用; 2:禁用
     */
    private int status;

    /**
     * 派生指标引用的指标以及连接计算时公式符号集合
     */
    private List<MarketingKpiFormulaDTO> formulaList;

    /**
     * 是否已经被选作核心指标
     */
    private boolean used;

}
