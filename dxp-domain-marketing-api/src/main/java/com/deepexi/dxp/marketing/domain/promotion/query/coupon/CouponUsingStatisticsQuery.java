package com.deepexi.dxp.marketing.domain.promotion.query.coupon;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class CouponUsingStatisticsQuery extends SuperQuery {
    private static final long serialVersionUID = 3650480958230985383L;

    /**
     * 来源 id
     */
    @ApiModelProperty("来源 id")
    @NotNull(message = "来源不能为空")
    private Long releaseId;

    /**
     * 来源类型
     */
    @ApiModelProperty("来源类型")
    @NotNull(message = "来源类型为空")
    private Integer releaseType;

}
