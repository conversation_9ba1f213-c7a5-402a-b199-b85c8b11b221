package com.deepexi.dxp.marketing.domain.marketing.request.specify;


import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "ActivityTarget入参对象", description = "ActivityTarget入参对象")
public class ActivityTargetRequestDTO extends SuperDTO {



  /**
  *目标名称
  **/
  @ApiModelProperty(value = "目标名称",required = true)
  @NotBlank(message = "目标名称不能为空")
  private String targetName;


}