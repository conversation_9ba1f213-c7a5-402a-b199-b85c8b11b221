package com.deepexi.dxp.marketing.domain.promotion.dto.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 房源导出
 */
@Data
@ApiModel
public class PromotionResourceExportResponseDTO extends AbstractObject {
    /**
     * 资源名称
     */
    @ApiModelProperty("资源名称")
    private String name;
    /**
     * 卡劵面值
     */
    @ApiModelProperty("卡劵面值/折扣")
    private String couponValue;

    /**
     * 使用时间 eg：2021-04-05至2021-05-05
     */
    @ApiModelProperty("使用时间")
    private  String validTime;

    /**
     * 使用规则
     */
    @ApiModelProperty("使用规则")
    private String useRule;

    /**
     * 房源名称
     */
    @ApiModelProperty("房源名称")
    private String houseName;
    /**
     * 房源面积
     */
    @ApiModelProperty("房源面积")
    private String houseVolume;
    /**
     * 房源信息
     */
    @ApiModelProperty("房源信息")
    private String houseMessage;
    /**
     * 原价
     */
    @ApiModelProperty("原总价")
    private String costPrice;
    /**
     * 折扣价
     */
    @ApiModelProperty("折后总价")
    private String discountPrice;

    @ApiModelProperty("原单价(万元)")
    private String beforeCostPrice;

    @ApiModelProperty("折后单价(万元)")
    private String afterDiscountPrice;

    /**
     * 失败原因
     */
    @ApiModelProperty("失败原因")
    private String failReason;
}
