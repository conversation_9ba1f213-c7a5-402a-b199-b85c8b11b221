package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import lombok.Data;

import java.util.Map;

/**
 * 神策埋点请求参数
 * <AUTHOR>
 */
@Data
public class SensorsBuriedPointDTO {

    /**
     * 神策事件名
     */
    private String eventName;

    /**
     * 用户注册登录之后，系统分配的注册ID
     */
    private String registerId;

    /**
     * 用户匿名访问网站，cookieId 默认神策生成分配
     */
    private String cookieId;

    /**
     * 用户匿名访问网站，cookieId 默认神策生成分配
     */
    private Map<String,Object> superProperties;
}
