package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 项目参与活动中间表
 * <AUTHOR>
 */
@ApiModel(value = "活动适用项目入参对象", description = "活动适用项目入参对象")
@Data
@NoArgsConstructor
public class ActivityParticipationVO extends AbstractObject{

    /**
     * 项目代码
     */
    @ApiModelProperty(value = "项目代码")
    private String projectId;

    /**
     * 区域ID
     */
    @ApiModelProperty(value = "区域ID")
    private String areaId;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "城市ID")
    private String cityId;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "地理位置，城市ID")
    private String realCityId;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "地理位置，城市名称")
    private String realCityName;

    /**
     * 组织代码
     */
    @ApiModelProperty(value = "组织代码")
    private String orgId;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String orgName;
    /**
     * 分期列表
     */
    @ApiModelProperty(value = "分期-销售组织列表")
    private List<PorjectPeriodDTO> porjectPeriodList;

    /**
     * 	岗位编码
     */
    @ApiModelProperty(value = "岗位编码")
    private String positionId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目序号
     */
    @ApiModelProperty(value = "项目序号")
    private String projectNumber;

    /**
     * 销售组织代码
     */
    @ApiModelProperty(value = "销售组织代码")
    private String saleOrgId;
    /**
     * 销售组织名称
     */
    @ApiModelProperty(value = "销售组织名称")
    private String saleOrgName;

    /**
     * 用户别名
     */
    @ApiModelProperty(value = "用户别名")
    private String userAlias;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 是否可跳转（0不可跳转，1可跳转）
     */
    @ApiModelProperty(value = "是否可跳转（0不可跳转，1可跳转）")
    private Integer isJumpable;

    /**
     * 区域ID
     */
    @ApiModelProperty(value = "地理位置，区域ID")
    private String realAreaId;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "地理位置，区域名称")
    private String realAreaName;

    public ActivityParticipationVO(String projectId, String realCityId, String realCityName,String cityId, String cityName) {
        this.projectId = projectId;
        this.realCityId = realCityId;
        this.cityId = cityId;
        this.cityName = cityName;
        this.realCityName = realCityName;
    }

    /**
     * 分期-销售组织
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class PorjectPeriodDTO extends AbstractObject implements Serializable {

        @ApiModelProperty(value = "项目代码")
        private String projectId;

        @ApiModelProperty(value = "分期代码")
        private String periodId;

        @ApiModelProperty(value = "分期名称")
        private String periodName;

        @ApiModelProperty(value = "组织编码")
        private String saleOrgId;

        @ApiModelProperty(value = "组织名称")
        private String saleOrgName;
    }
}
