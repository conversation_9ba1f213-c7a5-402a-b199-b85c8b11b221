package com.deepexi.dxp.marketing.domain.promotion.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@Data
@ApiModel
public class ActivityPromotionChannelUpdateRequestDTO extends AbstractObject {

    @ApiModelProperty("主键")
    private Long id;
    /**
     * 渠道名称
     */
    @NotNull(message = "渠道名称不能为空")
    @ApiModelProperty(value = "渠道名称",required = true)
    @Length(max = 20, message = "渠道名称不能超过200个字符")
    private String channelName;

}
