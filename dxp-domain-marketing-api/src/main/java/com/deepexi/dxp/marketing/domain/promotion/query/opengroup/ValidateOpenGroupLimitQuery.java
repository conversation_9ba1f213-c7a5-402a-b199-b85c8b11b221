package com.deepexi.dxp.marketing.domain.promotion.query.opengroup;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 当前可参与的可拼团的商品列表 条件参数实体
 * 即：验证拼团限制入参DTO
 *
 * <AUTHOR> xianfeng.cai
 * @date 2019/11/29 14:08
 */
@Data
@ApiModel
public class ValidateOpenGroupLimitQuery extends AbstractObject {

    @ApiModelProperty("渠道id")
    @NotBlank(message = "渠道信息不能为空")
    private String tenantId;
    @ApiModelProperty("appId")
    @NotNull(message = "应用id不能为空")
    private Long appId;

    @ApiModelProperty("活动Id")
    @NotNull(message = "活动id不能为空")
    private Long activityId;
    @ApiModelProperty("用户Id")
    @NotNull(message = "会员id不能为空")
    private Long userId;
}