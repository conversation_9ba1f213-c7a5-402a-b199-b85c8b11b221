package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageShareDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.PromotionHisResourceDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityResponseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/27
 */
@Data
public class CardCollectingHomePageInfoResponseDTO {

    /**
     * 活动详情
     */
    PromotionActivityResponseDTO promotionActivity;

    @ApiModelProperty(value = "活动页信息")
    private ActivityPageVO activityPageDTO;

    @ApiModelProperty(value = "分享页信息")
    private ActivityPageShareDTO activityPageShareDTO;


    /**
     * 奖品列表
     */
    List<PromotionHisResourceDTO> prizeList;

    /**
     * 卡片
     */
    List<PromotionHisResourceDTO> cardList;

}
