package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageShareDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.PromotionHisResourceDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 活动抽奖奖品列表和剩余抽奖次数
 * <AUTHOR>
 */
@Data
public class PrizeListAndSurplusNumberDTO {

    /**
     * 活动详情
     */
    PromotionActivityResponseDTO promotionActivity;
    /**
     * 奖品列表
     */
    List<PromotionHisResourceDTO> prizeList;

    @ApiModelProperty(value = "活动页信息")
    private ActivityPageDTO activityPageDTO;

    @ApiModelProperty(value = "分享页信息")
    private ActivityPageShareDTO activityPageShareDTO;

    /**
     * 剩余抽奖次数
     */
    @ApiModelProperty(value = "剩余抽奖次数")
    Integer drawNumber;

    /**
     * 剩余分享次数
     */
    @ApiModelProperty(value = "剩余分享次数")
    Integer shareNumber;

    /**
     * 分享后获得抽奖次数
     */
    @ApiModelProperty(value = "分享后获得抽奖次数")
    Integer shareDrawNumber;

    /**
     * 是否显示切换按钮
     */
    @ApiModelProperty(value = "是否显示切换按钮")
    Boolean isShowSwitch;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     *项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     *项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectCity;
}
