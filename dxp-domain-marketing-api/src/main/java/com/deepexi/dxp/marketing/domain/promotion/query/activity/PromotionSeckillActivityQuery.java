package com.deepexi.dxp.marketing.domain.promotion.query.activity;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * 秒杀任务查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/9/21 11:46
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
@ToString
public class PromotionSeckillActivityQuery extends SuperQuery {
    private static final long serialVersionUID = 321603764958033939L;
    @ApiModelProperty("开始时间")
    Date startTime;
    @ApiModelProperty("结束时间")
    Date endTime;
}
