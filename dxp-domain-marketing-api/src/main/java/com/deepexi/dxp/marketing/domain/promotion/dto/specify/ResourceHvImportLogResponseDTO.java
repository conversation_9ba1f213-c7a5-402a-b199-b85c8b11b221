package com.deepexi.dxp.marketing.domain.promotion.dto.specify;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class ResourceHvImportLogResponseDTO extends AbstractObject implements Serializable {

    @ApiModelProperty("主键")
    private Long id;

    /**
     * 导入数
     */
    @ApiModelProperty("导入数")
    private Integer importNum;
    /**
     * 成功数
     */
    @ApiModelProperty("成功数")
    private Integer successNum;
    /**
     * 失败数
     */
    @ApiModelProperty("失败数")
    private Integer failNum;
    /**
     * 状态 0导入中,1导入成功
     */
    @ApiModelProperty("导入状态")
    private Integer importStatus;
    /**
     * 失败记录
     */
    /*@ApiModelProperty("失败记录")
    private String importFailJson;*/


    /**
     * 导入时间
     */
    @ApiModelProperty("导入时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdTime;

    @ApiModelProperty("操作者")
    private String createdBy;
}
