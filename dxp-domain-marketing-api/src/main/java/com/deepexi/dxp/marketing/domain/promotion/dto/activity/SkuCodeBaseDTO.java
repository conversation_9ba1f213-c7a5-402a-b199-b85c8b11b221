package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * //sku商品限制信息
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/22 16:25
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ToString
@ApiModel
public class SkuCodeBaseDTO extends AbstractObject {


    @ApiModelProperty("商品限制类型id")
    private String id;
    @ApiModelProperty("商品配置库存")
    private String commodityInventory;
    @ApiModelProperty("skuId")
    private String skuId;
    @ApiModelProperty("skuCode")
    private String skuCode;

    @ApiModelProperty("商品限制类型id")
    private String stockType;

    @ApiModelProperty("商品的店铺id")
    private String shopId;
    @ApiModelProperty("上架id")
    private String upId;
    @ApiModelProperty("商品详细信息")
    private String valueDetail;
    @ApiModelProperty("秒杀价格")
    private String price;
    @ApiModelProperty("每人限购")
    private String limit;

    @ApiModelProperty("渠道id")
    private Long channelId;


}
