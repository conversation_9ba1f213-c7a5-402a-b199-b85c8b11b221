package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "活动关联项目返回对象", description = "活动关联项目返回对象")
public class ProjectListResponseDTO {

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "项目列表")
    private List<ActivityParticipationDTO> projectList;
}
