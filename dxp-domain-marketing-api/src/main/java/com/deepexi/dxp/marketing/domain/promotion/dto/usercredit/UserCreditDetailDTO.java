package com.deepexi.dxp.marketing.domain.promotion.dto.usercredit;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 查询余额充值活动详情对象响应实体.
 *
 * <AUTHOR> feng<PERSON>
 * @date 2020/03/12 17:08
 */
@Data
@ApiModel
public class UserCreditDetailDTO {

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String activityName;

    /**
     * 模板id 活动从模板来的 所以不难为空
     */
    @ApiModelProperty(value = "模板id 活动从模板来的 所以不能为空")
    private Integer paTemplateId;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long id;

    /**
     * 活动金额
     */
    @ApiModelProperty("活动金额")
    private BigDecimal money;

    /**
     * 赠送余额
     */
    @ApiModelProperty("赠送余额")
    private BigDecimal balance;

    /**
     * 赠送积分
     */
    @ApiModelProperty("赠送积分")
    private BigDecimal integral;

    /**
     * 是否可用标识
     */
    @ApiModelProperty("是否可用标识")
    private Boolean isAvailable = true;
}
