package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发送激励请求接口（发送微信，小程序红包）
 * <AUTHOR>
 */
@Data
public class RechargeMobileRequestDTO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "话费充值")
    RechargeMobileRequest rechargeMobileRequest;

    @ApiModelProperty(value = "资源ID")
    private Long hisResourceId;

    @ApiModelProperty(value = "资源ID")
    private String resourceName;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("昵称")
    private String nickName;

    /**
     * 0、微信小程序 1、H5  2、抖音
     */
    @ApiModelProperty(value = "来源")
    private Integer type;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty("openId")
    private String openid;

    @ApiModelProperty(value = "unionId")
    private String unionId;

    @ApiModelProperty(value = "核销码")
    private String code;
}
