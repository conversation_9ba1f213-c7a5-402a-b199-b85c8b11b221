package com.deepexi.dxp.marketing.domain.marketing.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/12/27
 */
@Data
public class CardCollectingPrizeResponseDTO {

    @ApiModelProperty(value = "中奖记录id")
    private Long id;

    @ApiModelProperty(value = "活动资源id")
    private Long hisResourceId;

    @ApiModelProperty(value = "原资源id")
    private Long resourceId;

    @ApiModelProperty(value = "资源类型: 0、卡劵 1、第三方接入")
    private Integer type;

    @ApiModelProperty(value = "细分类别: 0-普通劵, 1-房源劵, 2-商品劵")
    private Integer couponCategory;

    @ApiModelProperty(value = "第三方细分类别 0、手机话费 1、现金红包")
    private Integer thirdCategory;

    @ApiModelProperty(value = "资源名称")
    private String name;

    @ApiModelProperty(value = "卡劵类型 0、代金劵 1、折扣劵")
    private Integer couponType;

    @ApiModelProperty(value = "卡劵面值")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "资源图片")
    private String url;

    @ApiModelProperty(value = "使用时间类型 0 、不限制 1、指定时间 2、有效天数")
    private Integer validTimeType;

    @ApiModelProperty(value = "礼品券发放方式:0-线上邮寄,1-线下核销")
    private Integer grantWay;

    @ApiModelProperty(value = "有效期开始时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validStartTime;

    @ApiModelProperty(value = "有效期结束时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validEndTime;

    @ApiModelProperty(value = "有效天数")
    private Integer validDay;

    @ApiModelProperty(value = "使用规则")
    private String useRule;

    @ApiModelProperty(value = "助力资源类型:0-助力阶梯,1-分享获奖,2-帮忙助力获奖")
    private Integer fissonResourceType;

    @ApiModelProperty(value = "阶梯排序号")
    private Integer ladderSort;

    @ApiModelProperty(value = "projectId")
    private String projectId;

    @ApiModelProperty(value = "projectName")
    private String projectName;

    @ApiModelProperty(value = "是否领取")
    private Integer received = 0;

    @ApiModelProperty(value = "房源名称")
    private String houseName;

    @ApiModelProperty(value = "房源面积")
    private String houseVolume;

    @ApiModelProperty(value = "房源信息")
    private String houseMessage;

    @ApiModelProperty(value = "原价")
    private String costPrice;

    @ApiModelProperty(value = "折扣价")
    private String discountPrice;
}
