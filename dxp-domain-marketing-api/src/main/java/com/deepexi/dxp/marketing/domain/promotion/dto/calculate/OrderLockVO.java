package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.dxp.marketing.domain.promotion.dto.opengroup.PromotionOpenGroupDetailResponseDTO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 下单锁定返回
 *
 * <AUTHOR> xinjian.yao
 * @date 2019/12/2 16:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@ApiModel
public class OrderLockVO extends AbstractObject {
    @ApiModelProperty("下单锁定结果")
    private Boolean resultFlag;
    @ApiModelProperty("下单锁定返回拼团信息")
    private PromotionOpenGroupDetailResponseDTO openGroupDetailResponseDTO;
}
