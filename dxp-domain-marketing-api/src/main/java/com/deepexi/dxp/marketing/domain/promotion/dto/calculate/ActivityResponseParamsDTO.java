package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.*;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 活动下的商品优惠类
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/25 17:48
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
@ToString
public class ActivityResponseParamsDTO extends AbstractObject {

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("活动编码")
    private String activityCode;

    @ApiModelProperty("活动描述")
    private String description;

    @ApiModelProperty("活动开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;

    @ApiModelProperty("活动结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;

    @ApiModelProperty("优惠券信息")
    private List<OrderCouponDTO> orderCouponDTOList;

    @ApiModelProperty("活动优惠模板Id")
    private String paTemplateId;

    @ApiModelProperty("活动优惠类型名称")
    private String paTemplateName;

    @ApiModelProperty("活动商品原始的价格")
    private BigDecimal detailPrice;

    @ApiModelProperty("活动商品优惠后的价格")
    private BigDecimal discountsPrice;

    @ApiModelProperty("活动商品折扣")
    private BigDecimal discount;

    @ApiModelProperty("优惠券折扣")
    private BigDecimal couponDiscount;

    @ApiModelProperty("活动商品减免金额")
    private BigDecimal subtractPrice;

    @ApiModelProperty("优惠券减免金额")
    private BigDecimal couponSubtractPrice;

    @ApiModelProperty("是否可用优惠券")
    private Boolean couponFlag;

    @ApiModelProperty("活动下的商品")
    private List<ActivityCommodityDTO> activityCommodityDTOList;

    @ApiModelProperty("需要总积分")
    private Integer integralAll;

    @ApiModelProperty("0 全款可退 1 定金可退 2 不可退 （默认是0）")
    private Integer returnMoneyType;

    @ApiModelProperty("送积分")
    private String point;

    @ApiModelProperty("送优惠券")
    private List<String> couponList;

    @ApiModelProperty("送商品")
    private SendCommodityDTO sendCommodityDTO;

    @ApiModelProperty("拼团基本信息")
    private GroupInfoDTO groupInfoDTO;

    @ApiModelProperty("没有活动的商品或者商品达不到享受活动的最低要求")
    private List<ActivityCommodityDTO> noActivityCommodityDTOList;

    @ApiModelProperty("预售活动商品信息")
    private PreSalesCommodityDTO preSalesCommodityDTO;
}
