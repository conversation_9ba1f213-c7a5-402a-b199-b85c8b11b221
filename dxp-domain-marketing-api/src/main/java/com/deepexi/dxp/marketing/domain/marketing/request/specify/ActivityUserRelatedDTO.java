package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 活动用户关系表
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ActivityUserRelatedDTO extends SuperDTO {
    public ActivityUserRelatedDTO(Long activityId, String userId, String phone) {
        this.activityId = activityId;
        this.userId = userId;
        this.phone = phone;
    }
    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 用户电话
     */
    @ApiModelProperty(value = "用户电话")
    private String phone;

    /**
     * 参与类型（0按天计算，1按人计算）
     */
    @ApiModelProperty(value = "参与类型（0按天计算，1按人计算）")
    private Integer joinType;
//
//    /**
//     * 抽奖次数
//     */
//    @ApiModelProperty(value = "抽奖次数")
//    private Integer drawNumber;
//
//    /**
//     * 分享次数
//     */
//    @ApiModelProperty(value = "分享次数")
//    private Integer shareNumber;

    /**
     * unionId
     */
    @ApiModelProperty(value = "unionId")
    private String unionId;

    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty(value = "0、微信小程序  1、H5  2、抖音")
    private Integer type;

    @ApiModelProperty(value = "用户数据信息")
    private Map<String,Object> limits;
//    /**
//     * 项目切换次数
//     */
//    @ApiModelProperty(value = "项目切换次数")
//    private Integer switchNumber;
}
