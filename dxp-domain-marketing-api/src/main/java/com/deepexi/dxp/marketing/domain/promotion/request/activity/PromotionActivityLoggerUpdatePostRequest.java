package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.util.domain.request.BaseExtRequest;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 促销活动日志
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/21 16:31
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionActivityLoggerUpdatePostRequest extends BaseExtRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单第一次支付时间 用于预售活动的定金支付，或者全款支付
     */
    private Date firstPayTime;

    /**
     * 订单最后支付时间 用于预售活动的尾款支付
     */
    private Date endPayTime;
    /**
     * 用户活动记录code
     */
    private String code;
    /**
     * 多久时间前需要支付尾款
     */
    private Date finalPaymentTime;

    /**
     * 0 锁定状态 1 支付完成状态 2 退货
     */
    @ApiModelProperty(value = "0 锁定状态 1 支付完成状态 2 退货")
    private Integer status;


    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    /**
     * 会员id
     */
    @ApiModelProperty(value = "会员id")
    private Long userId;

    /**
     * 订单参团id
     */
    @ApiModelProperty("订单参团id")
    private Long activityGroupId;

}