package com.deepexi.dxp.marketing.domain.promotion.request.opengroup;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 传输下单请求相关参数的DTO
 *
 * <AUTHOR> xianfeng.cai
 * @date 2019/11/29 14:08
 */
@Data
@ApiModel
public class OpenGroupOrderRequest extends AbstractObject {
    @ApiModelProperty("租户")
    private String tenantId;
    @ApiModelProperty("应用")
    private Long appId;

    @ApiModelProperty("活动id")
    private Long activityId;
    @ApiModelProperty("参团id")
    private Long activityGroupId;
    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("活动类型")
    private Integer activityType;
    @ApiModelProperty("用户类型")
    private String userType;
    @ApiModelProperty("渠道")
    private String clientTenant;
    @ApiModelProperty("活动商品")
    private ActivityCommodityDTO activityCommodityDTO;

    @ApiModelProperty(value = "订单号")
    private String orderCode;
    @ApiModelProperty(value = "订单数量")
    private Integer orderNum;

    @ApiModelProperty(value = "批量订单编号")
    private List<String> orderCodes;

//    @ApiModelProperty("活动商品")
//    private List<ActivityCommodityDTO> activityCommodityDTOList;
}