package com.deepexi.dxp.marketing.domain.marketing.response;


import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ActivityTarget返回对象", description = "ActivityTarget返回对象")
public class ActivityTargetResponseDTO extends SuperDTO {



  /**
  *目标名称
  **/
  @ApiModelProperty(value = "目标名称")
  private String targetName;


}