package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class InteractiveResponseDTO {

    @ApiModelProperty(value = "返回码，请求成功200，请求失败999")
    private String code;

    @ApiModelProperty(value = "返回结果说明")
    private String msg;

    @ApiModelProperty(value = "返回数据")
    private Objects data;
}
