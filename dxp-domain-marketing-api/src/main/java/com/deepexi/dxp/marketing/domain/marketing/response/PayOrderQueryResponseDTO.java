package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.CouponPayCallBackRequestDTO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "查询支付订单返回", description = "查询支付订单返回")
public class PayOrderQueryResponseDTO  extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "编码,200:代表成功，其余失败。如果失败，请在msg里面填入具体内容")
    private String code;

    @ApiModelProperty(value = "消息")
    private String msg;

    @ApiModelProperty(value = "数据")
    private CouponPayCallBackRequestDTO data;
}
