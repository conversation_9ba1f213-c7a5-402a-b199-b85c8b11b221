package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 抽奖活动详情信息返回
 * <AUTHOR>
 */
@Data
@ApiModel(value = "抽奖活动详情信息返回对象", description = "抽奖活动详情信息返回对象")
public class LuckyDrawResponseDTO extends SuperVO {
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private Integer status;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private String startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private String endTime;

    @ApiModelProperty(value="模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId;


    @ApiModelProperty(value = "活动扩展信息")
    private ActivityExtVO activityExtVO;

    @ApiModelProperty(value = "适用项目")
    private List<ActivityParticipationDTO> projectList;

    /**
     * 规则配置
     */
    @ApiModelProperty(value = "规则配置")
    private RuleConfigVO ruleConfigVO;


    @ApiModelProperty(value = "奖品配置")
    @NotNull(message = "奖品配置不能为空")
    @Valid
    private List<PromotionHisResourceDTO> prizeList;

    @ApiModelProperty(value = "活动页信息")
    private ActivityPageDTO activityPageDTO;

    @ApiModelProperty(value = "分享页信息")
    private ActivityPageShareDTO activityPageShareDTO;
}
