package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.LocationSignRuleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 参与活动详情返回对象
 */
@Data
public class LocationSignRuleVO extends LocationSignRuleDTO {

    @ApiModelProperty(value = "可抽奖次数")
    private Integer remainingDrawNumber = 0;

    @ApiModelProperty(value = "签到地点分类名称")
    private List<String> subSignTypeName;

    @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
    private Integer paTemplateId = 15;
}
