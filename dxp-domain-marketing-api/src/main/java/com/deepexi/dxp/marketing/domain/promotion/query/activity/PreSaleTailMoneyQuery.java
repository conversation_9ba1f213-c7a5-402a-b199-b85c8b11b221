package com.deepexi.dxp.marketing.domain.promotion.query.activity;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 尾款查询入参
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2020/3/19 18:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class PreSaleTailMoneyQuery extends AbstractObject {


    @ApiModelProperty("尾款时间")
    @NotNull(message = "尾款时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date tailDate;

    @ApiModelProperty("渠道类型==信息")
    private String tenantId;
    @ApiModelProperty("应用id")
    private Long appId;
}
