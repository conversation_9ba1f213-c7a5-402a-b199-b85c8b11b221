package com.deepexi.dxp.marketing.domain.promotion.dto.specify;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
public class PromotionResourceResponseDTO extends AbstractObject implements Serializable {


    /**
     * 主键id
     */
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 资源类型 0、卡劵 1、第三方接入
     */
    @ApiModelProperty("资源类型")
    private Integer type;
    /**
     * 细分类别 0、普通劵 1、房源劵 2、商品劵
     */
    @ApiModelProperty("细分类别")
    private Integer couponCategory;
    /**
     * 细分类别 0、手机话费 1、现金红包
     */
    @ApiModelProperty("第三方细分类别")
    private Integer thirdCategory;
    /**
     * 资源名称
     */
    @ApiModelProperty("资源名称")
    private String name;
    /**
     * 卡劵类型 0、代金劵 1、折扣劵
     */
    @ApiModelProperty("卡劵类型")
    private Integer couponType;
    /**
     * 卡劵面值
     */
    @ApiModelProperty("卡劵面值")
    private BigDecimal couponValue;
    /**
     * 资源图片
     */
    @ApiModelProperty("资源图片")
    private String url;
    /**
     * 使用时间类型 0 、不限制 1、指定时间 2、有效天数
     */
    @ApiModelProperty("使用时间类型")
    private Integer validTimeType;

    @ApiModelProperty("礼品券发放方式")
    private Integer grantWay;

    /**
     * 有效期开始时间
     */
    @ApiModelProperty("有效期开始时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date validStartTime;
    /**
     * 有效期结束时间
     */
    @ApiModelProperty("有效期结束时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date validEndTime;
    /**
     * 有效天数
     */
    @ApiModelProperty("有效天数")
    private Integer validDay;
    /**
     * 使用规则
     */
    @ApiModelProperty("使用规则")
    private String useRule;
    /**
     * 房源名称
     */
    @ApiModelProperty("房源名称")
    private String houseName;
    /**
     * 房源面积
     */
    @ApiModelProperty("房源面积")
    private String houseVolume;
    /**
     * 房源信息
     */
    @ApiModelProperty("房源信息")
    private String houseMessage;
    /**
     * 原价
     */
    @ApiModelProperty("原价")
    private String costPrice;
    /**
     * 折扣价
     */
    @ApiModelProperty("折扣价")
    private String discountPrice;
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String itemName;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdTime;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updatedTime;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private String tenantId;
    /**
     * 应用id
     */
    @ApiModelProperty("应用id")
    private Long appId;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 优惠卷code
     */
    @ApiModelProperty("优惠卷code")
    private String code;
    /**
     * 拓展字段
     */
    @ApiModelProperty("拓展字段")
    private String ext;

    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 原单价或原总价类型:0-原单价,1-原总价
     */
    @ApiModelProperty("cost_price_type")
    private Integer costPriceType;


    /**
     * 折后总价及折后单价类型:0-折后单价,1-折后总价
     */
    @ApiModelProperty("discount_price_type")
    private Integer discountPriceType;
}
