package com.deepexi.dxp.marketing.domain.marketing.response;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 指标路径图节点VO
 * @Author: HuangBo.
 * @Date: 2020/6/18 11:33
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Api("指标路径图节点")
public class MarketingKpiRouteMapNodeVO extends SuperVO {


    /**
     * 路径图ID
     */
    @ApiModelProperty(value = "路径图ID" ,required = true)
    @NotNull(message = "路径图ID不能为空")
    private Long routeMapId;

    /**
     * 前端节点ID(与前端图xml数据进行映射)
     */
    @ApiModelProperty(value = "路径图数据节点ID" ,required = true)
    @NotNull(message = "路径图数据节点ID不能为空")
    private String nodeId;

    /**
     * 节点名称
     */
    @ApiModelProperty(value = "路径图节点名称" ,required = true)
    @NotBlank(message = "路径图节点名称不能为空")
    @Length(max = 8, message = "节点名称不能超过8个字符")
    private String name;

    /**
     * 节点图标
     */
    @Length(max = 255, message = "图标URL不能超过255个字符")
    private String iconUrl;

    /**
     * 节点配置的指标项
     */
    @NotEmpty(message = "路径图节点指标不能为空")
    private List<MarketingKpiRouteMapNodeItemsVO> nodeItemsList;


}
