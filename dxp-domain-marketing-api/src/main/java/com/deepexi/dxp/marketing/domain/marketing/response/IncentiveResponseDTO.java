package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value = "发送激励返回参数", description = "发送激励返回参数")
@Data
public class IncentiveResponseDTO {

    @ApiModelProperty(value = "200代表成功，其余失败。如果失败，请在msg里面填入具体内容")
    private String code;

    @ApiModelProperty(value = "msg")
    private String msg;

    @ApiModelProperty(value = "业务信息字段，json格式")
    private IncentiveResponseResult data;

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("发送激励返回数据")
    public static class IncentiveResponseResult extends AbstractObject implements Serializable {

        @ApiModelProperty(value = "支付平台唯一订单号")
        private String orderNo;

        @ApiModelProperty(value = "激励发放成功时间")
        private String successTime;

        @ApiModelProperty(value = "时间戳")
        private String timeStamp;

        @ApiModelProperty(value = "退款状态SUCCESS：成功，FAIL：失败")
        private String status;

        @ApiModelProperty(value = "随机字符串")
        private String nonceStr;

        @ApiModelProperty(value = "签名类型，目前仅支持MD5")
        private String signType;

        @ApiModelProperty(value = "签名，使用字段appId、timeStamp、nonceStr、package计算得出的签名值")
        private String paySign;

        @ApiModelProperty(value = "订单详情扩展字符串")
        private String packageExt;
    }
}
