package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 裂变排行榜
 */
@Data
@ApiModel(value = "裂变排行榜返回对象", description = "裂变排行榜返回对象")
public class FissionRankAdminResponseDTO {

    @ApiModelProperty(value = "昵称list")
    private List<String> nickNameList;

    @ApiModelProperty(value = "数量list")
    private List<Integer> cntList;
}
