package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "活动分析渠道分布返回对象", description = "活动分析渠道分布返回对象")
public class ChannelDistributionResponseDTO implements Serializable {

    @ApiModelProperty(value = "渠道类型")
    private Integer type;

    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "访问人数")
    private Long accessPeopleNumber;

    @ApiModelProperty(value = "参与人数")
    private Long joinPeopleNumber;

    @ApiModelProperty(value = "分享人数")
    private Long sharePeopleNumber;

    @ApiModelProperty(value = "留资人数")
    private Long lzPeopleNumber;

}
