package com.deepexi.dxp.marketing.domain.promotion.query.usercredit;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 充值赠送活动详情查询入参.
 *
 * <AUTHOR> feng<PERSON>
 * @date 2020/03/16 14:08
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionUserCreditDetailQuery extends AbstractObject {

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long id;

}