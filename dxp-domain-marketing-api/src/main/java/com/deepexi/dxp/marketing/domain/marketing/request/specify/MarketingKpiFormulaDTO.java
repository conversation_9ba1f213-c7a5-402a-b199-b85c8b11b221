package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 派生指标公式明细项表DO
 * @Author: HuangBo.
 * @Date: 2020/5/15 15:06
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingKpiFormulaDTO extends SuperDTO {

    /**
     * 派生指标ID
     */
    private Long kpiId;

    /**
     * 选择的原生指标或派生指标ID
     */
    private Long quoteKpiId;

    /**
     * 选择的原生指标或派生指标名称
     */
    private String quoteKpiName;

    /**
     * 计算公式连接符号
     */
    private String symbols;

    /**
     * 排序
     */
    private int sort;

}
