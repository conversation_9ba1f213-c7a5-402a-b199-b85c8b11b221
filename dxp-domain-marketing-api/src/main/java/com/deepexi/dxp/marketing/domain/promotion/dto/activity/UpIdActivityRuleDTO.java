package com.deepexi.dxp.marketing.domain.promotion.dto.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 带upId的活动规则
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/9/21 14:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class UpIdActivityRuleDTO extends AbstractObject {
    @ApiModelProperty("规则的条件")
    private List<BaseActivityDTO> condition;
    @ApiModelProperty("规则的结果")
    private List<UpIdDTO> operation;
    @ApiModelProperty("规则的优先级")
    private String sort;
    @ApiModelProperty("规则的类型")
    private String strategyType;
}
