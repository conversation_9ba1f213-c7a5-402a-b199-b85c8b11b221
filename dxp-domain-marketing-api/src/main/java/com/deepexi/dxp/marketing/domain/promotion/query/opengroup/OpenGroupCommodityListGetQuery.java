package com.deepexi.dxp.marketing.domain.promotion.query.opengroup;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * 当前可参与的可拼团的商品列表 条件参数实体
 *
 * <AUTHOR> xianfeng.cai
 * @date 2019/11/29 14:08
 */
@Data
@ApiModel
public class OpenGroupCommodityListGetQuery extends SuperQuery {


    /**
     * 备注
     */
    private String remark;

    /**
     * 乐观锁
     */
    private Long version;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 更新时间
     */
    private Date updatedTime;
}