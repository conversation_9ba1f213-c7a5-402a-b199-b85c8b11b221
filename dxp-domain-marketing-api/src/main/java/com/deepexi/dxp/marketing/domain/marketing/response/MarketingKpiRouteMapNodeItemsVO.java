package com.deepexi.dxp.marketing.domain.marketing.response;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 指标节点配置项VO
 * @Author: HuangBo.
 * @Date: 2020/6/18 11:40
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Api("指标路径图节点配置信息")
public class MarketingKpiRouteMapNodeItemsVO extends SuperVO {


    /**
     * 指标路径图节点ID
     */
//    private Long routeMapNodeId;

    /**
     * 指标ID
     */
    @ApiModelProperty(value = "指标ID" ,required = true)
    @NotNull(message = "指标ID不能为空")
    private Long kpiItemsId;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称" ,required = true)
    private String kpiItemsName;

}
