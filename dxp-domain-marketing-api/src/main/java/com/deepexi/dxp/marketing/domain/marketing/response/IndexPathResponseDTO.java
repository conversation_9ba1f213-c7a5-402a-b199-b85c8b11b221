package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "活动分析指标路径返回对象", description = "活动分析指标路径返回对象")
public class IndexPathResponseDTO {
    @ApiModelProperty(value = "指标路径")
    private String indexPath;

    @ApiModelProperty(value = "累计人数")
    private Long totalNumber;

    @ApiModelProperty(value = "转化率")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "今日人数")
    private Long todayNumber;

    @ApiModelProperty(value = "昨日人数")
    private Long yesterdayNumber;

    @ApiModelProperty(value = "比上一天")
    private BigDecimal beforeTodayRatio;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("指标编码")
    private String kpiCode;

    @ApiModelProperty("指标id")
    private Long kpiId;
}
