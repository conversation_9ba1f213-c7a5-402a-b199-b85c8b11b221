package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;

/**
 * 用户优惠券
 *
 * <AUTHOR> ming.zhong
 * @date created in 19:34 2019/12/2
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@ApiModel
public class CouponResponseDTO extends ActivityConfigDTO {
    /**
     * 用户优惠券code
     */
    private String code;
    @ApiModelProperty("优惠券id")
    private Long couponId;
    @ApiModelProperty("优惠券限制数量")
    private Integer couponNumber;
    @ApiModelProperty("优惠券名称")
    private String couponName;
    @ApiModelProperty("优惠券类型（0金额券|1折扣券）")
    private String couponType;
    @ApiModelProperty("使用条件")
    private BigDecimal condition;
    @ApiModelProperty("优惠值")
    private BigDecimal couponValue;

    @ApiModelProperty("领取条件")
    private String obtainCondition;
}
