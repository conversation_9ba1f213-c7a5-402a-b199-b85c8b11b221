package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: HT
 * @CreateTime: 2022/5/11
 */
@Data
@ApiModel(value = "好主播视频返回对象", description = "好主播视频返回对象")
public class GoodAnchorVideoResponseDTO implements Serializable {

    @ApiModelProperty(value = "视频id")
    private Long id;

    @ApiModelProperty(value = "视频id")
    private Long videoId;

    @ApiModelProperty(value = "视频标题")
    private String videoTitle;

    @ApiModelProperty(value = "视频地址")
    private String videoUrl;

    @ApiModelProperty(value = "视频封面地址")
    private String videoCoverUrl;

    @ApiModelProperty(value = "账号id")
    private String accountId;

    @ApiModelProperty(value = "账号名称")
    private String accountName;

    @ApiModelProperty(value = "账号头像")
    private String avatar;

    @ApiModelProperty(value = "点赞数量")
    private Integer likesNum;

    @ApiModelProperty(value = "该用户是否点赞过此作品：0否，1是")
    private Integer likesFlag = 0;

}
