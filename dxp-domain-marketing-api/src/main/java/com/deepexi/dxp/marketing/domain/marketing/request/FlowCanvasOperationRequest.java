package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Class: FlowCanvasOperationDTO
 * @Description:
 * @Author: zht
 * @Date: 2020/7/22
 */
@Data
@ApiModel
public class FlowCanvasOperationRequest extends AbstractObject {
    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true)
    private String tenantId;

    @ApiModelProperty(value = "应用Id", required = true)
    private Long appId;

    /**
     * 关联项ID
     */
    @ApiModelProperty(value = "画布实例id集合", required = true)
    private List<Long> instIdList;

    /**
     * 画布id
     */
    @ApiModelProperty(value = "画布id")
    private Long taskId;

    /**
     * 关联项名称
     */
    @ApiModelProperty(value = "画布名称")
    private String name;

    /**
     * 类型
     */
    @ApiModelProperty(value = "画布名称")
    private String type;

    /**
     * 画布json
     */
    private String contentJson;

    /**
     * 操作类型
     */
    private Integer operationType;
}
