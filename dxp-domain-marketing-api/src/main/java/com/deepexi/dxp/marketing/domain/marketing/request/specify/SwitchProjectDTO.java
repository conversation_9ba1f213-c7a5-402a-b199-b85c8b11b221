package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 立即领取请求dto
 * <AUTHOR>
 */
@Data
@ApiModel
public class SwitchProjectDTO {
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目ID")
    @NotNull(message = "请选择项目！")
    private String projectId;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @NotBlank
    private String projectName;
    /**
     * 项目城市
     */
    @ApiModelProperty(value = "项目城市")
    private String projectCity;

    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     *用户ID
     **/
    @ApiModelProperty(value = "用户Id")
    private String userId;

    @ApiModelProperty(value = "手机号")
    @NotBlank
    private String phone;
}
