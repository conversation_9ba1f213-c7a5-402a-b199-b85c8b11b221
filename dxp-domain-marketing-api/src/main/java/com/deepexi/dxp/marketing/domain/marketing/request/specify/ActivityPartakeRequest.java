package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.dxp.marketing.domain.promotion.request.calculate.ActivityOrderParamsRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date  2021/05/24 17:54
 */
@Data
public class ActivityPartakeRequest extends ActivityOrderParamsRequest {

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     * 资源ID
     */
    @ApiModelProperty("优惠券/资源ID")
    //@NotNull(message = "优惠券ID不能为空")
    private Long hisResourceId;

    /**
     * 用户昵称
     */
    @ApiModelProperty("用户昵称")
    @NotNull(message = "用户昵称不能为空")
    private String nickName;

    /**
     * 用户昵称
     */
    @ApiModelProperty("用户名")
    private String userName;

    /**
     * 用户昵称
     */
    @ApiModelProperty("手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 0、微信小程序  1、H5  2、抖音
     */
    @ApiModelProperty("0、微信小程序  1、H5  2、抖音")
    @NotNull(message = "渠道类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "unionId")
    private String unionId;


    @ApiModelProperty("wxAppId")
    private String wxAppId;

  /*  @ApiModelProperty("用户ID")
    @NotNull(message = "用户ID不能为空")
    private String userId;*/

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("领取方式:0-免费领取,1-付费领取")
    private Integer receiveMode;

    @ApiModelProperty("uuId")
    private String uuId;

    @ApiModelProperty("用户参与类型：0-发起者,1-参与者")
    private Integer userJoinType;

    @ApiModelProperty("发起人信息id（抽奖、秒杀、优惠券的用户参与记录表主键id）")
    private Long sponsorId;

    @ApiModelProperty(value = "用户表单数据")
    private Object dynamicForm;

    @ApiModelProperty(value="微信用户头像")
    private String avatar;

    @ApiModelProperty(value="创建人")
    private String createdBy;

    @ApiModelProperty(value="检验的Dto")
    private GeetestValidateDTO validateObj;
}
