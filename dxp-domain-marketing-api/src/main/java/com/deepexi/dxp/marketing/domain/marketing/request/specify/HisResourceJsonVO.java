package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 历史资源信息json字段公用类
 * <AUTHOR>
 */
@Data
public class HisResourceJsonVO extends AbstractObject {
    /**
     * 历史资源信息表主键
     */
    private Long id;
    /**
     * 资源id
     */
    private Long resourceId;
    /**
     * 资源名称
     */
    private String name;

    /**
     * 资源图片
     */
    private String url;
    /**
     * 资源发放数量
     */
    private Integer issuedQuantity;
    /**
     * 每天发放上限
     */
    private Integer issuanceCap;
    /**
     * 中奖率
     */
    private Double oddsOfWinning;

    /**
     * 限制（1限制用户中奖1次，0不限中奖次数）
     */
    private Integer limitType;

    /* ***************** 优惠券数据 start ******************* */

    @ApiModelProperty("每人限制领取/购买次数")
    private Integer limitTimes;

    @ApiModelProperty("领取方式:0-免费领取,1-付费领取")
    private Integer receiveMode;

    @ApiModelProperty("购买价格")
    private BigDecimal purchasePrice;
    /* ***************** 优惠券数据 end ******************* */

    /* ***************** 助力数据 start ******************* */
    @ApiModelProperty("助力人数/累积签到次数")
    private Integer fissonCount;

    @ApiModelProperty("助力资源类型:0-助力阶梯,1-分享获奖,2-帮忙助力获奖")
    private Integer fissonResourceType;

    @ApiModelProperty("阶梯序号")
    private Integer ladderSort;
    /* ***************** 助力数据 end ******************* */

    //话费、红包核销需要的属性
    @ApiModelProperty("项目ID")
    private String projectId;

    //话费、红包核销需要的属性
    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目类型:0-活动项目,1-系统项目")
    private Integer sysType;
    @ApiModelProperty("资源已发放数量")
    private Integer issuedUsedQuantity = 0;
}
