package com.deepexi.dxp.marketing.domain.marketing.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel(value = "核销驳回列表返回对象", description = "核销驳回列表返回对象")
public class ActivityVerifyRejectLogResponseDTO {

    @ApiModelProperty(value = "驳回备注")
    private String remark;

    @ApiModelProperty(value = "驳回人")
    private String createdBy;

    @ApiModelProperty(value = "驳回时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdTime;
}
