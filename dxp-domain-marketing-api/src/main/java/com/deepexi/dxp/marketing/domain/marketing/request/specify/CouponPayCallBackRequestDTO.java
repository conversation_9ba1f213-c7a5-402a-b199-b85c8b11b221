package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel
@Data
public class CouponPayCallBackRequestDTO extends AbstractObject implements Serializable {

    @ApiModelProperty("支付状态 SUCCESS：支付成功，PAYERROR：支付失败，CLOSED：已关闭")
    private String status;

    @ApiModelProperty("支付平台的订单号")
    private String orderNo;

    @ApiModelProperty("业务系统内部订单号")
    private String bizOrderNo;

    @ApiModelProperty("用户在直连商户appid下的唯一标识")
    private String openid;

    @ApiModelProperty("支付完成时间")
    private String successTime;

    @ApiModelProperty("订单金额，单位分")
    private Integer amount;


}
