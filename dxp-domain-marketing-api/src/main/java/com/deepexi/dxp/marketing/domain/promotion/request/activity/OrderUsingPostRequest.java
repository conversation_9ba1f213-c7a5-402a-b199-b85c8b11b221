package com.deepexi.dxp.marketing.domain.promotion.request.activity;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import java.util.List;

/**
 * 使用相关入参
 *
 * <AUTHOR> xinjian.yao
 * @date 2020/3/12 14:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class OrderUsingPostRequest extends SuperRequest {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("原来的使用")
    private List<PromotionActivityLoggerUpdatePostRequest> dtoList;


    @ApiModelProperty("预售活动支付后被回调的接口")
    @Valid
    private List<PreSalesActivityLockPostRequest> preSalesList;
}
