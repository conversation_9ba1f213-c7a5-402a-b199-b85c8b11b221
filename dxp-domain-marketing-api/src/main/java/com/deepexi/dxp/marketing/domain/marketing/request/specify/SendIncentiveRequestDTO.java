package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 发送激励请求接口（发送微信，小程序红包）
 * <AUTHOR>
 */
@Data
public class SendIncentiveRequestDTO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "活动ID")
    private Long actId;

    @ApiModelProperty(value = "资源ID")
    private Long hisResourceId;

    @ApiModelProperty(value = "资源ID")
    private String resourceName;

    @ApiModelProperty(value = "unionId")
    private String unionId;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty(value = "用户电话")
    private String phone;
    /**
     * 0、微信小程序 1、H5  2、抖音
     */
    @ApiModelProperty(value = "来源")
    private Integer type;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("订单描述")
    private String description;

    @ApiModelProperty("用户在直连商户appId下的唯一标识")
    private String openid;

    @ApiModelProperty("业务系统内部订单号")
    private String bizOrderNo;

    @ApiModelProperty("激励金额，单位分")
    private Integer amount;

    @ApiModelProperty("红包发送者名称")
    private String sendName;

    @ApiModelProperty("红包祝福语")
    private String wishing;

    @ApiModelProperty("活动名称")
    private String actName;

    @ApiModelProperty(value = "核销码")
    private String resourceCode;

    /**
     * 小程序渠道
     */
    private String mpFrom;
}
