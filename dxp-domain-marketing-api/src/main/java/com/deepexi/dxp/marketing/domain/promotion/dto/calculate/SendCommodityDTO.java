package com.deepexi.dxp.marketing.domain.promotion.dto.calculate;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * //买赠商品DTO
 *
 * <AUTHOR> xinjian.yao
 * @date 2019/11/28 11:10
 */
@Data
@ApiModel
public class SendCommodityDTO extends AbstractObject {

    @ApiModelProperty("是否可选择")
    private Boolean selectFlag;

    @ApiModelProperty("可选择数量")
    private Integer selectNumber;

    @ApiModelProperty("商品列表信息")
    private List<CommodityDTO> commodityList;

    /**
     * //赠品信息DTO
     */
    @EqualsAndHashCode(callSuper = false)
    @Data
    @ApiModel
    public static class CommodityDTO extends AbstractObject implements Serializable {
        @ApiModelProperty("商品sku")
        private String skuCode;

        @ApiModelProperty("发放总量")
        private String grantTotal;

        @ApiModelProperty("单次发放量")
        private String grantNum;

        /**
         * 2020年4月27日修改，增加回显的赠品的已赠送数量
         * （活动类型：满赠）
         */
        @ApiModelProperty("发放已赠送数量")
        private String grantUsedNum;

    }
}
