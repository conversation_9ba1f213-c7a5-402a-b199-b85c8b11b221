package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: HT
 * @CreateTime: 2022/5/13
 */
@Data
@ApiModel(value = "好主播个人信息", description = "好主播个人信息")
public class GoodAnchorInfoVO implements Serializable {

    private static final long serialVersionUID = -4806132193285344552L;

    @ApiModelProperty(value = "账号id")
    private String accountId;

    @ApiModelProperty(value = "账号名称")
    private String accountName;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "账号头像")
    private String avatar;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "userKey")
    private String userKey;

    @ApiModelProperty(value = "简介")
    private String accountInfo;

    @ApiModelProperty(value = "归属项目id")
    private String projectId;

    @ApiModelProperty(value = "归属项目名称")
    private String projectName;

    @ApiModelProperty(value = "归属案场id")
    private String saleTeamId;

    @ApiModelProperty(value = "归属案场名称")
    private String saleTeamName;

    @ApiModelProperty(value = "归属城市id")
    private String realCityId;

    @ApiModelProperty(value = "归属城市名称")
    private String realCityName;

    @ApiModelProperty(value = "主播分享海报")
    private String shareIcon;

//    @ApiModelProperty(value = "点赞数排名")
//    private Integer rank;

}
