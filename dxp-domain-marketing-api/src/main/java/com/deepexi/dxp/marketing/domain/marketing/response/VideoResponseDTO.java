package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: HT
 * @CreateTime: 2022/5/12
 */
@Data
@ApiModel(value = "好主播作品信息", description = "好主播作品信息")
public class VideoResponseDTO implements Serializable {

    private static final long serialVersionUID = -6055208870543617576L;

    @ApiModelProperty(value = "作品主键id")
    private Long id;

    @ApiModelProperty(value = "视频id")
    private Long videoId;

    @ApiModelProperty(value = "视频标题")
    private String videoTitle;

    @ApiModelProperty(value = "视频地址")
    private String videoUrl;

    @ApiModelProperty(value = "视频封面地址")
    private String videoCoverUrl;

    @ApiModelProperty(value = "点赞数量")
    private Integer likesNum;

    @ApiModelProperty(value = "该用户是否点赞过此作品：0否，1是")
    private Integer likesFlag = 0;

    @ApiModelProperty(value = "分享次数")
    private Integer shareCount;

    @ApiModelProperty(value = "观看次数")
    private Integer playCount;

    @ApiModelProperty(value = "活动信息")
    private PromotionActivityDetailDTO actDTO;

    @ApiModelProperty(value = "视频关联的主播信息")
    private GoodAnchorInfoVO goodAnchorInfoVO;

}
