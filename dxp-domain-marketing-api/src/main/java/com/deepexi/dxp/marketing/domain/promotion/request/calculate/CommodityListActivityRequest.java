package com.deepexi.dxp.marketing.domain.promotion.request.calculate;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.util.pojo.AbstractObject;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 多个商品查询活动
 *
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/12/6 10:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class CommodityListActivityRequest extends AbstractObject {

    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("用户类型")
    private String userType;
    @ApiModelProperty("用户分组id List")
    private List<Long> groupIdList;
    @ApiModelProperty("渠道")
    private String clientTenant;

    @ApiModelProperty("租户")
    private String tenantId;
    @ApiModelProperty("应用")
    private Long appId;
    @ApiModelProperty("优惠券")
    private List<Long> couponUserList;

    @ApiModelProperty("商品列表")
    private List<ActivityCommodityDTO> commodityDTO;

    @ApiModelProperty("商店code")
    private String storeCode;

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("是否被邀请 ‘true’|‘false’")
    private String isInvited;


    @ApiModelProperty("是否过滤积分兑换，拼团和领券活动")
    private Boolean type;

    /**
     * 待验证的限制列表,默认是ALL=全部校验，NONE=不校验，COMMODITY=商品，NUMBER=数量，TENANT=渠道，USER=用户，SHOP=门店
     */
    @ApiModelProperty("待验证的限制列表，默认是ALL=全部校验，NONE=不校验，COMMODITY=商品，NUMBER=数量，TENANT=渠道，USER=用户，SHOP=门店")
    private List<String> limitList = Lists.newArrayList("ALL");

}
