package com.deepexi.dxp.marketing.domain.promotion.dto.usercredit;


import com.deepexi.util.domain.dto.BaseDTO;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 查询会员店铺余额充值活动分页对象响应实体.
 *
 * <AUTHOR> fengjun
 * @date 2020/03/12 17:08
 */
@Data
@ApiModel
public class UserCreditQueryDTO {
    /**
     * 充值活动信息分页集合
     */
    @ApiModelProperty("充值活动信息分页集合")
    private PageBean<UserCreditEatchItem> userCreditEatchItemList;

    /**
     * 充值活动信息
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel
    public static class UserCreditEatchItem extends BaseDTO {

        /**
         * 备注
         */
        @ApiModelProperty("备注")
        private String remark;


        /**
         * 创建时间
         */
        @ApiModelProperty("创建时间")
        private Date createdTime;


        /**
         * 更新时间
         */
        @ApiModelProperty("更新时间")
        private Date updatedTime;
        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String activityName;

        /**
         * 模板id 活动从模板来的 所以不难为空
         */
        @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
        private Integer paTemplateId;

        /**
         * 活动id
         */
        @ApiModelProperty("活动id")
        private Long id;

        /**
         * 活动金额
         */
        @ApiModelProperty("活动金额")
        private BigDecimal money;

        /**
         * 赠送余额
         */
        @ApiModelProperty("赠送余额")
        private BigDecimal balance;

        /**
         * 赠送积分
         */
        @ApiModelProperty("赠送积分")
        private BigDecimal integral;

        /**
         * 是否可用标识
         */
        @ApiModelProperty("是否可用标识")
        private Boolean isAvailable = true;
    }
}
