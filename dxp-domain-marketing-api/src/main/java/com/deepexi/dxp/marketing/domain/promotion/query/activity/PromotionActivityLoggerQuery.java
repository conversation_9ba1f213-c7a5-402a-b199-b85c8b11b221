package com.deepexi.dxp.marketing.domain.promotion.query.activity;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;

/**
 * 促销活动日志
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/21 16:31
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionActivityLoggerQuery extends AbstractObject {
    @ApiModelProperty(value = "null")
    private Long id;

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;

    /**
     * 会员id
     */
    @ApiModelProperty(value = "会员id")
    private Long userId;

    /**
     * 订单商品内容
     */
    @ApiModelProperty(value = "订单商品内容")
    private Object orderDetail;


    /**
     * 0 锁定状态 1 支付完成状态
     */
    @ApiModelProperty(value = "0 锁定状态 1 支付完成状态")
    private Integer status;

    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    private String followNumber;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNumber;


    /**
     * 用户类型
     */
    @ApiModelProperty(value = "用户类型")
    private Integer userType;

    /**
     * 查询的页码(-1 表示查询所有数据)
     */
    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;

}