package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageShareDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeLogDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.PromotionHisResourceDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 裂变活动主页信息返回
 * <AUTHOR>
 */
@Data
@ApiModel(value = "裂变活动主页信息返回对象", description = "裂变活动主页信息返回对象")
public class FissionHomeInfoResponseDTO {

    /**
     * 活动详情
     */
    PromotionActivityResponseDTO promotionActivity;
    /**
     * 奖品列表
     */
    List<PromotionHisResourceDTO> prizeList;

    @ApiModelProperty(value = "活动页信息")
    private ActivityPageVO activityPageDTO;

    @ApiModelProperty(value = "分享页信息")
    private ActivityPageShareDTO activityPageShareDTO;

    @ApiModelProperty(value = "用户参与记录")
    private ActivityPartakeLogDTO activityPartakeLogDTO;

    @ApiModelProperty(value = "参与文案")
    private String copywriting;

    @ApiModelProperty(value = "活动状态（0:未开始，1：进行中，2砍价成功，3：已领取，4砍价失败）")
    private Integer status;

    /**
     * 是否显示切换按钮
     */
    @ApiModelProperty(value = "是否显示切换按钮")
    private Boolean isShowSwitch;
    /**
     * 是否显示砍价按钮
     */
    @ApiModelProperty(value = "是否显示砍价按钮")
    private Boolean isShowBargain;

    @ApiModelProperty(value = "好友帮助记录")
    private List<ActivityFissionLogResponseDTO> activityFissionLogResponseDTOList;

    @ApiModelProperty(value = "参与者信息")
    private ActivityFissionLogResponseDTO fissionLogResponseDTO;

    @ApiModelProperty(value = "领取奖品方式:0-系统,1-手动")
    private Integer receiveMode;


    @ApiModelProperty(value = "助力可领取奖品")
    private List<ActivityFissionAssistResourceResponseDTO> activityFissionAssistResourceResponseDTOList;

    @ApiModelProperty(value = "阶梯总数")
    private Integer ladderSortTotal;

    @ApiModelProperty(value = "已完成阶梯数")
    private Integer ladderSortFinish;

    @ApiModelProperty(value = "当前阶梯还剩下多少人可以完成")
    private Integer currentLadderSurplusNumber;

    @ApiModelProperty("用户参与类型：0-发起者,1-参与者")
    private Integer userJoinType;

    @ApiModelProperty("奖品是否已领取：0-未领取，1-已领取")
    private Integer isReceiveFinish;

    @ApiModelProperty(value = "参与者已发起正在进行中的活动")
    private ActivityPartakeLogDTO myActivityPartakeLogDTO;
}
