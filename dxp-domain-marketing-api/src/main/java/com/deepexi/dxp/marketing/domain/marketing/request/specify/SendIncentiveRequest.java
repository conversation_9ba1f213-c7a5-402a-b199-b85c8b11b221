package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发送激励请求接口
 * <AUTHOR>
 */
@Data
public class SendIncentiveRequest {

    @ApiModelProperty("公众号id")
    private String appId;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("订单描述")
    private String description;

    @ApiModelProperty("用户在直连商户appId下的唯一标识")
    private String openid;

    @ApiModelProperty("业务系统内部订单号")
    private String bizOrderNo;

    @ApiModelProperty("激励金额，单位分")
    private Integer amount;

    @ApiModelProperty("红包发送者名称")
    private String sendName;

    @ApiModelProperty("红包祝福语")
    private String wishing;

    @ApiModelProperty("活动名称")
    private String actName;
}
