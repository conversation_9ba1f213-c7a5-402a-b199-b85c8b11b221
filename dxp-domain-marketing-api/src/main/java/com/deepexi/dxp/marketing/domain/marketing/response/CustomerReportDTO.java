package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/9/21 15:37
 */
@Data
public class CustomerReportDTO {

    @ApiModelProperty(value = "报备ID")
    private String reportId;
    /**
     * 经纪人姓名
     */
    @ApiModelProperty(value = "经纪人姓名")
    private String agentName;
    /**
     * 置业顾问名
     */
    @ApiModelProperty(value = "置业顾问名")
    private String saleMainName;
    /**
     * 报备时填写的推荐经纪人姓名
     */
    @ApiModelProperty(value = "报备时填写的推荐经纪人姓名")
    private String reportAgentName;
    /**
     * 经纪人类型
     */
    @ApiModelProperty(value = "经纪人类型")
    private String agentTypeNo;
    /**
     * 经纪人电话
     */
    @ApiModelProperty(value = "经纪人电话")
    private String agentMobile;
    /**
     * 经纪人编码
     */
    @ApiModelProperty(value = "经纪人编码")
    private String agentId;
    /**
     * 置业顾问Id (接待顾问 接待hi也顾问)
     */
    @ApiModelProperty(value = "置业顾问Id")
    private String saleMainId;
    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名")
    private String cusName;
    /**
     * 客户电话
     */
    @ApiModelProperty(value = "客户电话")
    private String cusTel;

    @ApiModelProperty(value = "案场id")
    private String saleTeamId;

    @ApiModelProperty(value = "报备状态")
    private String reportStatus;

    @ApiModelProperty(value = "到访有效期截止日期")
    private Date visitExtendDate;
    /**
     * 成交有效期截止日期
     */
    @ApiModelProperty(value = "成交有效期截止日期")
    private Date dealExtendDate;
    @ApiModelProperty(value = "报备日期")
    private Date reportDate;
}
