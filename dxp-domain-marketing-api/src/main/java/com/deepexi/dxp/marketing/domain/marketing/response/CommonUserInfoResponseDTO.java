package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@ApiModel(value = "用户登录信息", description = "用户登录信息对象")
public class CommonUserInfoResponseDTO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "状态码")
    private String code;

    @ApiModelProperty(value = "描述")
    private String msg;

    @ApiModelProperty(value = "用户信息")
    private CommonUserInfoDTO data;

    @Data
    @EqualsAndHashCode(callSuper = false)
    public static  class CommonUserInfoDTO extends AbstractObject implements Serializable {

        @ApiModelProperty(value = "用户账号")
        private String  userId;

        @ApiModelProperty(value = "用户姓名")
        private String  userName;

        @ApiModelProperty(value = "员工编号")
        private String employeeNum;

        @ApiModelProperty(value = "有效从")
        private String dateFrom;

        @ApiModelProperty(value = "有效至")
        private String dateTo;

        @ApiModelProperty(value = "是否锁定,0=正常，1=锁定")
        private Integer locked;

        @ApiModelProperty(value = "是否启用移动端，0=启用，1=不启用")
        private Integer mobile;

        @ApiModelProperty(value = "是否启用，0=启用，1=不启用")
        private Integer valid;

        @ApiModelProperty(value = "员工状态 在职：1，离职：2")
        private Integer empStatus;

        @ApiModelProperty(value = "离职日期")
        private String  outdutyDate;

        @ApiModelProperty(value = "电话号码")
        private String  telephone;

        @ApiModelProperty(value = "邮箱")
        private String  email;

        @ApiModelProperty(value = "最近登录时间")
        private String  lastLoginDate;

        @ApiModelProperty(value = "用户别名")
        private String  userAlias;

        @ApiModelProperty(value = "营销系统用户是否属于本部")
        private Integer gfBase;

        @ApiModelProperty(value = "账号所属组织名称")
        private String smartOrgNameFullPath;

        @ApiModelProperty(value = "账号所属组织编码")
        private String  smartOrgNoFullPath;

        @ApiModelProperty(value = "城市编码")
        private String  cityCode;

        @ApiModelProperty(value = "城市名称")
        private String  cityName;

        @ApiModelProperty(value = "区域编码")
        private String  areaCode;

        @ApiModelProperty(value = "区域名称")
        private String  areaName;
    }
}
