package com.deepexi.dxp.marketing.domain.marketing.response;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.PromotionHisResourceDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 活动分组返回
 * <AUTHOR>
 */
@ApiModel(value = "活动分组返回参数", description = "活动分组返回参数")
@Data
public class ActivityGroupRelatedResponseDTO extends SuperVO {
    /**
     * 活动Id+type
     */
    @ApiModelProperty("活动Id+type")
    private String tId;
    /**
     * 活动Id
     */
    @ApiModelProperty("活动Id")
    private Long activityId;
    /**
     * 活动专题名称
     */
    @ApiModelProperty("活动名称")
    private String name;

    /**
     * 模板 ID
     */
    @ApiModelProperty("模板 ID")
    private Integer paTemplateId;

    /**
     * 模板 ID
     */
    @ApiModelProperty("活动模板")
    private String templateName;
    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private Integer status;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private String startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private String endTime;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 图片地址
     */
    @ApiModelProperty("图片地址")
    private String imageUrl;

    @ApiModelProperty(value = "是否热门（0否，1是）")
    private Integer isPopular;

    /**
     * 0是活动组，1是活动
     */
    @ApiModelProperty("0是活动组，1是活动")
    private Integer type;


    /**
     * 活动组或者活动ID
     */
    @ApiModelProperty("活动组或者活动ID")
    private Long relatedId;

    /**
     * 奖品列表
     */
    @ApiModelProperty("奖品列表（秒杀活动，领券活动展示）")
    List<PromotionHisResourceDTO> prizeList;
}
