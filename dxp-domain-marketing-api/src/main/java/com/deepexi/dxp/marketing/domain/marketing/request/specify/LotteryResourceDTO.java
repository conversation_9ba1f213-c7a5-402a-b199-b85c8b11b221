package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import com.baomidou.mybatisplus.annotation.TableField;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 中奖信息相应dto
 * <AUTHOR>
 */
@Data
@ApiModel
public class LotteryResourceDTO extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 原资源id
     */
    @ApiModelProperty(value = "原资源id")
    private Long resourceId;
    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;

    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String name;
    /**
     * 卡劵类型 0、代金劵 1、折扣劵
     */
    @ApiModelProperty(value = "卡劵类型 0、代金劵 1、折扣劵")
    private Integer couponType;


    /**
     * 资源类型 0、卡劵 1、第三方接入
     */
    @TableField(value = "type")
    private Integer type;
    /**
     * 细分类别 0、普通劵 1、房源劵 2、商品劵
     */
    @TableField(value = "coupon_category")
    private Integer couponCategory;
    /**
     * 细分类别 0、手机话费 1、现金红包
     */
    @TableField(value = "third_category")
    private Integer thirdCategory;

    /**
     * 礼品券发放方式:0-线上邮寄,1-线下核销
     */
    @ApiModelProperty(value = "礼品券发放方式:0-线上邮寄,1-线下核销")
    private Integer grantWay;

    /**
     * 卡劵面值
     */
    @ApiModelProperty(value = "卡劵面值")
    private BigDecimal couponValue;
    /**
     * 资源图片
     */
    @ApiModelProperty(value = "资源图片")
    private String url;

    @ApiModelProperty(value = "阶梯排序号")
    private Integer ladderSort;

    @ApiModelProperty(value = "中奖id")
    private Long winLotteryId;
}
