package com.deepexi.dxp.marketing.domain.marketing.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 签到结果
 */
@Data
@ApiModel
@Accessors(chain = true)
public class SignResponseDTO implements Serializable {

    @ApiModelProperty(value = "0：普通签到，1：中奖")
    private int type = 0;

    @ApiModelProperty(value = "资源id，-1，则为没有中奖,大于0的说明是中奖品，领取奖品传入")
    private Long resourceId;

    @ApiModelProperty(value = "资源名称")
    private String name;

    @ApiModelProperty(value = "资源图片")
    private String url;

}
