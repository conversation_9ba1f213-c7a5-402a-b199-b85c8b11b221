package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/12/27
 */
@Data
public class AssistResultDTO {
    @ApiModelProperty("助力状态，1助力成功，2重复助力，3已经助力过其他人")
    private Integer status;

    @ApiModelProperty("用户昵称")
    private String nickName;

    @ApiModelProperty(value = "unionId")
    private String unionId;

    @ApiModelProperty(value="微信用户头像")
    private String avatar;
}
