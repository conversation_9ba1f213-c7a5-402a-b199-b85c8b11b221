package com.deepexi.dxp.marketing.domain.promotion.dto.coupon;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.CouponResponseDTO;
import com.deepexi.util.domain.dto.BaseExtDTO;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 用户优惠券表记录
 *
 * <AUTHOR> ming.zhong
 * @date created in 14:36 2019/12/2
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel
public class PromotionCouponLoggerListPostResponseDTO extends BaseExtDTO {
    /**
     * 用户优惠券code
     */
    private String code;
    @ApiModelProperty("优惠券 id")
    private Long couponId;
    /**
     * 优惠券起始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;
    /**
     * 优惠券结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;
    @ApiModelProperty("来源 id")
    private Long releaseId;
    @ApiModelProperty(value = "release_time")
    private Integer releaseType;
    @ApiModelProperty(value = "amount")
    private Integer amount = 1;

    @ApiModelProperty("订单 id")
    private String orderId;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("使用状态")
    private Integer status;

    @ApiModelProperty(value = "用户类型")
    private Integer userType;
    /**
     * 租户隔离
     */
    @ApiModelProperty(value = "租户隔离")
    private String tenantId;

    /**
     * 应用隔离
     */
    @ApiModelProperty(value = "应用隔离")
    private Long appId;
    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;

    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标记
     */
    private Integer deleted = 0;

    @ApiModelProperty("优惠券信息")
    private CouponResponseDTO couponDto;


    @ApiModelProperty(value = "领券时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date receiveTime;

    @ApiModelProperty(value = "核销时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date usedTime;

}
