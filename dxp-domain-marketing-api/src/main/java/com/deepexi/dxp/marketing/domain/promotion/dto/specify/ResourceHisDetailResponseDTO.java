package com.deepexi.dxp.marketing.domain.promotion.dto.specify;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.HisResourceJsonVO;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 资源信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ResourceHisDetailResponseDTO extends AbstractObject implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty(value="主键id")
    private Long id;
    /**
     * 原资源id
     */
    @ApiModelProperty(value="原资源id")
    private Long resourceId;
    /**
     * 活动id
     */
    @ApiModelProperty(value="活动id")
    private Long activityId;
    /**
     * 资源类型 0、卡劵 1、第三方接入
     */
    @ApiModelProperty(value="资源类型: 0、卡劵 1、第三方接入")
    private Integer type;
    /**
     * 细分类别 0、普通劵 1、房源劵 2、商品劵
     */
    @ApiModelProperty(value="细分类别: 0-普通劵, 1-房源劵, 2-商品劵")
    private Integer couponCategory;
    /**
     * 细分类别 0、手机话费 1、现金红包
     */
    @ApiModelProperty(value="第三方细分类别 0、手机话费 1、现金红包")
    private Integer thirdCategory;
    /**
     * 资源名称
     */
    @ApiModelProperty(value="资源名称")
    private String name;
    /**
     * 卡劵类型 0、代金劵 1、折扣劵
     */
    @ApiModelProperty(value="卡劵类型 0、代金劵 1、折扣劵")
    private Integer couponType;
    /**
     * 卡劵面值
     */
    @ApiModelProperty(value="卡劵面值")
    private BigDecimal couponValue;
    /**
     * 资源图片
     */
    @ApiModelProperty(value="资源图片")
    private String url;
    /**
     * 使用时间类型 0 、不限制 1、指定时间 2、有效天数
     */
    @ApiModelProperty(value="使用时间类型 0 、不限制 1、指定时间 2、有效天数")
    private Integer validTimeType;

    @ApiModelProperty(value="礼品券发放方式:0-线上邮寄,1-线下核销")
    private Integer grantWay;
    /**
     * 有效期开始时间
     */

    @ApiModelProperty(value="有效期开始时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date validStartTime;


    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value="有效期结束时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date validEndTime;
    /**
     * 有效天数
     */
    @ApiModelProperty(value="有效天数")
    private Integer validDay;
    /**
     * 使用规则
     */
    @ApiModelProperty(value="使用规则")
    private String useRule;
    /**
     * 房源名称
     */
    @ApiModelProperty(value="房源名称")
    private String houseName;
    /**
     * 房源面积
     */
    @ApiModelProperty(value="房源面积")
    private String houseVolume;
    /**
     * 房源信息
     */
    @ApiModelProperty(value="房源信息")
    private String houseMessage;
    /**
     * 原价
     */
    @ApiModelProperty(value="原价")
    private String costPrice;
    /**
     * 折扣价
     */
    @ApiModelProperty(value="折扣价")
    private String discountPrice;
    /**
     * 商品名称
     */
    @ApiModelProperty(value="商品名称")
    private String itemName;
    /**
     * 优惠卷code
     */
    @ApiModelProperty(value="优惠卷code")
    private String code;


    /**
     * 应用隔离
     */
    @ApiModelProperty(value="应用隔离")
    private Long appId;

    @ApiModelProperty(value = "租户id")
    private String tenantId;


    /**
     * 每人限制领取/购买次数
     */
    @ApiModelProperty(value = "每人限制领取/购买次数")
    private Integer limitTimes;

    /**
     * 领取方式:0-免费领取,1-付费领取
     */
    @ApiModelProperty(value = "领取方式:0-免费领取,1-付费领取")
    private Integer receiveMode;

    /**
     * 购买价格
     */
    @ApiModelProperty(value = "购买价格")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "资源发放数量")
    private Integer issuedQuantity;

    @ApiModelProperty(value = "资源剩余数量")
    private Integer remainingQuantity;

    @ApiModelProperty(value = "扩展字段")
    private HisResourceJsonVO ext;

    /**
     * 每日/每人发放上限
     */
    @ApiModelProperty(value = "每日/每人发放上限")
    private Integer issuanceCap;

    /**
     * 中奖率
     */
    @ApiModelProperty(value = "中奖率")
    private Double oddsOfWinning;

    /**
     * 限制（1限制用户中奖1次，0不限中奖次数）
     */
    @ApiModelProperty(value = "限制（1限制用户中奖1次，0不限中奖次数）")
    private Integer limitType;

    /**
     * 助力人数
     */
    @ApiModelProperty(value = "助力人数")
    private Integer fissonCount;

    /**
     * 助力资源类型:0-助力阶梯,1-分享获奖,2-帮忙助力获奖
     */
    @ApiModelProperty(value = "助力资源类型:0-助力阶梯,1-分享获奖,2-帮忙助力获奖")
    private Integer fissonResourceType;

    @ApiModelProperty(value = "阶梯排序号")
    private Integer ladderSort;

    @ApiModelProperty(value = "projectId")
    private String projectId;

    @ApiModelProperty(value = "projectName")
    private String projectName;


    @ApiModelProperty(value = "项目类型:0-活动项目,1-系统项目")
    private Integer sysType;
    /*@ApiModelProperty(value="扩展字段")
    private ActivityParticipationExtResponseDTO ext;

    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class ActivityParticipationExtResponseDTO extends AbstractObject implements Serializable {
        @ApiModelProperty(value="资源id")
        private Long resourceId;

        @ApiModelProperty("资源发放数量")
        private Integer resourceIssuedNumber;

        @ApiModelProperty("每人限制领取/购买次数")
        private Integer limitTimes;

        @ApiModelProperty("领取方式:0-免费领取,1-付费领取")
        private Integer receiveMode;

        @ApiModelProperty("购买价格")
        private BigDecimal purchasePrice;
    }*/
}
