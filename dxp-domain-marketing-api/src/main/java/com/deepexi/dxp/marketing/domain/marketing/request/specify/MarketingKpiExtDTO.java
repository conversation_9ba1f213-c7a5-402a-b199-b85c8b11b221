package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 原生指标和派生指标DTO
 * @Author: HuangBo.
 * @Date: 2020/5/23 17:42
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingKpiExtDTO extends MarketingKpiDTO {

    /**
     * 活动目标类型(1:营收；2:商品；3:用户)
     */
    private Integer purposeType;

    /**
     * 是否是核心指标
     */
    private boolean coreKpi;

}
