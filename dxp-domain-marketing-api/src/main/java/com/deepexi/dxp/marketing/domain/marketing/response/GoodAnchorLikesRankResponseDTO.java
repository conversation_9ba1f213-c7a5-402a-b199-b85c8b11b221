package com.deepexi.dxp.marketing.domain.marketing.response;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @Author: HT
 * @CreateTime: 2022/5/11
 */
@Data
@ApiModel(value = "好主播点赞排行榜返回对象", description = "好主播点赞排行榜返回对象")
public class GoodAnchorLikesRankResponseDTO implements Serializable {

    private static final long serialVersionUID = -8419880998413953314L;

    @ApiModelProperty(value = "城市维度点赞排名")
    private List<CityRankDTO> cityRankDTOList = Lists.newArrayList();

    @ApiModelProperty(value = "项目维度点赞排名")
    private List<ProjectRankDTO> projectRankDTOList = Lists.newArrayList();
}
