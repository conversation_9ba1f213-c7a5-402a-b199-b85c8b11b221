package com.deepexi.dxp.marketing.domain.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "活动分析城市分布返回对象", description = "活动分析城市分布返回对象")
public class CityDistributionResponseDTO {

    @ApiModelProperty(value = "排名")
    private Integer rank;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "参与人数")
    private Long joinPeopleNumber;

    @ApiModelProperty(value = "占比")
    private BigDecimal percentage;
}
