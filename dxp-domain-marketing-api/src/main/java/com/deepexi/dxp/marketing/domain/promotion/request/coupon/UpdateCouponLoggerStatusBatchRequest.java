package com.deepexi.dxp.marketing.domain.promotion.request.coupon;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-07-08 19:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateCouponLoggerStatusBatchRequest extends SuperRequest {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户优惠券id列表")
    @NotEmpty(message = "id列表不能为空")
    private List<Long> idList;

    @ApiModelProperty("修改为的状态")
    @NotNull(message = "修改为的状态")
    private Integer toStatus;

}
