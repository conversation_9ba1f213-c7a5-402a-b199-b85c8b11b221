package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "退款请求参数", description = "退款请求参数")
@Data
public class WxRefundsRequestDTO {


    @ApiModelProperty(value = "支付平台唯一订单号")
    private String orderNo;

    @ApiModelProperty(value = "退款原因")
    private String reason;

    @ApiModelProperty(value = "退款通知回调地址")
    private String bizNotifyUrl;
}
