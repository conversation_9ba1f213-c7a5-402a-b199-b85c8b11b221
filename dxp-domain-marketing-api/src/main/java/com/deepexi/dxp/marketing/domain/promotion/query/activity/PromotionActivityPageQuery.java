package com.deepexi.dxp.marketing.domain.promotion.query.activity;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 活动分页查询Query
 *
 * <AUTHOR> lwb
 * @date 2022/04/21
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class PromotionActivityPageQuery implements Serializable {

    @ApiModelProperty(value = "主键ID集合")
    private List<Long> idList;

    @ApiModelProperty(value = "搜索Key")
    private String searchKey;

    @ApiModelProperty("活动类型集合")
    private List<Integer> paTemplateIdList;

    @ApiModelProperty(value = "活动状态")
    private List<Integer> statusList;

    @ApiModelProperty(value="上下架状态:0-下架,1-上架")
    private Integer upperStatus;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;

    @ApiModelProperty(value = "区域id",hidden = true)
    private List<String> areaIdList;
    @ApiModelProperty(value = "城市公司id",hidden = true)
    private List<String> cityIdList;
    @ApiModelProperty(value = "项目id",hidden = true)
    private List<String> projectIdList;
    @ApiModelProperty(value="审核状态（0-未审核，1-审核通过，2-审核不通过）")
    private Integer auditStatus;
    @ApiModelProperty(value="业务条线（1-营销，2-客关）")
    private Integer lineType;
    @ApiModelProperty(value="投放渠道")
    private String deliveryChannel;
}