package com.deepexi.dxp.marketing.domain.marketing.request.specify;

import lombok.Data;

import java.io.Serializable;

/**
 * 指标运算DTO
 *
 * <AUTHOR>
 * @since 2020年05月23日 11:55
 */
@Data
public class KpiOperationDTO implements Serializable {

    /**
     * KPI指标ID(marketing_kpi表ID)
     */
    private Long id;

    /**
     * 指标类型(1:原生指标; 2:派生指标)
     */
    private Integer type;

    /**
     * 编码
     */
    private String code;

    /**
     * 计算公式连接符号
     */
    private String symbols;
}
