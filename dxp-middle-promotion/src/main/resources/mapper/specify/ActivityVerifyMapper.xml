<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.ActivityVerifyMapper">

    <update id="updateGiftVoucher">
        update promotion_activity_verify
        set is_deleted = 1
        <where>
            id in
            <foreach collection="verifyIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>

    <select id="getAnalysisResourceInfo"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityTopicAnalysisResourceResponseDTO">
        select b.*,pr.`type` ,pr.name,pr.url,pr.coupon_category,pr.third_category ,pr.coupon_type ,pr.coupon_value ,pr.discount_price,pr.house_name,pr.cost_price
        from (
            select av.resource_id,count(DISTINCT av.user_id) as gotCount,
                   count(DISTINCT case when verify_status = 1 then av.user_id else null end) as writeOffPeopleNumber
              from (
                     select agr.related_activity_id as activity_id from promotion_activity_group_related agr
                     left join promotion_activity_group ag on ag.id = agr.related_group_id
                     where group_id = #{query.id,jdbcType=BIGINT} and agr.related_activity_id !=0
                         UNION ALL
                     select agr2.related_activity_id as activity_id from promotion_activity_group_related agr
                     left join promotion_activity_group ag on ag.id = agr.related_group_id
                     left join promotion_activity_group_related agr2 on agr2.group_id = agr.related_group_id
                     where agr.group_id = #{query.id,jdbcType=BIGINT} and agr.related_group_id !=0

                 ) a join promotion_activity pa on a.activity_id = pa.id and pa.is_deleted = 0
                     join promotion_activity_verify av ON av.activity_id = pa.id and av.is_deleted = 0
            GROUP BY av.resource_id
        ) b join promotion_his_resource pr on b.resource_id = pr.id

    </select>
    <select id="findPage" resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyInResponseDTO"
            parameterType="com.deepexi.dxp.marketing.domain.marketing.query.specify.VerifyPageQuery">
             SELECT a.*,o.wx_order_no,o.pay_money from promotion_activity_verify as a
             join promotion_activity pa on a.activity_id = pa.id
            LEFT JOIN promotion_activity_order o ON a.order_id=o.id
            where  a.is_deleted = 0
        <if test="query.likeParams != null and  query.likeParams !='' ">
            AND (a.nick_name like CONCAT('%', #{query.likeParams}, '%') or a.phone like CONCAT('%', #{query.likeParams}, '%') or a.name like CONCAT('%', #{query.likeParams}, '%') or
    <!--                  a.code like CONCAT('%', #{query.likeParams}, '%') or a.verify_by like CONCAT('%', #{query.likeParams}, '%') or a.refund_by like CONCAT('%', #{query.likeParams}, '%')-->
                 pa.name like CONCAT('%', #{query.likeParams}, '%')
                 )
            </if>
            <if test="query.projectNameList != null and query.projectNameList.size() >0">
                and a.project_name in
                <foreach item="item" index="index" collection="query.projectNameList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.projectName != null and  query.projectName !='' ">
                AND a.project_name like CONCAT('%', #{query.projectName}, '%')
            </if>
            <if test="query.userId != null and  query.userId !='' ">
                AND a.user_id = #{query.userId}
            </if>
            <if test="query.phone != null and  query.phone !='' ">
                AND a.phone = #{query.phone}
            </if>
            <if test="query.isPay != null">
                AND a.is_pay = #{query.isPay}
            </if>
            <if test="query.verifyStatus != null">
                AND a.verify_status = #{query.verifyStatus}
            </if>
            <if test="query.refundStatus != null">
                AND a.refund_status = #{query.refundStatus}
            </if>
            <if test="query.verifyEndTime != null">
                and DATE_FORMAT(a.verify_time,'%Y-%m-%d') &lt;= #{query.verifyEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="query.verifyStartTime != null">
                and DATE_FORMAT(a.verify_time,'%Y-%m-%d') >= #{query.verifyStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="query.refundEndTime != null">
                and DATE_FORMAT(a.refund_time,'%Y-%m-%d') &lt;= #{query.refundEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="query.refundStartTime != null">
                and DATE_FORMAT(a.refund_time,'%Y-%m-%d') >= #{query.refundStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="query.verifyType != null">
                and a.verify_type = #{query.verifyType}
            </if>
            <if test="query.id != null">
                and a.id = #{query.id}
            </if>
            <if test="query.invalidType != null and query.invalidType == 1">
                and (a.refund_status = 1 or a.verify_status in (2,3))
            </if>
            <if test="query.verifyTypeLists != null">
                and a.verify_type in
                <foreach item="item" index="index" collection="query.verifyTypeLists"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="query.verifyBy != null and  query.verifyBy !='' ">
                AND a.verify_by = #{query.verifyBy}
            </if>
            <if test="query.activityIdList != null and query.activityIdList.size() >0">
                and a.activity_id in
                <foreach item="item" index="index" collection="query.activityIdList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="query.verifyStatus != null and query.verifyStatus == 1">
                    order by a.verify_time desc
                </when>
                <otherwise>
                    order by a.created_time desc
                </otherwise>
            </choose>
    </select>

    <select id="getExcelData" resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyInResponseDTO"
            parameterType="com.deepexi.dxp.marketing.domain.marketing.query.specify.VerifyPageQuery">
        SELECT a.*,pa.name as activity_name,o.wx_order_no,o.pay_money from promotion_activity_verify as a
        join promotion_activity pa on a.activity_id = pa.id
        LEFT JOIN promotion_activity_order o ON a.order_id=o.id
        where  a.is_deleted = 0
        <if test="query.likeParams != null and  query.likeParams !='' ">
            AND (a.nick_name like CONCAT('%', #{query.likeParams}, '%') or a.phone like CONCAT('%', #{query.likeParams}, '%') or a.name like CONCAT('%', #{query.likeParams}, '%') or
            <!--                  a.code like CONCAT('%', #{query.likeParams}, '%') or a.verify_by like CONCAT('%', #{query.likeParams}, '%') or a.refund_by like CONCAT('%', #{query.likeParams}, '%')-->
            pa.name like CONCAT('%', #{query.likeParams}, '%')
            )
        </if>
        <if test="query.projectNameList != null and query.projectNameList.size() >0">
            and a.project_name in
            <foreach item="item" index="index" collection="query.projectNameList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.projectName != null and  query.projectName !='' ">
            AND a.project_name like CONCAT('%', #{query.projectName}, '%')
        </if>
        <if test="query.userId != null and  query.userId !='' ">
            AND a.user_id = #{query.userId}
        </if>
        <if test="query.phone != null and  query.phone !='' ">
            AND a.phone = #{query.phone}
        </if>
        <if test="query.isPay != null">
            AND a.is_pay = #{query.isPay}
        </if>
        <if test="query.verifyStatus != null">
            AND a.verify_status = #{query.verifyStatus}
        </if>
        <if test="query.refundStatus != null">
            AND a.refund_status = #{query.refundStatus}
        </if>
        <if test="query.verifyEndTime != null">
            and DATE_FORMAT(a.verify_time,'%Y-%m-%d') &lt;= #{query.verifyEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="query.verifyStartTime != null">
            and DATE_FORMAT(a.verify_time,'%Y-%m-%d') >= #{query.verifyStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="query.refundEndTime != null">
            and DATE_FORMAT(a.refund_time,'%Y-%m-%d') &lt;= #{query.refundEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="query.refundStartTime != null">
            and DATE_FORMAT(a.refund_time,'%Y-%m-%d') >= #{query.refundStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="query.verifyType != null">
            and a.verify_type = #{query.verifyType}
        </if>
        <if test="query.id != null">
            and a.id = #{query.id}
        </if>
        <if test="query.invalidType != null and query.invalidType == 1">
            and (a.refund_status = 1 or a.verify_status in (2,3))
        </if>
        <if test="query.verifyTypeLists != null">
            and a.verify_type in
            <foreach item="item" index="index" collection="query.verifyTypeLists"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="query.verifyBy != null and  query.verifyBy !='' ">
            AND a.verify_by = #{query.verifyBy}
        </if>
        <if test="query.activityIdList != null and query.activityIdList.size() >0">
            and a.activity_id in
            <foreach item="item" index="index" collection="query.activityIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="query.verifyStatus != null and query.verifyStatus == 1">
                order by a.verify_time desc
            </when>
            <otherwise>
                order by a.created_time desc
            </otherwise>
        </choose>
    </select>

        <select id="findCountByActivityIdUserId"
                resultType="com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityVerifyCountDTO">
            select resource_id as resourceId,
                   count(1) as dayCount
            from promotion_activity_partake_log
            where activity_id = #{activityId}
            and created_time &lt; DATE_SUB(CURDATE(), INTERVAL -1 DAY)
            and created_time &gt;= CURDATE()
            group by resource_id
        </select>

        <!--  驳回核销 -->
    <update id="updateRejectVerify">
        UPDATE  promotion_activity_verify as a
        set a.verify_by=null ,a.verify_time=null,a.verify_status=#{verifyStatus},
        a.project_name=#{projectName},
        a.project_id=#{projectId},
        a.updated_time=NOW(),
        a.updated_by=#{updatedBy}
        where a.id=#{id}
    </update>

    <!--  设置过期 -->
    <update id="updateOver">
        UPDATE  promotion_activity_verify as a
        set a.verify_status=#{verifyStatus},
        a.updated_time=NOW()
        where a.id=#{id}
    </update>
<!-- 过期核销记录 -->
    <select id="findOverList" resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyInResponseDTO">
        SELECT a.* from promotion_activity_verify as a where 1=1 and ((a.valid_time_type=1 and  a.valid_end_time &lt;= NOW())
        or (a.valid_time_type = 2 and DATE_ADD(a.pay_time,INTERVAL a.valid_day day) &lt;= NOW() ))
        and a.verify_status not in (1,2,3)
    </select>

    <select id="findUnGiftVouchers"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyInResponseDTO">
        select *
        from promotion_activity_verify av
        join promotion_his_resource pr on av.resource_id = pr.id and pr.is_deleted = 0 and pr.coupon_category = 2
        where  av.activity_id = #{activityId} and av.phone = #{phone} and av.verify_status = 0 and av.is_deleted = 0
    </select>
    <select id="getOrderList"
            resultType="com.deepexi.dxp.marketing.domain.promotion.dto.specify.VerifyOrderResponseDTO">
        SELECT
        o.code,
        v.code verifyCode,
        o.created_by nick_name,
        o.phone,
        a.name,
        o.status,
        o.pay_time,
        JSON_EXTRACT(o.`ext`, '$.total') total,
        o.pay_money,
        v.project_name,
        v.verify_status,
        v.verify_by,
        v.verify_time,
        o.created_time,
        JSON_EXTRACT(a.`ext`, '$.depositPaid') depositPaid
        FROM promotion_activity_order o
        LEFT JOIN promotion_activity_verify v ON v.order_id = o.id
        JOIN promotion_activity a ON a.id = o.activity_id
        WHERE o.order_type = 0
        <if test="dto.verifyStatus != null">
            AND v.verify_status = #{dto.verifyStatus}
        </if>
        <if test="dto.status != null">
            AND o.status = #{dto.status}
        </if>
        <if test="dto.startTime != null and dto.endTime != null">
            AND v.verify_time BETWEEN #{dto.startTime} AND #{dto.endTime}
        </if>
        <if test="dto.keyword != null and dto.keyword != ''">
            AND (v.nick_name LIKE CONCAT('%', #{dto.keyword}, '%')
            OR v.phone LIKE CONCAT('%', #{dto.keyword}, '%')
            OR a.name LIKE CONCAT('%', #{dto.keyword}, '%')
            OR o.code LIKE CONCAT('%', #{dto.keyword}, '%')
                )
        </if>
        <if test="dto.deliveryChannel != null and dto.deliveryChannel != ''">
            AND JSON_EXTRACT(a.`ext`, '$.deliveryChannel') = #{dto.deliveryChannel}
        </if>
        <if test="dto.projectId != null and dto.projectId != ''">
            AND v.project_id = #{dto.projectId}
        </if>
        <if test="dto.depositPaid != null">
            AND ifnull(JSON_EXTRACT(a.`ext`, '$.depositPaid'),0) = #{dto.depositPaid}
        </if>
        <if test="dto.activityIdList != null and dto.activityIdList.size() > 0">
            and o.activity_id in
            <foreach item="item" collection="dto.activityIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by o.id desc
    </select>
</mapper>