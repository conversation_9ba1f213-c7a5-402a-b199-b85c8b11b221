<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.PromotionActivityMapper">
    <resultMap id="BaseResultMap" type="com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO">
        <!--@mbg.generated-->
        <!--@Table promotion_activity-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="PA_template_id" jdbcType="INTEGER" property="paTemplateId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="upper_status" jdbcType="INTEGER" property="upperStatus"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="created_person" jdbcType="VARCHAR" property="createdPerson"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="app_id" jdbcType="BIGINT" property="appId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="coupons_flag" jdbcType="BOOLEAN" property="couponsFlag"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="return_money_type" jdbcType="INTEGER" property="returnMoneyType"/>
        <result column="ext" jdbcType="VARCHAR" property="ext" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>
    <resultMap id="ActivityCouponResultMap" type="com.deepexi.dxp.middle.promotion.domain.dto.ActivityCouponAggDTO">
        <!--@mbg.generated-->
        <!--@Table promotion_activity-->
        <id column="id" jdbcType="BIGINT" property="activityId"/>
        <result column="PA_template_id" jdbcType="INTEGER" property="paTemplateId"/>
        <result column="name" jdbcType="VARCHAR" property="activityName"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="upper_status" jdbcType="INTEGER" property="upperStatus"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="created_person" jdbcType="VARCHAR" property="createdPerson"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time"  property="endTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="app_id" jdbcType="BIGINT" property="appId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="strategy_id" jdbcType="BIGINT" property="strategyId"/>
        <result column="rules" jdbcType="VARCHAR" property="rules"/>
        <result column="limit_id" jdbcType="BIGINT" property="limitId"/>
        <result column="limit_type" jdbcType="INTEGER" property="limitType"/>
        <result column="limits" jdbcType="VARCHAR" property="limits"/>
    </resultMap>

    <resultMap id="DTOResultMap" type="com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityResponseDTO">
        <!--@mbg.generated-->
        <!--@Table promotion_activity-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="PA_template_id" jdbcType="INTEGER" property="paTemplateId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="upper_status" jdbcType="INTEGER" property="upperStatus"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="created_person" jdbcType="VARCHAR" property="createdPerson"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="app_id" jdbcType="BIGINT" property="appId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="coupons_flag" jdbcType="BOOLEAN" property="couponsFlag"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="return_money_type" jdbcType="INTEGER" property="returnMoneyType"/>
        <result column="ext" jdbcType="VARCHAR" property="ext" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="projects" jdbcType="VARCHAR" property="projects"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, PA_template_id, `name`, code, description, `status`,upper_status, created_time, created_person, start_time,
        end_time, updated_time,tenant_id, app_id, is_deleted, coupons_flag,
        remark, version, return_money_type,ext
    </sql>

    <!--auto generated by MybatisCodeHelper on 2019-11-21-->
    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_activity
        <where>
            and is_deleted = 0
            <if test="paTemplateId != null">
                and PA_template_id=#{paTemplateId,jdbcType=INTEGER}
            </if>
            <if test="name != null">
                and `name` like concat ('%', #{name,jdbcType=VARCHAR},'%')
            </if>
            <if test="code != null">
                and code=#{code,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and `status`=#{status,jdbcType=INTEGER}
            </if>
            <if test="startTime != null">
                and start_time &lt;= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and end_time >= #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="windowEnd != null">
                and start_time &lt;= #{windowEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="windowStart != null">
                and end_time >= #{windowStart,jdbcType=TIMESTAMP}
            </if>
            <if test="id != null">
                and id=#{id,jdbcType=BIGINT}
            </if>
            <if test="tenantId != null">
                and tenant_id=#{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="appId != null">
                and app_id=#{appId,jdbcType=BIGINT}
            </if>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach item="item" index="index" collection="ids"
                         open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="statusList != null and statusList.size()>0">
                and status in
                <foreach collection="statusList" index="index" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="paTemplateList != null and paTemplateList.size()>0">
                and PA_template_id in
                <foreach collection="paTemplateList" index="index" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        order by created_time desc
    </select>

    <select id="findAllByNameAndId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_activity
        <where>
            and is_deleted = 0
            <if test="params.activityName != null">
                and `name` =#{params.activityName,jdbcType=VARCHAR}
            </if>
            <if test="params.id != null">
                and `id` =#{params.id,jdbcType=BIGINT}
            </if>
            <if test="params.tenantId != null">
                and tenant_id=#{params.tenantId,jdbcType=VARCHAR}
            </if>
            <if test="params.appId != null">
                and app_id=#{params.appId,jdbcType=BIGINT}
            </if>
        </where>
    </select>
    <insert id="insertActivity" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        <!-- selectKey 标签实现主键返回 -->
        <!-- keyColumn:主键对应的表中的哪一列 -->
        <!-- keyProperty：主键对应的pojo中的哪一个属性 -->
        <!-- order：设置在执行insert语句前执行查询id的sql，在执行insert语句之后执行查询id的sql -->
        <!-- resultType：设置返回的id的类型 -->
<!--        <selectKey keyColumn="id" keyProperty="id" order="AFTER"-->
<!--                   resultType="int">-->
<!--            SELECT LAST_INSERT_ID()-->
<!--        </selectKey>-->
        INSERT INTO promotion_activity(
        PA_template_id,
        name,
        code,
        description,
        status,
        start_time,
        end_time,
        tenant_id,
        app_id,
        coupons_flag,
        created_time,
        created_person,
        updated_time,
        remark,
        return_money_type
        )VALUES
        (
        #{element.paTemplateId,jdbcType=INTEGER},
        #{element.name,jdbcType=VARCHAR},
        #{element.code,jdbcType=VARCHAR},
        #{element.description,jdbcType=VARCHAR},
        #{element.status,jdbcType=INTEGER},
        #{element.startTime,jdbcType=TIMESTAMP},
        #{element.endTime,jdbcType=TIMESTAMP},
        #{element.tenantId,jdbcType=VARCHAR},
        #{element.appId,jdbcType=VARCHAR},
        #{element.couponsFlag,jdbcType=BOOLEAN},
        #{element.createdTime,jdbcType=TIMESTAMP},
        #{element.createdPerson,jdbcType=VARCHAR},
        #{element.updatedTime,jdbcType=TIMESTAMP},
        #{element.remark,jdbcType=VARCHAR},
        #{element.returnMoneyType,jdbcType=INTEGER}
        )
    </insert>

    <!--auto generated by MybatisCodeHelper on 2019-11-28-->
    <select id="selectAllCanUserActivity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_activity
        <where>
            and is_deleted = 0
            <if test="null != appId and ''!=appId">
                and app_id=#{appId,jdbcType=BIGINT}
            </if>
            <if test="null != tenantId and ''!=tenantId">
                and tenant_id=#{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and `status`= #{status,jdbcType=INTEGER}
            </if>
            <if test="paTemplateIdCollection != null and paTemplateIdCollection.size() > 0">
                and PA_template_id in
                <foreach item="item" index="index" collection="paTemplateIdCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="nowDate != null">
                and start_time &lt;= #{nowDate,jdbcType=TIMESTAMP}
            </if>
            <if test="nowDate != null">
                and end_time >= #{nowDate,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

    <select id="findAllIncludeDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_activity
        <where>
            <if test="paTemplateId != null">
                and PA_template_id=#{paTemplateId,jdbcType=INTEGER}
            </if>
            <if test="code != null">
                and code=#{code,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and `status`=#{status,jdbcType=INTEGER}
            </if>
            <if test="startTime != null and endTime != null">
                and (updated_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP})
            </if>
            <if test="id != null">
                and id=#{id,jdbcType=BIGINT}
            </if>
            <if test="tenantId != null">
                and tenant_id=#{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="appId != null">
                and app_id=#{appId,jdbcType=BIGINT}
            </if>
            <if test="statusList != null and statusList.size()>0">
                and status in
                <foreach collection="statusList" index="index" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="paTemplateList != null and paTemplateList.size()>0">
                and PA_template_id in
                <foreach collection="paTemplateList" index="index" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        order by created_time desc
    </select>

    <select id="selectActivityCouponPage" resultMap="ActivityCouponResultMap">
        select
        t2.id strategy_id, t2.rules, t3.id limit_id, t3.type limit_type, t3.limits,
        t1.id, t1.PA_template_id, t1.`name`, t1.code, t1.description, t1.`status`, t1.created_time, t1.created_person, t1.start_time,
        t1.end_time, t1.updated_time,t1.tenant_id, t1.app_id, t1.is_deleted, t1.coupons_flag,
        t1.remark, t1.version, t1.return_money_type
        from promotion_activity t1
        left join promotion_strategy t2 on t2.activity_id = t1.id
        left join promotion_activity_limit t3 on t3.activity_id = t1.id and t3.type = 5
        <where>
            t1.is_deleted = 0
            <if test="query.startTime != null">
                and t1.end_time <![CDATA[>=]]> #{query.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="query.endTime != null">
                and t1.start_time <![CDATA[<=]]> #{query.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="query.activityName != null">
                and t1.`name` like concat ('%', #{query.activityName,jdbcType=VARCHAR},'%')
            </if>
            <if test="query.statusList != null and query.statusList.size()>0">
                and t1.status in
                <foreach collection="query.statusList" index="index" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="query.paTemplateList != null and query.paTemplateList.size()>0">
                and t1.PA_template_id in
                <foreach collection="query.paTemplateList" index="index" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="query.strategyCondition!=null">
                and JSON_CONTAINS(  JSON_EXTRACT ( t2.rules, '$[*].sort' ), '"${query.strategyCondition}"')
            </if>
            <if test="query.userLimit!=null">
                and JSON_CONTAINS ( JSON_EXTRACT ( t3.limits, '$[*].id' ), '"${query.userLimit}"' )
            </if>
        </where>
        order by t1.created_time desc
    </select>

    <!-- 活动通用列表查询 -->
    <select id="findCurrencyList" parameterType="com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery"
            resultMap="DTOResultMap">
        SELECT a.* from promotion_activity as a
        LEFT JOIN promotion_activity_participation as b on a.id = b.activity_id
        where a.is_deleted = 0
        <if test="query.projectName != null and query.projectName != ''">
            and b.project_name like concat ('%', #{query.projectName,jdbcType=VARCHAR},'%')
        </if>
        <if test="query.paTemplateId != null">
            and a.PA_template_id=#{query.paTemplateId,jdbcType=INTEGER}
        </if>
        <if test="query.name != null and query.name != ''">
            and a.`name` like concat ('%', #{query.name,jdbcType=VARCHAR},'%')
        </if>
        <if test="query.code != null and query.code !=''">
            and a.code=#{query.code,jdbcType=VARCHAR}
        </if>
        <if test="query.status != null">
            and a.`status`=#{query.status,jdbcType=INTEGER}
        </if>
        <if test="query.upperStatus != null">
            and a.`upper_status`=#{query.upperStatus,jdbcType=INTEGER}
        </if>
        <if test="query.startTime != null and query.endTime != null">
            and
            (
            a.start_time between #{query.startTime,jdbcType=TIMESTAMP} and #{query.endTime,jdbcType=TIMESTAMP}
            or
            a.end_time between  #{query.startTime,jdbcType=TIMESTAMP} and #{query.endTime,jdbcType=TIMESTAMP}
            )
        </if>
        <!--<if test="query.endTime != null">
            and a.end_time &lt;= #{query.endTime,jdbcType=TIMESTAMP}
        </if>-->
        <if test="query.windowEnd != null">
            and a.start_time &lt;= #{query.windowEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="query.windowStart != null">
            and a.end_time >= #{query.windowStart,jdbcType=TIMESTAMP}
        </if>
        <if test="query.id != null">
            and a.id=#{query.id,jdbcType=BIGINT}
        </if>
        <if test="query.tenantId != null and query.tenantId != ''">
            and a.tenant_id=#{query.tenantId,jdbcType=VARCHAR}
        </if>
        <if test="query.appId != null">
            and a.app_id=#{query.appId,jdbcType=BIGINT}
        </if>
        <if test="query.ids != null and query.ids.size() > 0">
            and a.id in
            <foreach item="item" index="index" collection="query.ids"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="query.statusList != null and query.statusList.size()>0">
            and a.status in
            <foreach collection="query.statusList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="query.paTemplateList != null and query.paTemplateList.size()>0">
            and a.PA_template_id in
            <foreach collection="query.paTemplateList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="query.kpiPath != null">
            and JSON_CONTAINS ( JSON_EXTRACT ( a.ext, '$.kpiPath' ), '${query.kpiPath}' )
        </if>
        GROUP BY a.id
        order by a.created_time desc
    </select>

    <update id="updateStatusByStartTime">
        update promotion_activity set status = 2 where is_deleted = 0 and status = 1 and ifnull(JSON_EXTRACT(`ext`,'$.auditStatus'),1) = 1 and <![CDATA[ start_time <= now()]]>
    </update>

    <update id="updateStatusByEndTime">
        update promotion_activity set status = 3,top_time = null where is_deleted = 0 and status = 2 and <![CDATA[ end_time < now()]]>
    </update>


    <select id="getListByShouldStart" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List"/>
        from promotion_activity
        <where>
            is_deleted = 0 and status = 1 and <![CDATA[ start_time <= now()]]>
        </where>
    </select>

    <select id="getListByShouldFinish" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List"/>
        from promotion_activity
        <where>
            is_deleted = 0 and status = 2 and <![CDATA[ end_time < now()]]>
        </where>
    </select>

    <select id="findAnalysisList1"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityAnalysisResponseDTO">
        select pa.id,pa.PA_template_id as template,
               pa.name as activityName,
               count(pl.user_id) as joinNumber,
               count(DISTINCT pl.user_id) as joinPeopleNumber,
        (select COUNT(DISTINCT user_id) from promotion_activity_form_feedback where activity_id = pa.id and is_deleted = 0  ) as lzPeopleNumber,
        (select count(DISTINCT user_id) from promotion_activity_verify where activity_id = pa.id and verify_status = 1 and is_deleted = 0 ) as writeOffPeopleNumber
         from promotion_activity pa
         Left join promotion_activity_partake_log pl on pl.activity_id = pa.id
        where pa.is_deleted = 0
        <if test="query.activityName != null and query.activityName != ''">
            and pa.name like concat('%',#{query.activityName},'%')
        </if>
        <if test="query.templateId != null">
            and pa.PA_template_id = #{query.templateId}
        </if>
        GROUP BY pa.id
        order by pa.created_time desc
    </select>
    <select id="findAnalysisList"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityAnalysisResponseDTO">
        select pa.id,pa.PA_template_id as template,
               pa.name as activityName,
               count(DISTINCT pl.user_id) as writeOffPeopleNumber
        from promotion_activity pa
        left join promotion_activity_verify pl on pl.activity_id = pa.id and pl.verify_status = 1 and pl.is_deleted = 0
        where pa.is_deleted = 0
        <if test="query.activityName != null and query.activityName != ''">
            and pa.name like concat('%',#{query.activityName},'%')
        </if>
        <if test="query.templateId != null">
            and pa.PA_template_id = #{query.templateId}
        </if>
        <if test="query.upperStatus != null">
            and pa.upper_status = #{query.upperStatus,jdbcType=INTEGER}
        </if>
        <if test="query.activityIdList != null and query.activityIdList.size() > 0">
            and pa.id in
            <foreach item="item" index="index" collection="query.activityIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY pa.id
        order by pa.created_time desc
    </select>
    <select id="analysisByActivityId"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.OverviewOfIndicatorsDTO">
        select pa.name as activityName,
               count(pl.user_id) as joinNumber,
               count(case when DATE_FORMAT(pl.created_time,'%Y-%m-%d') = curdate() then pl.user_id else null end) as dayJoinNumber,
               count(case when DATE_FORMAT(pl.created_time,'%Y-%m-%d') = date_sub(curdate(),interval 1 day) then pl.user_id else null end) as dyesterdayJoinNumber,

               count(DISTINCT pl.user_id) as joinPeopleNumber,
               count(DISTINCT case when DATE_FORMAT(pl.created_time,'%Y-%m-%d') = curdate() then pl.user_id else null end) as dayJoinPeopleNumber,
               count(DISTINCT case when DATE_FORMAT(pl.created_time,'%Y-%m-%d') = date_sub(curdate(),interval 1 day) then pl.user_id else null end) as dyesterdayJoinPeopleNumber,

               count(DISTINCT af.user_id) as lzPeopleNumber,
               count(DISTINCT case when DATE_FORMAT(pl.created_time,'%Y-%m-%d') = curdate() then af.user_id else null end) as daylzPeopleNumber,
               count(DISTINCT case when DATE_FORMAT(pl.created_time,'%Y-%m-%d') = date_sub(curdate(),interval 1 day) then af.user_id else null end) as dyesterdaylzPeopleNumber,

               count(DISTINCT av.user_id) as writeOffPeopleNumber,
               count(DISTINCT case when DATE_FORMAT(pl.created_time,'%Y-%m-%d') = curdate() then av.user_id else null end) as dayWriteOffPeopleNumber,
               count(DISTINCT case when DATE_FORMAT(pl.created_time,'%Y-%m-%d') = date_sub(curdate(),interval 1 day) then av.user_id else null end) as dyesterdayWriteOffPeopleNumber
        from promotion_activity pa
         Left join promotion_activity_partake_log pl on pl.activity_id = pa.id
         LEFT JOIN promotion_activity_form_feedback af on af.activity_id = pl.activity_id and af.user_id = pl.user_id
         LEFT join promotion_activity_verify av ON av.activity_id = pa.id and av.verify_status = 1 and av.is_deleted = 0
        where pa.is_deleted = 0 and pa.id = #{id}
    </select>

    <select id="findChannelDistribution"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ChannelDistributionResponseDTO">
        select pl.type,
               count(DISTINCT pl.user_id) as joinPeopleNumber,
               count(DISTINCT af.user_id) as lzPeopleNumber
        from promotion_activity pa
                 Left join promotion_activity_partake_log pl on pl.activity_id = pa.id
                 LEFT JOIN promotion_activity_form_feedback af on af.activity_id = pl.activity_id and af.user_id = pl.user_id
        where pa.is_deleted = 0 and pa.id = #{id} and pl.type is not null
        group by pl.type
    </select>
    <select id="findCityDistribution"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.CityDistributionResponseDTO">
        select *, @rownum:=@rownum+1 as rank
        from (SELECT @rownum := 0) r ,
          (select pl.area as province,
            count(DISTINCT pl.user_id) as joinPeopleNumber
            from promotion_activity pa
            Left join promotion_activity_partake_log pl on pl.activity_id = pa.id
            where pa.is_deleted = 0  and pa.id = #{id}
            group by pl.area
            order by joinPeopleNumber asc
            ) a  limit 10
    </select>
    <select id="findActivityTrendJoinPeopleNumber"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityTrendResponseDTO">
        select pl.created_time,
        count(DISTINCT pl.user_id) as peopleNumber,
        <if test="dateType == 1">
            DATE_FORMAT(pl.created_time,'%H') as date
        </if>

        <if test="dateType == 2">
            DATE_FORMAT(pl.created_time,'%d') as date
        </if>
        from promotion_activity pa
        Left join promotion_activity_partake_log pl on pl.activity_id = pa.id
        where pa.is_deleted = 0 and pa.id = #{id}
        <if test="dateType == 1">
            and DATE_FORMAT(pl.created_time,'%Y-%m-%d') = #{date}
            group by DATE_FORMAT(pl.created_time,'%Y-%m-%d %H')
        </if>

        <if test="dateType == 2">
            and DATE_FORMAT(pl.created_time,'%Y-%m') = #{date}
            group by DATE_FORMAT(pl.created_time,'%Y-%m-%d')
        </if>
    </select>

    <select id="findActivityTrendlzPeopleNumber"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityTrendResponseDTO">
        select pl.created_time,
        count(DISTINCT af.user_id) as peopleNumber,
        <if test="dateType == 1">
            DATE_FORMAT(pl.created_time,'%H') as date
        </if>

        <if test="dateType == 2">
            DATE_FORMAT(pl.created_time,'%d') as date
        </if>
        from promotion_activity pa
        Left join promotion_activity_partake_log pl on pl.activity_id = pa.id
        LEFT JOIN promotion_activity_form_feedback af on af.activity_id = pl.activity_id and af.user_id = pl.user_id
        where pa.is_deleted = 0 and pa.id = #{id}
        <if test="dateType == 1">
            and DATE_FORMAT(pl.created_time,'%Y-%m-%d') = #{date}
            group by DATE_FORMAT(pl.created_time,'%Y-%m-%d %H')
        </if>

        <if test="dateType == 2">
            and DATE_FORMAT(pl.created_time,'%Y-%m') = #{date}
            group by DATE_FORMAT(pl.created_time,'%Y-%m-%d')
        </if>
    </select>

    <select id="getAnalysisActivityInfo"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityTopicAnalysisActivityInfoResponseDTO">
          select t1.id,
               t1.name as activityName,
               group_concat(DISTINCT ap.project_name) as projects,
               count(DISTINCT pl.user_id) as joinPeopleNumber,
               count(DISTINCT av.user_id) as writeOffPeopleNumber
                 from (select  pa.* from promotion_activity pa where 1=1
                <if test="query.activityIdList != null and query.activityIdList.size() > 0">
                    and pa.id in
                    <foreach item="item" index="index" collection="query.activityIdList"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                 and pa.is_deleted = 0) as t1
                 LEFT JOIN promotion_activity_participation as ap on t1.id = ap.activity_id and ap.is_deleted = 0
                 Left join promotion_activity_partake_log pl on pl.activity_id = t1.id
                 LEFT join promotion_activity_verify av ON av.activity_id = t1.id and av.verify_status = 1 and av.is_deleted = 0
          GROUP BY t1.id
    </select>
    <select id="joinUserAnalysis" resultType="com.deepexi.dxp.marketing.domain.marketing.response.IndexAnalysisDTO"
            parameterType="java.lang.Long">
        select
               count(pl.user_id) as totalNumber,
               count(case when DATE_FORMAT(pl.created_time,'%Y-%m-%d') = curdate() then pl.user_id else null end) as todayNumber,
               count(case when DATE_FORMAT(pl.created_time,'%Y-%m-%d') = date_sub(curdate(),interval 1 day) then pl.user_id else null end) as yesterdayNumber,

               count(DISTINCT pl.user_id) as totalPeopleNumber,
               count(DISTINCT case when DATE_FORMAT(pl.created_time,'%Y-%m-%d') = curdate() then pl.user_id else null end) as todayPeopleNumber,
               count(DISTINCT case when DATE_FORMAT(pl.created_time,'%Y-%m-%d') = date_sub(curdate(),interval 1 day) then pl.user_id else null end) as yesterdayPeopleNumber

        from promotion_activity pa
        Left join promotion_activity_partake_log pl on pl.activity_id = pa.id
        where pa.is_deleted = 0 and pa.id = #{id}
    </select>
    <select id="receiveAwardAnalysis" resultType="com.deepexi.dxp.marketing.domain.marketing.response.IndexAnalysisDTO"
            parameterType="java.lang.Long">
        select
            count(userId) as totalPeopleNumber,
            count(case when DATE_FORMAT(createdTime,'%Y-%m-%d') = curdate() then userId else null end) as todayPeopleNumber,
            count(case when DATE_FORMAT(createdTime,'%Y-%m-%d') = date_sub(curdate(),interval 1 day) then userId else null end) as yesterdayPeopleNumber
        from (
                 select av.user_id as userId, min(av.created_time) as createdTime,verify_status
                 from promotion_activity pa
                 join promotion_activity_verify av ON av.activity_id = pa.id and av.is_deleted = 0
                 where pa.is_deleted = 0 and pa.id = #{id}
                 group by av.user_id
             ) a
    </select>
    <select id="writeOffNumberAnalysis" resultType="com.deepexi.dxp.marketing.domain.marketing.response.IndexAnalysisDTO"
            parameterType="java.lang.Long">
        select
            count(userId) as totalWriteOffNumber,
            count(case when DATE_FORMAT(createdTime,'%Y-%m-%d') = curdate() then userId else null end) as todayWriteOffNumber,
            count(case when DATE_FORMAT(createdTime,'%Y-%m-%d') = date_sub(curdate(),interval 1 day) then userId else null end) as yesterdayWriteOffNumber
        from (
                 select av.user_id as userId, min(av.created_time) as createdTime,verify_status
                 from promotion_activity pa
                 join promotion_activity_verify av ON av.activity_id = pa.id and av.is_deleted = 0
                 where pa.is_deleted = 0 and pa.id = #{id} and verify_status = 1
                 group by av.user_id
             ) a
    </select>

    <!-- 个人活动 -->
    <select id="findMyActivityList" resultMap="BaseResultMap">
        SELECT DISTINCT b.*  from  promotion_activity_partake_log as a
        JOIN  promotion_activity as b on a.activity_id=b.id
        <if test="query.queryType == 3">
        LEFT JOIN promotion_activity_partake_log AS a2
            ON a2.user_id = a.user_id AND a2.activity_id = a.activity_id AND a2.is_deleted = 0
        </if>
       where 1=1
        <if test="query.queryType == 1">
            and a.is_deleted = 0
        </if>
        <if test="query.queryType == 3">
            and a.is_deleted = 1 AND a2.activity_id IS NULL
        </if>
        <if test="query.userId != null">
            and  a.user_id= #{query.userId}
        </if>
        <if test="query.phone != null">
            and  a.phone= #{query.phone}
        </if>
        <if test="query.status != null">
            and b.`status`= #{query.status}
        </if>
        <if test="query.statusList != null and query.statusList.size() > 0">
            and b.`status` in
            <foreach item="item" index="index" collection="query.statusList"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <!-- 置业通小程序中展示 -->
        <if test="query.deliveryChannel != null">
            and JSON_EXTRACT(b.`ext`,'$.deliveryChannel') like concat('%',#{query.deliveryChannel},'%')
        </if>
        order by (CASE
        WHEN b.`status` = 2 THEN 0
        WHEN b.`status` = 1 THEN 1
        ELSE 2 END) asc, b.start_time desc
     </select>

    <!-- 已发布活动列表 -->
    <select id="findAllPublishActivityList" resultMap="BaseResultMap">
        select
        DISTINCT act.*
        from promotion_activity act
        join promotion_activity_participation par on act.id= par.activity_id
            <if test="query.cityName != null and query.cityName != ''">
                and par.real_city_name=#{query.cityName}
            </if>
            <if test="query.cityId != null and query.cityId != ''">
                and par.real_city_id=#{query.cityId}
            </if>
            <if test="query.companyIds != null and query.companyIds.size() > 0">
                and par.city_id in
                <foreach item="item" collection="query.companyIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.projectIds != null and query.projectIds.size() > 0">
                and par.project_id in
                <foreach item="item" collection="query.projectIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        <if test="query.tagNames != null and query.tagNames.size() > 0">
            join promotion_activity_association a on a.main_id=act.id
        </if>
        <where>
            and act.is_deleted = 0
            and upper_status=1
            <if test="query.paTemplateId != null">
                and act.PA_template_id=#{query.paTemplateId,jdbcType=INTEGER}
            </if>
            <if test="query.name != null">
                and act.name like concat ('%', #{nquery.ame,jdbcType=VARCHAR},'%')
            </if>
            <if test="query.code != null">
                and act.code=#{query.code,jdbcType=VARCHAR}
            </if>
            <if test="query.status != null">
                and act.status=#{query.status,jdbcType=INTEGER}
            </if>
            <if test="query.startTime != null">
                and act.start_time &lt;= #{query.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="query.endTime != null">
                and act.end_time >= #{query.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="query.windowEnd != null">
                and act.start_time &lt;= #{query.windowEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="query.windowStart != null">
                and act.end_time >= #{query.windowStart,jdbcType=TIMESTAMP}
            </if>
            <if test="query.id != null">
                and act.id=#{query.id,jdbcType=BIGINT}
            </if>
            <if test="query.tenantId != null">
                and act.tenant_id=#{query.tenantId,jdbcType=VARCHAR}
            </if>
            <if test="query.appId != null">
                and act.app_id=#{query.appId,jdbcType=BIGINT}
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                and act.id in
                <foreach item="item" index="index" collection="query.ids"
                         open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="query.statusList != null and query.statusList.size()>0">
                and act.status in
                <foreach collection="query.statusList" index="index" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="query.paTemplateList != null and query.paTemplateList.size()>0">
                and act.PA_template_id in
                <foreach collection="query.paTemplateList" index="index" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <!-- 置业通小程序中展示 -->
            <if test="query.deliveryChannel != null">
                and JSON_EXTRACT(act.`ext`,'$.deliveryChannel') like concat('%',#{query.deliveryChannel},'%')
            </if>
            <if test="query.communityId != null">
                and JSON_EXTRACT(act.`ext`,'$.communityId') = #{query.communityId}
            </if>
            <if test="query.tagNames != null and query.tagNames.size() > 0">
                AND a.sub_text1 IN
                <foreach collection="query.tagNames" item="tagName" open="(" close=")" separator=",">
                    #{tagName}
                </foreach>
            </if>
        </where>
        order by act.top_time desc,(CASE
        WHEN `status` = 2  THEN 0
        WHEN `status` = 1 THEN 1
        WHEN `status` = 3  THEN 2
        WHEN `status` = 4 THEN 3
        ELSE 4 END) asc, start_time desc
    </select>

    <!-- 所有创建人查询用于定时更新用户项目信息 -->
    <select id="findAllUserIdList" resultType="java.lang.String">
        SELECT DISTINCT(created_person) created_person from (
        SELECT DISTINCT(created_person) created_person from promotion_activity where is_deleted=0 and created_person is not NULL and LENGTH(created_person) >0
        UNION
        SELECT DISTINCT(user_id) as created_person from promotion_activity_group where is_deleted=0 and user_id is not NULL and LENGTH(user_id) >0) as t
    </select>

    <!-- 活动分析所有活动ID -->
    <select id="getAnalysisActivityInfoActivityIds" resultType="java.lang.Long">
         select DISTINCT(activity_id) as activity_id from (select agr.related_activity_id as activity_id
                 from promotion_activity_group_related agr
                 left join promotion_activity_group ag on ag.id = agr.related_group_id
                 where group_id = = #{groupId} and agr.related_activity_id !=0
                 union ALL
                 select agr2.related_activity_id as activity_id
                 from promotion_activity_group_related agr
                     left join promotion_activity_group ag on ag.id = agr.related_group_id
                     left join promotion_activity_group_related agr2 on agr2.group_id = agr.related_group_id
                 where agr.group_id = #{groupId} and agr.related_group_id !=0) as t where t.activity_id is not null
    </select>

    <select id="selectWholePageList"
            resultType="com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityDTO">
        SELECT
        a.id ,
        a.PA_template_id ,
        a.name ,
        a.code ,
        a.status ,
        a.upper_status ,
        a.start_time ,
        a.end_time ,
        a.ext ext_str,
        a.app_id ,
        a.tenant_id ,
        a.created_by ,
        a.created_time ,
        a.updated_by ,
        a.updated_time
        from
        promotion_activity as a
        JOIN promotion_activity_participation as b on a.id = b.activity_id and b.is_deleted = 0
        where a.is_deleted = 0
        <if test="query.idList != null and query.idList.size() > 0">
            and a.id in
            <foreach collection="query.idList" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
        <if test="query.searchKey != null and query.searchKey != ''">
            and (a.name like concat ('%', #{query.searchKey},'%')
            or a.created_by like concat ('%', #{query.searchKey},'%'))
        </if>
        <if test="query.paTemplateIdList != null and query.paTemplateIdList.size > 0">
            and a.PA_template_id in
            <foreach collection="query.paTemplateIdList" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
        <if test="query.statusList != null and query.statusList.size > 0">
            and a.`status` in
            <foreach collection="query.statusList" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
        <if test="query.upperStatus != null">
            and a.`upper_status`=#{query.upperStatus,jdbcType=INTEGER}
        </if>
        <if test="query.auditStatus != null">
            and JSON_EXTRACT(a.`ext`,'$.auditStatus') = #{query.auditStatus}
        </if>
        <if test="query.lineType != null and query.lineType > 0">
            and JSON_EXTRACT(a.`ext`,'$.lineType') = #{query.lineType}
        </if>
        <if test="query.deliveryChannel != null and query.deliveryChannel != ''">
            and JSON_EXTRACT(a.`ext`,'$.deliveryChannel') = #{query.deliveryChannel}
        </if>
        <if test="query.projectName != null and query.projectName != ''">
            and b.project_name like concat ('%', #{query.projectName,jdbcType=VARCHAR},'%')
        </if>
        <if test="query.startTime != null and query.endTime != null">
            and
            (
            a.start_time between #{query.startTime,jdbcType=TIMESTAMP} and #{query.endTime,jdbcType=TIMESTAMP}
            or
            a.end_time between  #{query.startTime,jdbcType=TIMESTAMP} and #{query.endTime,jdbcType=TIMESTAMP}
            )
        </if>
        GROUP BY a.id
        order by a.created_time desc
    </select>
    <select id="findMyPublishActivityList" resultMap="BaseResultMap">
        select act.* from promotion_activity act
        where JSON_EXTRACT(act.`ext`,'$.principalMobile') = #{query.phone}
            and JSON_EXTRACT(act.`ext`,'$.deliveryChannel') like concat('%',#{query.deliveryChannel},'%')
            and act.created_time > DATE_SUB(NOW(), INTERVAL 1 YEAR)
        order by (CASE WHEN JSON_EXTRACT(act.`ext`,'$.auditStatus') = 0  THEN 0
                      WHEN JSON_EXTRACT(act.`ext`,'$.auditStatus') = 2  THEN 1
                      ELSE 2 END), start_time desc
    </select>
</mapper>