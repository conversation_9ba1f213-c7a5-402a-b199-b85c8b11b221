package com.deepexi.dxp.middle.promotion.dao.impl.specify;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityParticipationQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityPageQuery;
import com.deepexi.dxp.middle.promotion.common.base.SuperEntity;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityParticipationDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityParticipationDO;
import com.deepexi.dxp.middle.promotion.mapper.specify.ActivityParticipationMapper;
import com.deepexi.util.CollectionUtil;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class ActivityParticipationDAOImpl  extends ServiceImpl<ActivityParticipationMapper,
        ActivityParticipationDO> implements ActivityParticipationDAO {
    @Override
    public List<ActivityParticipationDO> listByActivityId(Long id) {
        QueryWrapper<ActivityParticipationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityParticipationDO::getActivityId,id);
        queryWrapper.lambda().eq(ActivityParticipationDO::getDeleted, SuperEntity.DR_NORMAL);
        return this.list(queryWrapper);
    }

    @Override
    public Boolean deletedByActivityIds(List<Long> activityIds) {
        UpdateWrapper<ActivityParticipationDO> wrapper = new UpdateWrapper<>();
        wrapper.lambda().in(ActivityParticipationDO::getActivityId,activityIds).set(ActivityParticipationDO::getDeleted,1).set(ActivityParticipationDO::getUpdatedTime,new Date());
        return   baseMapper.update(null, wrapper) > 0;
    }

    @Override
    public List<ActivityParticipationDO> listByProjectIds(List<String> projectIds) {
        QueryWrapper<ActivityParticipationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ActivityParticipationDO::getProjectId,projectIds);
        queryWrapper.lambda().eq(ActivityParticipationDO::getDeleted, SuperEntity.DR_NORMAL);
        queryWrapper.select("DISTINCT activity_id");
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<ActivityParticipationDO> getListByActivityList(List<Long> activityIds) {
        if(CollectionUtil.isEmpty(activityIds)){
            return Lists.newArrayList();
        }
        return baseMapper.getListByActivityList(activityIds);
    }

    @Override
    public List<ActivityParticipationDO> getSelectActivity(ActivityParticipationQuery activityParticipationQuery) {
        if(CollectionUtil.isEmpty(activityParticipationQuery.getActivityIdList())){
            return Lists.newArrayList();
        }
        QueryWrapper<ActivityParticipationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityParticipationDO::getDeleted, SuperEntity.DR_NORMAL);
        queryWrapper.lambda().in(ActivityParticipationDO::getActivityId,activityParticipationQuery.getActivityIdList());
        if(activityParticipationQuery.getSelectType() == 0){
            queryWrapper.select("DISTINCT area_id,area_name");
        }else if(activityParticipationQuery.getSelectType() == 1){
            if(CollectionUtil.isEmpty(activityParticipationQuery.getAreaIdList())){
                return Lists.newArrayList();
            }
            queryWrapper.lambda().in(ActivityParticipationDO::getAreaId,activityParticipationQuery.getAreaIdList());
            queryWrapper.select("DISTINCT real_city_id as city_id,real_city_name as city_name");
        }else if(activityParticipationQuery.getSelectType() == 2){
            if(CollectionUtil.isEmpty(activityParticipationQuery.getAreaIdList()) || CollectionUtil.isEmpty(activityParticipationQuery.getCityIdList())){
                return Lists.newArrayList();
            }
            queryWrapper.lambda().in(ActivityParticipationDO::getAreaId,activityParticipationQuery.getAreaIdList());
            queryWrapper.lambda().in(ActivityParticipationDO::getRealCityId,activityParticipationQuery.getCityIdList());
            queryWrapper.select("DISTINCT project_id,project_name,area_id,area_name,real_city_id,real_city_name");
        }else{
            return Lists.newArrayList();
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<Long> listActivityId(PromotionActivityPageQuery query) {
        return baseMapper.listActivityId(query);
    }
}
