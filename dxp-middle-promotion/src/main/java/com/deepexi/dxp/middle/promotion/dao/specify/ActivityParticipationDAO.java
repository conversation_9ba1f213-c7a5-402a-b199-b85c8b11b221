package com.deepexi.dxp.middle.promotion.dao.specify;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityParticipationQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityPageQuery;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityParticipationDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ActivityParticipationDAO  extends IService<ActivityParticipationDO> {

    /**
     * 活动id获取项目信息
     * @param id
     * @return
     */
    List<ActivityParticipationDO> listByActivityId(Long id);

    /**
     * 根据活动ID删除
     * @param activityIds
     * @return
     */
    Boolean deletedByActivityIds(List<Long> activityIds);


    /**
     * 通过项目ID获取活动id
     * @param projectIds
     * @return
     */
    List<ActivityParticipationDO> listByProjectIds(List<String> projectIds);

    /**
     * 通过活动ID获取项目
     * @param activityIds
     * @return
     */
    List<ActivityParticipationDO> getListByActivityList(List<Long> activityIds);


    /**
     * 通过活动信息获取区域、城市、项目
     * @param activityParticipationQuery
     * @return
     */
    List<ActivityParticipationDO> getSelectActivity(ActivityParticipationQuery activityParticipationQuery);

    List<Long> listActivityId(PromotionActivityPageQuery query);
}
