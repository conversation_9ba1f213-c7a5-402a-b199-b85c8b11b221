---
description: 
globs: 
alwaysApply: false
---
# 华发活动服务项目指南

## 项目概述
这是一个基于Spring Boot的华发活动中心服务项目，采用微服务架构，主要提供营销活动相关的功能。

## 项目结构

### 主要模块
- **dxp-domain-marketing-api**: API接口定义模块
- **dxp-domain-marketing-common**: 公共组件和工具模块
- **dxp-middle-promotion**: 促销活动中间层模块
- **dxp-middle-marketing**: 营销中间层模块
- **huafa-actvity-provider**: 主服务提供者模块

### 核心配置文件
- [pom.xml](mdc:pom.xml) - Maven主配置文件
- [MarketingApplication.java](mdc:huafa-actvity-provider/src/main/java/com/deepexi/dxp/marketing/MarketingApplication.java) - Spring Boot启动类

## 开发指南

### 技术栈
- Spring Boot 2.3.2.RELEASE
- Spring Cloud Hoxton.SR9
- Spring Cloud Alibaba 2.2.5.RELEASE
- Java 1.8
- Maven

### 主要包结构
```
com.deepexi.dxp.marketing
├── controller/     # 控制器层
│   ├── promotion/  # 促销活动控制器
│   └── specify/    # 特定功能控制器
├── service/        # 服务层
│   ├── promotion/  # 促销活动服务
│   ├── specify/    # 特定功能服务
│   └── assist/     # 辅助服务
├── manager/        # 管理层
├── engine/         # 引擎层
├── config/         # 配置类
├── converter/      # 转换器
├── utils/          # 工具类
└── domain/         # 领域模型
```

### 关键特性
- 支持定时任务 (@EnableScheduling)
- 支持AOP切面 (@EnableAspectJAutoProxy)
- 集成Eureka服务发现
- 支持Swagger文档 (http://localhost:38007/swagger-ui.html)

### 开发注意事项
1. 所有新功能应在对应的模块中开发
2. 公共组件放在common模块中
3. API接口定义在api模块中
4. 遵循分层架构：controller -> service -> manager -> dao
5. 使用Lombok简化代码
6. 遵循华发项目的命名规范

### 数据库相关
- 数据库脚本位于 [docs/db/](mdc:docs/db) 目录
- 包含表结构变更和初始化脚本

### 部署相关
- [Dockerfile](mdc:Dockerfile) - 容器化部署配置
- [createSdk.sh](mdc:createSdk.sh) - SDK生成脚本
- [sonar-project.properties](mdc:sonar-project.properties) - 代码质量检查配置

