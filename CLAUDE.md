# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 常用命令

### Maven命令
- 编译项目: `mvn clean compile`
- 运行测试: `mvn test`
- 打包项目: `mvn clean package`
- 跳过测试打包: `mvn clean package -DskipTests`
- 安装到本地仓库: `mvn clean install`

### 启动应用
- 开发环境启动: `java -jar huafa-actvity-provider/target/huafa-actvity-provider-1.0.0-BASE-SNAPSHOT.jar`
- 指定配置文件启动: `java -jar huafa-actvity-provider/target/huafa-actvity-provider-1.0.0-BASE-SNAPSHOT.jar --spring.profiles.active=dev`

### Docker命令
- 构建镜像: `docker build -t huafa-activity-service .`
- 运行容器: `docker run -p 38007:38007 huafa-activity-service`

## 项目架构

### 主要模块
- **dxp-domain-marketing-api**: API接口定义模块
- **dxp-domain-marketing-common**: 公共组件和工具模块
- **dxp-middle-promotion**: 促销活动中间层模块
- **dxp-middle-marketing**: 营销中间层模块
- **huafa-actvity-provider**: 主服务提供者模块

### 核心包结构
```
com.deepexi.dxp.marketing
├── controller/     # 控制器层
│   ├── promotion/  # 促销活动控制器
│   └── specify/    # 特定功能控制器
├── service/        # 服务层
│   ├── promotion/  # 促销活动服务
│   ├── specify/    # 特定功能服务
│   └── assist/     # 辅助服务
├── manager/        # 管理层
├── engine/         # 引擎层
├── config/         # 配置类
├── converter/      # 转换器
├── utils/          # 工具类
└── domain/         # 领域模型
```

### 关键特性
- 支持定时任务 (@EnableScheduling)
- 支持AOP切面 (@EnableAspectJAutoProxy)
- 集成Eureka服务发现
- 支持Swagger文档 (http://localhost:38007/swagger-ui.html)

## 开发规范

### API设计规范
- 使用RESTful风格
- URL使用名词复数形式
- 统一响应格式:
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 代码规范
- 分层架构: Controller -> Service -> Manager -> DAO
- 使用Lombok简化代码
- 方法长度不超过50行
- 类长度不超过500行
- 注释使用中文

### 数据库相关
- 数据库脚本位于 `docs/db/` 目录
- 使用MyBatis-Plus进行数据库操作
- 分页查询使用`Page`对象