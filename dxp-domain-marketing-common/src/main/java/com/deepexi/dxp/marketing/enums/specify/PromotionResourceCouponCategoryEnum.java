package com.deepexi.dxp.marketing.enums.specify;

import lombok.Getter;

/**
 * 细分类别
 */
@Getter
public enum PromotionResourceCouponCategoryEnum {
    ORDINARY_COUPON(0,"普通劵"),
    REAL_ESTATE_COUPON(1,"房源劵"),//特价房
    COMMODITY_COUPON(2,"礼品劵"),
    ;

    private final Integer id;
    private final String value;

    PromotionResourceCouponCategoryEnum(Integer id, String value) {
        this.id = id;
        this.value = value;
    }

    public static String getValueById(Integer id) {
        PromotionResourceCouponCategoryEnum[] enums = values();
        for (PromotionResourceCouponCategoryEnum enum1 : enums) {
            if (enum1.getId().equals(id)) {
                return enum1.getValue();
            }
        }
        return null;
    }
}
