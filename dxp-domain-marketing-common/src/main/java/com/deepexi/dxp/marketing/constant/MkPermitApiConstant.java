package com.deepexi.dxp.marketing.constant;

/**
 * 营销权限管理系统API接口相关配置
 */
public class MkPermitApiConstant {


    //public static final String BASE_URL = "http://mk-permit.paasdev.huafagroup.com:35143";

    /**
     * 用户口项目无登录请求url
     */
    public static final String USER_PROJECT_NO_LOGIN_URL = "/api/getUserProjectNoLogin";
    public static final String USER_PROJECT_NO_LOGIN_URL_V2 = "/api/v2/getUserProjectNoLogin";


    /**
     * 用户组织
     */
    public static final String ORG_GET_URL = "/api/getOrg";

    /**
     * 用户所有菜单权限
     */
    public static final String MENU_PERMISSION_GET_URL = "/api/getUserAllMenuPermission";

    /**
     * 用户信息
     */
    public static final String USER_INFO_URL = "/api/getUserInfo";

    /**
     * 免登录获取用户信息
     */
    public static final String USER_INFO_NO_LOGIN = "/api/getUserInfoByUserId";

    /**
     * 用户区域
     */
    //public static final String GET_USER_AREA_URL = "/api/getUserRealArea"; //用户区域-地理位置概念
    public static final String GET_USER_AREA_URL = "/api/getUserArea";
    /**
     * 用户区域
     */
    //public static final String GET_USER_REAL_CITY_BY_AREA_ID_URL = "/api/getUserRealCityByRealAreaId";//用户城市-地理位置概念
    public static final String GET_USER_REAL_CITY_BY_AREA_ID_URL = "/api/getUserCityByAreaId";

    /**
     * 查询项目详情
     */
    public static final String GET_PROJECT_PAGE_BY_HF_PROJECT_ID = "/api/salesCenter/getProjectPageByHfProjectId/";

    /**
     * 校验业主身份
     */
    public static final String IDENTITY_CHECK_URL = "/identityCheck/proprietorRoleByProjectIdAndTelphone";

}