package com.deepexi.dxp.marketing.enums.activity.strategy.operation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ming.zhong
 * @date created in 17:38 2019/12/3
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum TimeOperationEnum {
    /**
     * 优惠券状态
     */
    START_TIME("start", "起始时间"),
    END_TIME("end", "终止时间"),
    SINCE_GET("since", "多少天");
    private String id;
    private String value;
}
