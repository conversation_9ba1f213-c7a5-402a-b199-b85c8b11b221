package com.deepexi.dxp.marketing.enums.activity.strategy;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Arrays;

/**
 * 活动类型PA_template_id 枚举
 * 增加模板类型，需要同时增加分享路经:ActivityChannelInfoEnum
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/30 11:15
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
public enum StrategyGroupEnum {
    /**
     * 活动类型枚举
     */
    MJS_G("1", "满减", 100),
    ZJRX_G("3", "N元N件", 100),
    DZCX_G("4", "打折促销", 100),
    MZ_G("5", "买赠", 100),
    JFDH_G("6", "积分兑换", 100),
    PTHD_G("7", "拼团活动", 100),
    LQHD_G("8", "领券活动", 100),
    YS_G("10", "预售活动", 1),
    CZ_G("11", "充值活动", 100),
    SECKILL("13", "秒杀活动", 0),

    //华发项目
    HF_FORM_ACT("14","表单活动",100),
    HF_LUCKYDRAW_ACT("15","抽奖活动",100),
    HF_COUPON_ACT("16","优惠券活动",100),
    HF_SECKILL_ACT("17","秒杀活动",100),
    HF_FISSION_ASSIST_ACT("18","点击类助力活动",100),
    HF_FISSION_REDUCEPRICE_ACT("19","砍价活动",100),
    HF_CARD_COLLECTING_ACT("20","集卡活动",100),
    HF_SIGN_ACT("21","签到活动",100),
    HF_GOOD_ANCHOR_ACT("22","好主播活动",100),
    HF_LOCATION_SIGN_ACT("23","地点打卡活动",100),
    HF_SIGN_UP_ACT("24","报名活动",100)
    ;
    //华发项目

    private String id;
    private String value;
    private int sort;

    public static String getValueById(String id) {

        return Arrays.stream(StrategyGroupEnum.values())
                .filter(val -> val.getId().equals(id))
                .findFirst()
                .map(StrategyGroupEnum::getValue)
                .orElse("该活动类型无对应的活动");

    }

    public static StrategyGroupEnum getById(String id) {
        return Arrays.stream(StrategyGroupEnum.values())
                .filter(val -> val.getId().equals(id))
                .findFirst()
                .orElse(MJS_G);
    }
}
