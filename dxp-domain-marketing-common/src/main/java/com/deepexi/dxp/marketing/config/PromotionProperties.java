//package com.deepexi.dxp.marketing.config;
//
//import lombok.Data;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.stereotype.Component;
//
///**
// * @description：
// * @author：z<PERSON><PERSON><PERSON>
// * @version：1.0.0
// * @date：2021-03-20 4:19 下午
// */
//@Component
//@Configuration
//@ConditionalOnProperty(prefix = "deepexi.promotion", value = "enabled", havingValue = "true")
//@ConfigurationProperties(prefix = "deepexi.promotion")
//@Data
//public class PromotionProperties {
//
//    /**
//     * 提前预告多少小时搜出活动
//     */
//    private Integer foreshowHour;
//
//}
