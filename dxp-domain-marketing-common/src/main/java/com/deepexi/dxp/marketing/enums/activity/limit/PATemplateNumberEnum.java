package com.deepexi.dxp.marketing.enums.activity.limit;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 活动数量限制枚举类
 *
 * <AUTHOR> xianfeng.cai
 * @date created in 17:29 2019/11/26
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PATemplateNumberEnum implements BaseLimit {

    /**
     * 异常值
     */
    ERROR("error", "error"),
    /**
     * 单日次数限制
     */
    DRCS("dayNum", "dayNum"),
    /**
     *
     */
    ZCS("totalNum", "totalNum") {},
    /**
     * 商品限购数 commodityNum
     */
    COMMODITY_NUM("commodityNum", "commodityNum");

    @Override
    public BaseLimit getById(String id) {
        for (PATemplateNumberEnum paTemplateNumberEnum : PATemplateNumberEnum.values()) {
            if (paTemplateNumberEnum.getId().equals(id)) {
                return paTemplateNumberEnum;
            }
        }

        return PATemplateNumberEnum.ERROR;
    }

    public static BaseLimit getByValue(String value) {
        for (PATemplateNumberEnum paTemplateNumberEnum : PATemplateNumberEnum.values()) {
            if (paTemplateNumberEnum.getValue().equals(value)) {
                return paTemplateNumberEnum;
            }
        }

        return PATemplateNumberEnum.ERROR;
    }

    @Override
    public Boolean isEqual(String id) {
        //TODO
        return true;
    }


    private String id;
    private String value;
}
