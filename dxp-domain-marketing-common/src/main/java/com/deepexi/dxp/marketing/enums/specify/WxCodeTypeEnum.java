package com.deepexi.dxp.marketing.enums.specify;

import lombok.Getter;

/**
 * 创建微信小程序码 类型
 */
@Getter
public enum WxCodeTypeEnum {

//    ORGANIZATION_CODE(1,"组织码"),
//    MINI_PROGRAM_PC(2,"拓客码"),
//    LIVE_CODE(3,"直播间海报码"),
    SIGN_CODE(4,"签到码"),
    //以上废弃

    MINI_PROGRAM_PC(13,"微信小程序"),
    WEBCHAT_WEBSITE(8,"H5"),//微信内网页
    ;

    private final Integer id;
    private final String value;

    WxCodeTypeEnum(Integer id, String value) {
        this.id = id;
        this.value = value;
    }
}
