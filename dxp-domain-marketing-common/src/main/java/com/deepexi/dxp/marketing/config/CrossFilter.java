package com.deepexi.dxp.marketing.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021/5/27 19:09
 */

public class CrossFilter implements Filter {

    private static final Logger log = LoggerFactory.getLogger(CrossFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        String allowOrigin = request.getHeader("Origin");
        String allowMethods = "POST, GET, OPTIONS, PUT, DELETE, HEAD";
        String allowHeaders = "Origin,No-Cache, X-Requested-With, If-Modified-Since, Pragma, Last-Modified,Cache-Control" +
                ", Expires, Content-Type, X-E4M-With, X-Requested-With, X-Prototype-Version, Authorization";
        response.addHeader("Access-Control-Allow-Credentials", "true");
        response.addHeader("Access-Control-Allow-Headers", allowHeaders);
        response.addHeader("Access-Control-Allow-Methods", allowMethods);
        response.addHeader("Access-Control-Allow-Origin", allowOrigin);
//        log.info("crossFilter before");
        filterChain.doFilter(servletRequest, servletResponse);
//        log.info("crossFilter after");
    }

    @Override
    public void destroy() {
    }
}

