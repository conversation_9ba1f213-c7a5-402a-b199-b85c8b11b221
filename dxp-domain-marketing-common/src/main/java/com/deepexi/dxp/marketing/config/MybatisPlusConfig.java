package com.deepexi.dxp.marketing.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.constant.CommonConstant;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.StringTypeHandler;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
@MapperScan({"com.deepexi.dxp.middle.*.mapper"})
public class MybatisPlusConfig {

    /**
     * 自动填充配置
     *
     * @return
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                this.strictInsertFill(metaObject, "createdTime", Date.class, new Date());
                this.strictInsertFill(metaObject, "updatedTime", Date.class, new Date());
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                this.strictUpdateFill(metaObject, "updatedTime", Date.class, new Date());
            }
        };
    }

    /**
     * 新版的插件配置
     *
     * @return
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();


        // 多租户插件
//        TenantLineInnerInterceptor tenantLineInnerInterceptor = new TenantLineInnerInterceptor();
//        tenantLineInnerInterceptor.setTenantLineHandler(new TenantLineHandler() {
//            @Override
//            public Expression getTenantId() {
//                // 需要从用户上下文获取当前的租户ID
//                String tenantId = AppRuntimeEnv.getTenantId();
//                if (StringUtils.isBlank(tenantId)) {
//                    throw new IllegalArgumentException("tenantId is null");
//                }
//                return new StringValue(tenantId);
//            }
//
//            @Override
//            public String getTenantIdColumn() {
//                return "tenant_id";
//            }
//
//            @Override
//            public boolean ignoreTable(String tableName) {
//                // 配置忽略多租户的表
//                if (CommonConstant.IGNORE_TENANT_TABLES.contains(tableName)) {
//                    return true;
//                }
//                return false;
//            }
//        });

//        mybatisPlusInterceptor.addInnerInterceptor(tenantLineInnerInterceptor);


        // 分页插件
        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor());

        // 乐观锁插件
        mybatisPlusInterceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        return mybatisPlusInterceptor;
    }

}