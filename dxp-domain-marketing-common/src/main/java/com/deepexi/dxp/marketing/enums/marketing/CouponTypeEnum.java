package com.deepexi.dxp.marketing.enums.marketing;

import lombok.Getter;

/**
 * 优惠券类型
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-24 10:51
 */
@Getter
public enum CouponTypeEnum {
    /**
     * 折扣券
     */
    DISCOUNT_COUPON("0", "折扣券"),
    /**
     * 代金券
     */
    CASH_COUPON("1", "代金券");

    private final String id;

    private final String value;

    CouponTypeEnum(String id, String value) {
        this.id = id;
        this.value = value;
    }

    public static String getValueById(String id) {
        CouponTypeEnum[] businessModeEnums = values();
        for (CouponTypeEnum businessModeEnum : businessModeEnums) {
            if (businessModeEnum.getId().equals(id)) {
                return businessModeEnum.getValue();
            }
        }
        return null;
    }

}
