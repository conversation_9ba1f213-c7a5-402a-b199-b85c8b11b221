package com.deepexi.dxp.marketing.enums.activity.strategy;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date：2020/4/28
 * @version: V1.0
 * @slogan: 天下风云出我辈，一入代码岁月催
 * @description :
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum LoggerCommodityEnum {
    /**
     * 订单赠品
     */
    GIFT("giftCommodity", "订单赠品"),
    /**
     * 订单商品
     */
    ORDER("orderCommodity", "订单商品");
    private String id;
    private String value;
}
