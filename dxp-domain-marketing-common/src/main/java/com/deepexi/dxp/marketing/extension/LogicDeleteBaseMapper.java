package com.deepexi.dxp.marketing.extension;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;

public interface LogicDeleteBaseMapper<T> extends BaseMapper<T> {
    /**
     * 批量逻辑删除并填充字段
     *
     * @param idList
     * @param entity
     * @return
     */
    int batchDeleteWithFill(@Param(Constants.COLLECTION) Collection<? extends Serializable> idList, @Param(Constants.ENTITY) T entity);

}
