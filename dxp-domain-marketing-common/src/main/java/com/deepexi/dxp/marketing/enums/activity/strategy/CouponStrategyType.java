package com.deepexi.dxp.marketing.enums.activity.strategy;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/27 20:55
 */

@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
public enum CouponStrategyType {
    /**
     * 满减活动策略类型
     */
    WMK("wml", "无门槛"),
    JEMJ("money", "按金额满减");
    private String id;
    private String value;
}
