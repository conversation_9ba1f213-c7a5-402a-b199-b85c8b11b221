package com.deepexi.dxp.marketing.enums.activity.limit;

import cn.hutool.core.collection.ListUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MemberTypeEnum {

    /**
     * 活动模板会员限制
     */
    ALL("1", "全部会员"),
    OWNER("2", "全国业主"),
    PROJECT_OWNER("3", "指定项目业主"),
    WHITE_LIST("4", "活动白名单方式"),
    INDEPENDENT_AGENT("10", "独立经纪人"),
    ORGANIZATION("20", "机构"),
    HF_HOUSE_HOST("30", "华发业主"),
    HF_EMPLOYEE("40", "华发员工"),
    SALE_ADVISER("50", "拓客团队")
    ;

    private String id;
    private String value;

    public static boolean isAgent(String id){
        return getAgentList().contains(id);
    }

    public static List<String> getAgentList(){
        return ListUtil.toList(INDEPENDENT_AGENT.getId(), ORGANIZATION.getId(), HF_HOUSE_HOST.getId(), HF_EMPLOYEE.getId(), SALE_ADVISER.getId());
    }

    public static String getAgentTypeName(String agentType) {
        for (MemberTypeEnum value : values()) {
            if (value.getId().equals(agentType)) {
                return value.getValue();
            }
        }
        return null;
    }
}