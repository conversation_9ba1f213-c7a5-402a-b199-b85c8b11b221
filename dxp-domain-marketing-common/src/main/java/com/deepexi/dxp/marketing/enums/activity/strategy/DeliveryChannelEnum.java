package com.deepexi.dxp.marketing.enums.activity.strategy;

/**
 * <AUTHOR>
 * @description: 发布渠道枚举 投放渠道:0-置业通小程序展示,2-抖音小程序展示,4-客服小程序
 * @date 2024/8/9 10:51
 */
public enum DeliveryChannelEnum {
    ZYT("0", "置业通小程序"),
    DYT("2", "抖音小程序"),
    KF("4", "客服小程序"),
    SQ("6", "社群小程序");

    private String id;
    private String value;

    DeliveryChannelEnum(String id, String value) {
        this.id = id;
        this.value = value;
    }

    public String getId() {
        return id;
    }

    public String getValue() {
        return value;
    }
}
