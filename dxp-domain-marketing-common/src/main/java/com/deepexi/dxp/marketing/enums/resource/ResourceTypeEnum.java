package com.deepexi.dxp.marketing.enums.resource;

import lombok.Getter;

/**
 * 资源类型枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2020-06-02 11:27
 */
@Getter
public enum ResourceTypeEnum {
    /**
     * 资源类型
     */
    DR1("DR1","促销资源-DR活动"),
    HAO_WAN("24-1","互动资源-24好玩活动"),
    HAO_WAN_GAME("100","互动游戏");
    private final String id;
    private final String value;

    ResourceTypeEnum(String id, String value){
        this.id = id;
        this.value = value;
    }

}
