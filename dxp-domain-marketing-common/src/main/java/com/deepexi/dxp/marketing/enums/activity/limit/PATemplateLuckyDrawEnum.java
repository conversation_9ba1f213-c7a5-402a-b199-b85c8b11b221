package com.deepexi.dxp.marketing.enums.activity.limit;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date created in 2021-05-21 11:29
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PATemplateLuckyDrawEnum {

    /**
     * 抽奖限制
     */
    JOINTYPE("joinType", "参与类型"),
    PER_DRAW_NUMBER("perDrawNumber","每人或者每天 可抽奖次数"),
    PER_SHARE_NUMBER("perShareNumber","每人或者每天 可分享次数"),
    SHARE_DRAW_NUMBER("shareDrawNumber","分享后获得抽奖次数"),
    GET_DRAW_NUMBER_REG_AGENT("1","活动期间注册经纪人"),
    GET_DRAW_NUMBER_REPORT("2","活动期间推荐"),
    GET_DRAW_NUMBER_DEAL("3","活动期间推荐且成交"),
    PER_GET_DRAW_NUMBER("perGetDrawNumber","活动期间推荐每次获得的抽奖次数,如：每次推荐获取5次"),
    TOTAL_GET_NUMBER("totalGetNumber","活动期间推荐总共获得机会次数，如：最多可推荐4次（实际可获得20次抽奖）"),
    ;
    private String id;
    private String value;
}
