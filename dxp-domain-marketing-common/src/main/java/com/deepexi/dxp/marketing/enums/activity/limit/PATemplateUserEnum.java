package com.deepexi.dxp.marketing.enums.activity.limit;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/10/28 16:33
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PATemplateUserEnum {

    /**
     * 活动模板会员限制
     */
    QBHY("allMember", "全部会员") {

    }, HYFZ("memberGroup", "会员分组") {

    }, BQQR("tag", "标签圈人") {

    }, ZDHY("assignMember", "指定会员") {

    }, HYLX("memberType", "会员类型") {

    };

    private String id;
    private String value;
}