package com.deepexi.dxp.marketing.enums.marketing;

import lombok.Getter;

/**
 * 圈选用户类型枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2020-11-09 11:05
 */
@Getter
public enum MarketingTaskActiveTargetUsersTypeEnum {
    /**
     *
     */
    USER(1, "用户"),
    /**
     * 代金券
     */
    MEMBER(2, "会员"),

    FANS(3, "粉丝");

    private Integer id;

    private String value;

    MarketingTaskActiveTargetUsersTypeEnum(Integer id, String value) {
        this.id = id;
        this.value = value;
    }

}
