package com.deepexi.dxp.marketing.enums.marketing;


import lombok.Getter;

/**
 * 任务状态控制状态
 * <AUTHOR>
 * @Date 2020/4/2
 */
@Getter
public enum TaskControlStatusEnum {

    /**
     * 未生效
     */
    INVALID(1, "未生效"),

    /**
     * 启动中
     */
    STARTING(2, "启动中"),

    /**
     * 启动成功
     */
    RUNNING(3, "运行中"),

    /**
     * 暂停中
     */
    STOPPING(4, "暂停中"),

    /**
     * 已暂停
     */
    STOPPED(5, "已暂停");

    private final Integer value;

    private final String label;

    TaskControlStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public static TaskControlStatusEnum getEnumByValue(Integer value) {
        TaskControlStatusEnum[] items = values();
        for (TaskControlStatusEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }
}
