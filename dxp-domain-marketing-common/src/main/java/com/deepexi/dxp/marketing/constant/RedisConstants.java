package com.deepexi.dxp.marketing.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2021/5/6 19:52
 */
@Configuration
public class RedisConstants {

    //======================= 符号 ================================
    //环境标识前缀
    public static String ENV_FLAG;

    //秒杀活动信息前缀
    public static final String UNDER_LINE = "_";

    @Value("${spring.profiles.active}")
    public void setEnvFlag(String envFlag) {
        ENV_FLAG = envFlag + UNDER_LINE;
        //活动信息前缀（使用RBucket存储）
        CACHE_PREV_KEY_ACT_INFO = ENV_FLAG+"activity_service_act_info:";
        //获取进行中活动列表及缓存key
        CACHE_PREV_KEY_RUNNINGACT_INFO = ENV_FLAG+"activity_service_running_act_info:";
        CACHE_PREV_KEY_RUNNINGACT_INFO_KEYS = ENV_FLAG+"activity_service_running_act_info_keys";
        //活动创建前缀+用户名称
        CACHE_PREV_KEY_ACT_CREATE_INFO_REPEAT = ENV_FLAG+"activity_service_act_create_repeat:%s";
        //用户项目信息-创建前缀+userId
        CACHE_PREV_KEY_USER_PROJECT_INFO = ENV_FLAG+"activity_service_user_project_info:%s";
        //是否置业通项目缓存信息
        CACHE_JUMPABLE_PROJECT_INFO = ENV_FLAG+"activity_jumpable_project_info";
        //用户领奖信息-创建前缀+userId
        CACHE_PREV_KEY_USER_RESOURCE_MAP = ENV_FLAG+"activity_service_user_resource_map:";
        CACHE_PREV_KEY_USER_RESOURCE_SET = ENV_FLAG+"activity_service_user_resource_set:";
        CACHE_PREV_KEY_USER_MONEY_LIMIT = ENV_FLAG+"activity_service_user_money_limit:";
        //用户参加活动次数前缀（用户参与活动的次数）(使用RAtomicLong存储，使用phone+activityId作为名称)
        CACHE_PREV_KEY_ACT_USER_PARTAKE_COUNT = ENV_FLAG+"activity_service_user_partake_count:";
        //用户领取奖品次数前缀（用户领取奖品次数）(使用RAtomicLong存储，使用phone+hisResourceId作为名称)
        CACHE_PREV_KEY_ACT_USER_GET_RESOURCE_COUNT = ENV_FLAG+"activity_service_user_get_resource_count:";
        //用户填写信息登记次数前缀（用户填写信息登记次数）(使用RAtomicLong存储，使用phone+activityId作为名称)
        CACHE_PREV_KEY_ACT_USER_FEEDBACK_COUNT = ENV_FLAG+"activity_service_user_feedback_count:";
        //用户未支付订单前缀（用户未支付订单）(使用RBucket存储，使用phone+activityId+resourceId作为名称)
        CACHE_PREV_KEY_USER_UNPAY_ORDER = ENV_FLAG+"activity_service_user_unpay_order:";
        //资源缓存前缀（使用RMap存储，map使用act_id作为名称，key使用hisResourceId）
        CACHE_PREV_KEY_ACT_RESOURCE_INFO = ENV_FLAG+"activity_service_resource_info:";
        //资源数量缓存前缀（使用RBucket存储，使用hisResourceId作为名称）
        CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT = ENV_FLAG+"activity_service_resource_info_count:";
        // 马拉松参赛码缓存集合
        CACHE_PREV_KEY_ACT_MARATHON_CODE = ENV_FLAG+"activity_service_marathon_code:%s";
        // 马拉松用户手机号缓存集合
        CACHE_PREV_KEY_ACT_MARATHON_PHONE = ENV_FLAG+"activity_service_marathon_phone:%s";
        //资源领取重复领取缓存前：用户id+活动id+资源id>>>用户手机号+活动id+资源id
        CACHE_PREV_KEY_ACT_RESOURCE_INFO_REPEAT = ENV_FLAG+"activity_service_resource_info_repeat:%s:%s:%s";
        // 活动限制用户点击作品频率：活动id+用户id+作品id
        CACHE_PREV_KEY_ACT_SUBMIT_REPEAT = ENV_FLAG+"activity_user_submit_repeat:%s:%s:%s";
        //全局订单号累计
        CACHE_PREV_KEY_ORDER_NUMBER = ENV_FLAG+"activity_service_order_number";
        //全局资源券码累计
        CACHE_PREV_KEY_RESOURCE_CODE_NUMBER = ENV_FLAG+"activity_service_resource_code_number";
        // 好主播活动点赞缓存key
        CACHE_PREV_KEY_ACT_GOOD_ANCHOR_LIKES_LIMIT = ENV_FLAG + "activity_act_good_anchor_likes_limit:%s";
        //活动分析自定义指标记录前缀+用户名称+活动id
        ACTIVITY_ANALYSIS_OVERVIEW_OF_INDICATORS_RECORD = ENV_FLAG+"activity_analysis_overview_of_indicators_record:%s:%s";
        //分布式锁 参加活动
        CACHE_PREV_KEY_ACT_PARTAKE_LOCK = ENV_FLAG+"activity_service_act_partake_lock:";
        //分布式锁 活动信息
        CACHE_PREV_KEY_ACT_INFO_LOCK = ENV_FLAG+"activity_service_act_info_lock:";
        //分布式锁 资源数量
        CACHE_PREV_KEY_RESOURCE_QTY_LOCK = ENV_FLAG+"activity_service_resource_qty_lock:";
        //分布式锁 下单回调
        CACHE_PREV_KEY_ORDER_BACK_LOCK = ENV_FLAG+"activity_service_order_back_lock:";
        //分布式锁 退款回调
        CACHE_PREV_KEY_REFUND_BACK_LOCK = ENV_FLAG+"activity_service_refund_back_lock:";
        //分布式锁 裂变活动-系统判断活动有效期结束，统一发放奖品
        CACHE_PREV_KEY_FISSON_ACT_RESOURCE_LOCK = ENV_FLAG+"activity_service_fissson_act_resource_lock:";
        //分布式锁 裂变活动-砍价活动-发放奖品
        CACHE_PREV_KEY_FISSION_REDUCEPRICE_ACT_RESOURCE_LOCK = ENV_FLAG+"activity_service_fisson_reduceprice_act_resource_lock:";
        //手机验证码缓存键值（使用RBucket存储，使用phone作为名称）
        CACHE_PREV_KEY_PHONE_VERIFY_CODE = ENV_FLAG+"activity_service_phone_verify_code:";
        //参与用户白名单
        CACHE_PREV_KEY_WHITELIST = ENV_FLAG+"activity_whiteList:";
        LUCKY_DRAW_ADD_DRAW_NUMBER = ENV_FLAG+"activity_addDrawNumber:";
    }

    //======================= 缓存相关 ================================
    //活动信息前缀（使用RBucket存储）
    public static  String CACHE_PREV_KEY_ACT_INFO;
    public static  String CACHE_PREV_KEY_WHITELIST;
    public static  String LUCKY_DRAW_ADD_DRAW_NUMBER;
    public static  String CACHE_PREV_KEY_RUNNINGACT_INFO;
    public static  String CACHE_PREV_KEY_RUNNINGACT_INFO_KEYS;

    //活动创建前缀+用户名称
    public  static String CACHE_PREV_KEY_ACT_CREATE_INFO_REPEAT;

    //用户项目信息-创建前缀+userId
    public  static String CACHE_PREV_KEY_USER_PROJECT_INFO;
    public  static String CACHE_JUMPABLE_PROJECT_INFO;

    //用户领奖信息-创建前缀+userId
    public  static String CACHE_PREV_KEY_USER_RESOURCE_MAP;
    public  static String CACHE_PREV_KEY_USER_RESOURCE_SET;
    public  static String CACHE_PREV_KEY_USER_MONEY_LIMIT;


    //用户参加活动次数前缀（用户参与活动的次数）(使用RAtomicLong存储，使用phone+activityId作为名称)
    public static  String CACHE_PREV_KEY_ACT_USER_PARTAKE_COUNT;

    //用户领取奖品次数前缀（用户领取奖品次数）(使用RAtomicLong存储，使用phone+hisResourceId作为名称)
    public static  String CACHE_PREV_KEY_ACT_USER_GET_RESOURCE_COUNT;

    //用户填写信息登记次数前缀（用户填写信息登记次数）(使用RAtomicLong存储，使用phone+activityId作为名称)
    public static  String CACHE_PREV_KEY_ACT_USER_FEEDBACK_COUNT;

    //用户未支付订单前缀（用户未支付订单）(使用RBucket存储，使用phone+activityId+resourceId作为名称)
    public static  String CACHE_PREV_KEY_USER_UNPAY_ORDER;

    //资源缓存前缀（使用RMap存储，map使用act_id作为名称，key使用hisResourceId）
    public  static String CACHE_PREV_KEY_ACT_RESOURCE_INFO;

    //资源数量缓存前缀（使用RBucket存储，使用hisResourceId作为名称）
    public  static String CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT;

    // 马拉松参赛码缓存集合
    public  static String CACHE_PREV_KEY_ACT_MARATHON_CODE;

    // 马拉松用户手机号缓存集合
    public  static String CACHE_PREV_KEY_ACT_MARATHON_PHONE;

    //资源领取重复领取缓存前：用户id+活动id+资源id>>>用户手机号+活动id+资源id
    public  static String CACHE_PREV_KEY_ACT_RESOURCE_INFO_REPEAT;

    // 活动限制用户点击作品频率：活动id+用户id+作品id
    public  static String CACHE_PREV_KEY_ACT_SUBMIT_REPEAT;

    //全局订单号累计
    public static  String CACHE_PREV_KEY_ORDER_NUMBER;

    //全局资源券码累计
    public static  String CACHE_PREV_KEY_RESOURCE_CODE_NUMBER;

    // 好主播活动点赞缓存key
    public static  String CACHE_PREV_KEY_ACT_GOOD_ANCHOR_LIKES_LIMIT;

    //活动分析自定义指标记录前缀+用户名称+活动id
    public  static String ACTIVITY_ANALYSIS_OVERVIEW_OF_INDICATORS_RECORD;

    //======================= 缓存过期时间 ================================
    //如果是秒杀活动，暂时没有结束时间，则规定三个月过期
    public  static Long SECKILL_EXPIRE_SECOND = 60*60*24L*30*3;

    //额外添加过期时间 12小时转换成秒
    public  static Long EXTRA_EXPIRE_SECOND = 60*60*12L;

    //======================= 分布式缓存前缀 ================================

    //分布式锁 参加活动
    public static  String CACHE_PREV_KEY_ACT_PARTAKE_LOCK;

    //分布式锁 活动信息
    public static  String CACHE_PREV_KEY_ACT_INFO_LOCK;

    //分布式锁 资源数量
    public static  String CACHE_PREV_KEY_RESOURCE_QTY_LOCK;

    //分布式锁 下单回调
    public static  String CACHE_PREV_KEY_ORDER_BACK_LOCK;

    //分布式锁 退款回调
    public static  String CACHE_PREV_KEY_REFUND_BACK_LOCK;

    //分布式锁 裂变活动-系统判断活动有效期结束，统一发放奖品
    public static  String CACHE_PREV_KEY_FISSON_ACT_RESOURCE_LOCK;

    //分布式锁 裂变活动-砍价活动-发放奖品
    public static  String CACHE_PREV_KEY_FISSION_REDUCEPRICE_ACT_RESOURCE_LOCK;

    //手机验证码缓存键值（使用RBucket存储，使用phone作为名称）
    public static  String CACHE_PREV_KEY_PHONE_VERIFY_CODE;
}
