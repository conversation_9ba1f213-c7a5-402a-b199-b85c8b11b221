package com.deepexi.dxp.marketing.utils;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;

/**
 * List复制工具类
 */
public class ListUtils<T> {

    public void copyList(Object obj, List<T> targetList, Class<T> classObj) {
        if ((!Objects.isNull(obj)) && (!Objects.isNull(targetList))) {
            List sourceList = (List) obj;
            sourceList.forEach(item -> {
                try {
                    T data = classObj.newInstance();
                    BeanUtils.copyProperties(item, data);
                    targetList.add(data);
                } catch (InstantiationException e) {
                } catch (IllegalAccessException e) {
                }
            });
        }
    }

    /**
     * 将DTO集合复制到VO集合里并返回VO集合
     */
    public static <VO extends SuperVO,DTO extends SuperDTO> List<VO> copyListDTO2VO(Class<VO> clazz, List<DTO> dtoList){
        List<VO> voEntityList = Lists.newArrayList();
        ListUtils<VO> listUtils = BeanUtils.instantiateClass(ListUtils.class);
        listUtils.copyList(dtoList, voEntityList, clazz);
        return voEntityList;
    }

    /**
     * 将VO集合复制到DTO集合里并返回DTO集合
     */
    public static <VO extends SuperVO,DTO extends SuperDTO> List<DTO> copyListVO2DTO(Class<DTO> clazz, List<VO> voList){
        List<DTO> dtoEntityList = Lists.newArrayList();
        ListUtils<DTO> listUtils = BeanUtils.instantiateClass(ListUtils.class);
        listUtils.copyList(voList, dtoEntityList, clazz);
        return dtoEntityList;
    }

}
