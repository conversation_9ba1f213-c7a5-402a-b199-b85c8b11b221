package com.deepexi.dxp.marketing.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * @description：
 * @author：<PERSON><PERSON><PERSON><PERSON>
 * @version：1.0.0
 * @date：2021-03-20 4:19 下午
 */
@Component
@Configuration
@ConfigurationProperties(prefix = "deepexi")
@Data
public class CommonProperties {

    /*********************【分布式ID】*********************/
    private String distributeIdAddress;



}
