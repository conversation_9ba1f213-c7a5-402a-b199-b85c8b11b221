package com.deepexi.dxp.marketing.enums.activity.strategy.operation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ming.zhong
 * @date created in 14:52 2019/11/27
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MJYHOperationEnum {
    /**
     * 满减优惠的金额计算
     */
    JE("money", "减多少金额");
    private String id;
    private String value;
}
