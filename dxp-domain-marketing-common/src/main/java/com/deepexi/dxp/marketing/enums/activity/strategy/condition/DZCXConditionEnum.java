package com.deepexi.dxp.marketing.enums.activity.strategy.condition;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ming.zhong
 * @date created in 21:07 2019/11/26
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum DZCXConditionEnum {
    /**
     * 打折促销的条件限制枚举
     */
    AJS("number", "购买的件数"),
    AJE("money", "达到的金额"),
    WMK("wmk", "无门槛");
    private String id;
    private String value;
}
