package com.deepexi.dxp.marketing.enums.resource;

import lombok.Getter;

/**
 * 资源渠道
 * <AUTHOR>
 * @version 1.0
 * @date 2020-06-04 9:57
 */
@Getter
public enum ResourceChannelEnum {
    /**
     * 资源渠道
     */
    DR("DR","DR渠道"),
    HAO_WAN("24好玩","24好玩渠道"),
    ;
    private final String id;
    private final String value;

    ResourceChannelEnum(String id, String value){
        this.id = id;
        this.value = value;
    }

}
