package com.deepexi.dxp.marketing.enums.dolphinscheduler;


import lombok.Getter;

/**
 * 流程实例执行状态 枚举
 * <AUTHOR>
 * @Date 2020/3/30
 */
@Getter
public enum InstanceExecuteStatusEnum {
    RUNNING_EXEUTION("RUNNING_EXEUTION", "运行中"),

    /**
     * 执行成功
     */
    SUCCESS("SUCCESS", "执行成功"),

    /**
     * 执行失败
     */
    FAILURE("FAILURE", "执行失败");

    private final String value;

    private final String label;

    InstanceExecuteStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static InstanceExecuteStatusEnum getEnumByValue(String value) {
        InstanceExecuteStatusEnum[] items = values();
        for (InstanceExecuteStatusEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}
