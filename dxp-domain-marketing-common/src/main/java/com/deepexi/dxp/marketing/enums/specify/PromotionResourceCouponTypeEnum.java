package com.deepexi.dxp.marketing.enums.specify;

import lombok.Getter;

/**
 * 卡劵类型
 */
@Getter
public enum PromotionResourceCouponTypeEnum {
    VOUCHER(0,"代金券"),
    DISCOUNT_COUPON(1,"折扣劵"),
    ;

    private final Integer id;
    private final String value;

    PromotionResourceCouponTypeEnum(Integer id, String value) {
        this.id = id;
        this.value = value;
    }
    public static String getValueById(Integer id) {
        PromotionResourceCouponTypeEnum[] enums = values();
        for (PromotionResourceCouponTypeEnum enum1 : enums) {
            if (enum1.getId().equals(id)) {
                return enum1.getValue();
            }
        }
        return null;
    }
}
