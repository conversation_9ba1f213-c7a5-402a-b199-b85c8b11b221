package com.deepexi.dxp.marketing.enums.usertype;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/25 16:24
 */
public enum UserTypeEnum {
    /**
     * 用户的类型 比如会员 ,账号
     */
    MEMBER("0", "会员用户");
    private String id;
    private String value;

    UserTypeEnum(String id, String value) {
        this.id = id;
        this.value = value;
    }

    public String getId() {
        return id;
    }


    public String getValue() {
        return value;
    }

}
