package com.deepexi.dxp.marketing.enums.resource;

import lombok.Getter;

/**
 * 用户类型
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/10/28 16:33
 */
@Getter
public enum PATemplateUserEnum {

    /**
     * 活动模板会员限制
     */
    QBHY("allMember", "全部会员") {

    }, HYFZ("memberGroup", "会员分组") {

    }, BQQR("tag", "标签圈人") {

    }, ZDHY("assignMember", "指定会员") {

    };

    private final String id;
    private final String value;

    PATemplateUserEnum(String id, String value) {
        this.id = id;
        this.value = value;
    }
}