package com.deepexi.dxp.marketing.enums.activity.limit;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date created in 2021-05-21 11:29
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PATemplateFeedbackInfoEnum {

    /**
     * 信息登记限制
     */
    INFO("info","内容模板内容"),
    ;
    private String id;
    private String value;
}
