package com.deepexi.dxp.marketing.extension;

/**
 * 应用运行时上下文
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020-09-02 14:42
 */
public class MarketingContext {

    private static final ThreadLocal<String> TOKEN = new ThreadLocal<>();

    public static void setToken(String token) {
        TOKEN.set(token);
    }

    public static String getToken() {
        return TOKEN.get();
    }

    /**
     * 清空上下文信息
     */
    public static void clear() {
        TOKEN.remove();
    }
}