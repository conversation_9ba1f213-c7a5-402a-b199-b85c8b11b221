package com.deepexi.dxp.marketing.enums.activity.limit;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/27 15:29
 */
@Getter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public enum PATemplateBaseEnum {
    /**
     * 活动限制类型
     */
    COMMODITY(1, "商品限制"),
    COUPON(2, "优惠券限制"),
    NUMBER(3, "数量限制"),
    TENANT(4, "渠道限制"),
    USER(5, "用户限制"),
    SHOP(6, "门店限制"),

    //==========华发===========
    LUCKYDRAW(7,"抽奖限制"),
    BARGAIN(8,"砍价限制"),
    ASSIST(9,"助力限制"),
    FORM(10,"表单限制"),
    CARD_COLLECTING(11,"集卡限制"),
    //==========华发===========
    ;
    private Integer id;
    private String value;
}
