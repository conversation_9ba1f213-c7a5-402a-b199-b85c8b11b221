package com.deepexi.dxp.marketing.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2021/5/27 19:08
 */
@Configuration
public class CorsConfig {

    /**
     * 增加跨域的过滤器
     * @return 返回跨域过滤器
     */
    @Bean
    public FilterRegistrationBean<Filter> crossFilter() {
        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new CrossFilter());
        registrationBean.setUrlPatterns(Arrays.asList("/*"));
        registrationBean.setName("crossFilter");
        registrationBean.setOrder(-1000);
        return registrationBean;
    }

}
