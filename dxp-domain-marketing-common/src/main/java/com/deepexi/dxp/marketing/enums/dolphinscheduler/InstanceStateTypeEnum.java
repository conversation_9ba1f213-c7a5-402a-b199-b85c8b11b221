package com.deepexi.dxp.marketing.enums.dolphinscheduler;


import lombok.Getter;

/**
 * 工作流和任务节点的运行状态 枚举
 * SUBMITTED_SUCCESS,
 * RUNNING_EXEUTION,
 * READY_PAUSE,
 * PAUSE,
 * READY_STOP,
 * STOP,
 * FAILURE,
 * SUCCESS,
 * NEED_FAULT_TOLERANCE,
 * KILL,
 * WAITTING_THREAD,
 * WAITTING_DEPEND
 * <AUTHOR>
 * @Date 2020/3/31
 */
@Getter
public enum InstanceStateTypeEnum {
    /**
     * 执行中
     */
    RUNNING_EXEUTION("RUNNING_EXEUTION", "执行中"),
    /**
     * 停止
     */
    STOP("STOP", "停止");

    private final String value;

    private final String label;

    InstanceStateTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static InstanceStateTypeEnum getEnumByValue(String value) {
        InstanceStateTypeEnum[] items = values();
        for (InstanceStateTypeEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }

}
