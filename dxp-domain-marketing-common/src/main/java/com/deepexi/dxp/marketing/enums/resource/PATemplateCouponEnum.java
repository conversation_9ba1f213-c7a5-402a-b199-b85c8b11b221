package com.deepexi.dxp.marketing.enums.resource;


import lombok.Getter;

/**
 * 活动模板渠道限制
 * <AUTHOR> ming.zhong
 * @date created in 19:18 2019/11/25
 */
@Getter
public enum PATemplateCouponEnum {
    COUPONFLAG ("couponFlag", "是否使用优惠券"),
    COUPON_ID ("couponId", "关联优惠券id"),
    COUPON_NUMBER ("couponNumber", "发放数量");

    private final String id;
    private final String value;

    PATemplateCouponEnum(String id, String value) {
        this.id = id;
        this.value = value;
    }
}
