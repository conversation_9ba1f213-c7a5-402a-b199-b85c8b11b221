package com.deepexi.dxp.marketing.enums.activity.strategy.condition;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ming.zhong
 * @date created in 17:35 2019/12/3
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum TimeConditionEnum {
    /**
     * 优惠券状态
     */
    WXZ("wmk", "无限制"),
    ORDER_TIME("orderTime", "指定时间范围"),
    SINCE_GET("sinceGet", "自领取当天起");
    private String id;
    private String value;

}
