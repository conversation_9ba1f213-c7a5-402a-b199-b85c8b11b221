package com.deepexi.dxp.marketing.enums.marketing;

import lombok.Getter;

/**
 * 优惠券类型后缀枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-24 11:11
 */
@Getter
public enum  CouponTypeSuffixEnum {
    /**
     * 折
     */
    DISCOUNT_COUPON("0", "折"),
    /**
     * 元
     */
    CASH_COUPON("1", "元");

    private final String id;

    private final String value;

    CouponTypeSuffixEnum(String id, String value) {
        this.id = id;
        this.value = value;
    }

    public static String getValueById(String id) {
        CouponTypeSuffixEnum[] businessModeEnums = values();
        for (CouponTypeSuffixEnum businessModeEnum : businessModeEnums) {
            if (businessModeEnum.getId().equals(id)) {
                return businessModeEnum.getValue();
            }
        }
        return null;
    }

}
