package com.deepexi.dxp.marketing.enums.activity.strategy.operation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> xianfeng.cai
 * @date created in 21:10 2019/11/27
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MZOperationEnum {
    /**
     * 赠品任选-件数
     */
    ZP_NUM("zp_num", "赠品任选-件数"),
    /**
     * 赠品任选-商品列表
     */
    ZP_COMMODITYS("zp_commoditys", "赠品任选-商品列表"),
    /**
     * 赠送优惠券
     */
    ZSYHQ("zsyhq", "赠送优惠券"),
    /**
     * 赠送积分
     */
    ZSJF("zsjf", "赠送积分");

    private String id;
    private String value;

}
