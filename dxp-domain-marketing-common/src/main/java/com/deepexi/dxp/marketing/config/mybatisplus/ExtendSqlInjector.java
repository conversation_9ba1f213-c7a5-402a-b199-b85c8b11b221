package com.deepexi.dxp.marketing.config.mybatisplus;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;

import java.util.List;

/**
 * 注入批量逻辑删除并且填充更新信息的字段
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/3 15:57
 */
public class ExtendSqlInjector extends DefaultSqlInjector {
 
    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass) {
       List<AbstractMethod> methodList = super.getMethodList(mapperClass);
        //methodList.add(new LogicBatchDeleteWithFill());
        return methodList;
    }
}