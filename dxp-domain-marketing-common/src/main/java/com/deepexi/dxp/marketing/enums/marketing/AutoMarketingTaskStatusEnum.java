package com.deepexi.dxp.marketing.enums.marketing;

import lombok.Getter;

/**
 * @author: zhang.yongwei
 * 自动运营状态(1:草稿，2:启动中，3:运行中，4:暂停，5:结束,6:停止中)
 *
 * 2：未开始——不是一个页面感知的状态，是提交flink任务之后等待flink返回响应之前的状态
 */
@Getter
public enum AutoMarketingTaskStatusEnum {

    DRAFT(1, "草稿"),

    STARTING(2, "启动中"),

    STARTFAIL(7, "启动失败"),

    RUNNING(3, "运行中"),

    STOPING(6, "暂停中"),

    STOPFAIL(8, "暂停失败"),

    SUSPEND(4, "暂停"),

    END(5, "终止"),

    ENDFAIL(9, "终止失败");

    private final int code;

    private final String value;

    AutoMarketingTaskStatusEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }
}
