package com.deepexi.dxp.marketing.enums.specify;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * ActivityTopicChannelInfoEnum 枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/30 11:15
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
public enum ActivityTopicChannelInfoEnum {

    //华发项目
//    HF_ACTIVITY_TOPIC("活动专题","src/pages/activity/pages/project/index",""),
    HF_ACTIVITY_TOPIC_LIST("活动专题-列表展示","src/pages/activity/pages/project/index","/#/src/pages/activity/pages/project/index"),
    HF_ACTIVITY_TOPIC_AREA("活动专题-区域展示","src/pages/activity/pages/project/area","/#/src/pages/activity/pages/project/area"),
    ;
    //华发项目

    private String actName;
    private String miniProgramPath;
    private String h5Path;

}
