package com.deepexi.dxp.marketing.constant;

/**
 * @Class: DolphinSchedulerConstant
 * @Description: DolphinScheduler常亮类
 * @Author: lizhong<PERSON>
 * @Date: 2020/3/26
 **/
public class DolphinSchedulerConstant {
    /**
     * 返回码-成功
     */
    public static final String CODE_SUCCESS = "0";

    /**
     * 授权信息redis缓存的key
     */
    public static final String KEY_DOLPHIN_SCHEDULER_AUTH = "dolphin_scheduler_auth";

    /**
     * 基础接口链接
     */
    private static final String PATH_BASE = "/dolphinscheduler";

    /**
     * 项目接口链接前缀
     */
    public static final String PATH_PROJECTS = PATH_BASE + "/projects/";

    /**
     * 登录接口链接
     */
    public static final String PATH_LOGIN = PATH_BASE + "/login";

    /**
     * 流程定义-新增 接口链接后缀
     */
    public static final String PATH_PROCESS_SAVE = "/process/save";

    /**
     * 流程定义-列表 接口链接后缀
     */
    public static final String PATH_PROCESS_PAGE = "/process/list-paging";

    /**
     * 流程定义-修改发布状态 接口链接后缀
     */
    public static final String PATH_PROCESS_RELEASE = "/process/release";

    /**
     * 运行流程 接口链接后缀
     */
    public static final String PATH_PROCESS_INSTANCE_START = "/executors/start-process-instance";

    /**
     * 流程实例-列表 接口链接后缀
     */
    public static final String PATH_PROCESS_INSTANCE_PAGE = "/instance/list-paging";

    /**
     * 流程定义-删除 接口链接后缀
     */
    public static final String PATH_PROCESS_DELETE = "/process/delete";

    /**
     * 流程实例-执行操作 接口链接后缀
     */
    public static final String PATH_PROCESS_INSTANCE_EXECUTE = "/executors/execute";
}
