package com.deepexi.dxp.marketing.enums.marketing;


import lombok.Getter;

/**
 * 营销任务类型(1:主动营销，2:自动营销)
 * @Author: HuangBo.
 * @Date: 2020/3/12 17:28
 */
@Getter
public enum MarketingTaskTypeEnum {

    /**
     * 主动营销
     */
    INITIATIVE(1, "主动营销"),

    /**
     * 自动营销
     */
    AUTO(2, "自动营销"),

    /**
     * 流程画布
     */

    FLOW_CANVAS(3, "流程画布");

    private final Integer value;

    private final String label;

    MarketingTaskTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public static MarketingTaskTypeEnum getEnumByValue(Integer value) {
        MarketingTaskTypeEnum[] items = values();
        for (MarketingTaskTypeEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }

}
