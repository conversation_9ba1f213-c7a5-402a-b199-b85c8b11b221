package com.deepexi.dxp.marketing.extension;

import com.deepexi.util.config.Payload;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.exception.CommonExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 * <AUTHOR>
 **/
@RestControllerAdvice
@Slf4j
public class CustomExceptionHandler {

    @ExceptionHandler(value = Exception.class)
    public Payload<String> handle(Exception e){

        //是不是自定义异常
        if(e instanceof ApplicationException){

            ApplicationException exception = (ApplicationException) e;
            log.error("[业务异常]", exception);
            return new Payload<>(null, exception.getCode(), exception.getMessage());
        }else{
            log.error("[系统异常]",e);
            return new Payload<>(null, CommonExceptionCode.ERROR_CODE, "系统异常");
        }

    }

    /**
     * 拦截捕获 @RequestBody 参数校验异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseStatus(code = HttpStatus.OK)
    public Payload<String> validExceptionHandler(MethodArgumentNotValidException ex) {
        String message = ex.getBindingResult().getAllErrors().get(0).getDefaultMessage();
        return new Payload<>(null, "400", message);
    }

}
