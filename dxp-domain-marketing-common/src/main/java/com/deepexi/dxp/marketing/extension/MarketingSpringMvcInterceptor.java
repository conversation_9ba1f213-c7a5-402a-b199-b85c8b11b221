package com.deepexi.dxp.marketing.extension;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 默认的Spring mvc拦截器组件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020-11-30 10:57
 */
public class MarketingSpringMvcInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String token = request.getHeader("token");
        if(StringUtils.isEmpty(token)) {
            token = request.getParameter("token");
        }
        // 先清空上下文
        MarketingContext.clear();
        MarketingContext.setToken(token);
        return true;
    }
}
