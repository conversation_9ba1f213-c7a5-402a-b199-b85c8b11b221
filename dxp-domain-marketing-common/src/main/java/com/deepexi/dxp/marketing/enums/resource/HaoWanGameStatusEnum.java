package com.deepexi.dxp.marketing.enums.resource;

import lombok.Getter;

/**
 * 24好玩游戏状态
 * <AUTHOR>
 * @version 1.0
 * @date 2020-06-04 15:39
 */
@Getter
public enum HaoWanGameStatusEnum {
    /**
     * 未发布
     */
    WAIT("wait",1),
    /**
     * 发布中
     */
    PUBLISH("publish",2),
    /**
     * 已结束
     */
    OUTDATE("outdate",3),
    /**
     * 维护中
     */
    PAUSE("pause",4);


    private final String id;
    private final Integer value;

    HaoWanGameStatusEnum(String id, Integer value) {
        this.id = id;
        this.value = value;
    }

    public static Integer getValueById(String id) {
        HaoWanGameStatusEnum[] businessModeEnums = values();
        for (HaoWanGameStatusEnum businessModeEnum : businessModeEnums) {
            if (businessModeEnum.getId().equals(id)) {
                return businessModeEnum.getValue();
            }
        }
        return null;
    }

}
