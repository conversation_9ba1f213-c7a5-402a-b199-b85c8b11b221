package com.deepexi.dxp.marketing.enums.marketing;

import lombok.Getter;

/**
 * 发送凭证号
 * <AUTHOR>
 * @version 1.0
 * @date 2020-09-03 11:46
 */
@Getter
public enum SendVoucherNoEnum {
    /**
     * 短信任务
     */
    SMS("sms", "dm_sms_task");

    private final String id;

    private final String value;

    SendVoucherNoEnum(String id, String value) {
        this.id = id;
        this.value = value;
    }

    public static String getValueById(Integer id) {
        SendVoucherNoEnum[] businessModeEnums = values();
        for (SendVoucherNoEnum businessModeEnum : businessModeEnums) {
            if (businessModeEnum.getId().equals(id)) {
                return businessModeEnum.getValue();
            }
        }
        return null;
    }
}
