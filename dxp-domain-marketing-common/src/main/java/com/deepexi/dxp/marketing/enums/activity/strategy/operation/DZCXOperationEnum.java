package com.deepexi.dxp.marketing.enums.activity.strategy.operation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ming.zhong
 * @date created in 21:10 2019/11/26
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum DZCXOperationEnum {
    /**
     * 打折促销的行为
     */
    ZK("discount", "活动折扣");

    private String id;
    private String value;

}
