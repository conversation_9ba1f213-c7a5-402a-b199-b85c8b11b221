package com.deepexi.dxp.marketing.enums.activity.strategy.operation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> xianfeng.cai
 * @date created in 21:10 2019/11/27
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PTOperationEnum {

    /**
     * 拼团活动商品，目前只支持单商品
     */
    SKUCODE("skuCode", "商品编码"),
    /**
     * 拼团商品库存数量
     */
    KCSL("inventoryNumber", "拼团商品库存数量"),
    /**
     * 活动价格
     */
    HDJG("activityPrice", "活动价格"),
    /**
     * 开团价格
     */
    KTJG("discountPrice", "开团价格"),
    /**
     * 参团人数
     */
    CTRS("peopleNumber", "参团人数"),
    /**
     * 有效时间;计量单位:秒(s)
     */
    YXSJ("duration", "有效时间"),
    /**
     * 团主优惠开关 1是 0否
     */
    TZKG("discountFlag", "团主优惠标识"),
    /**
     * 团主价格
     */
    TZJG("discountPrice", "团主价格");



    private String id;
    private String value;

}
