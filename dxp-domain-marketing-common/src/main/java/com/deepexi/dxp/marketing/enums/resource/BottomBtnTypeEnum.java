package com.deepexi.dxp.marketing.enums.resource;

import lombok.Getter;

/**
 * 底部按钮
 * <AUTHOR>
 */
@Getter
public enum BottomBtnTypeEnum {
    /**
     * 1、项目详情, 2、联系销售,3、联系电话,4、分享,5、生成海报
     */
    PROJECT_DETAILS(1,"项目详情"),
    CONTACT_SALES(2,"联系销售"),
    CONTACT_NUMBER(3,"联系电话"),
    SHARE(4,"分享"),
    GENERATE_POSTER(5,"生成海报"),
    CONTACT_ONLINE(6,"在线咨询");
    private final Integer id;
    private final String value;

    BottomBtnTypeEnum(Integer id, String value){
        this.id = id;
        this.value = value;
    }

}
