package com.deepexi.dxp.marketing.enums.activity.limit;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ming.zhong
 * @date created in 19:18 2019/11/25
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PATemplateCouponEnum {
    /**
     * 活动模板渠道限制
     */
    COUPONFLAG("couponFlag", "是否使用优惠券") {},
    COUPON_ID("couponId", "关联优惠券id") {},
    COUPON_NUMBER("couponNumber", "发放数量");


    private String id;
    private String value;
}
