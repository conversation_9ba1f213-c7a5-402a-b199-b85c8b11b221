package com.deepexi.dxp.marketing.constant;

import com.google.common.collect.ImmutableList;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-09-24 12:06
 */
public class CommonConstant {


    /**
     * 放在请求头的链路id
     **/
    public static final String HEADER_TRACE_ID = "X-TRACE-ID";


    /**
     * 链路id
     **/
    public static final String TRACE_ID = "traceId";


    /**
     * MybatisPlus 配置忽略多租户的表
     */
    public static final List<String> IGNORE_TENANT_TABLES = ImmutableList.of("tenant_init_data"
            , "marketing_task_flink_start_param"
            , "dolphin_scheduler_process_template"
            , "dolphin_scheduler_account"
            , "tenant_init"
            , "activity_target"
    );

    /**
     * 活动推广方式链接
     */
    public static final String ACTIVITY_PROMOTION_CHANNEL_URL = "http://www.cnhuafag.com";

    /**
     * 微信小程序默认渠道名称
     */
    public static final String WX_CHANNEL_NAME = "微信小程序";

    /**
     * 微信内网页默认渠道名称
     */
    public static final String WEB_CHANNEL_NAME = "微信内网页";

    /**
     * 抖音小程序默认渠道名称
     */
    public static final String DY_CHANNEL_NAME = "抖音小程序";


//    /**
//     * 微信小程序码 appid
//     */
//    public static final String MINI_APPID = "wx7f05ade0396f497c";

    /**
     * 小程序优惠券活动分享页面
     */
    public static final  String MINI_COUPON_ACT_PATH = "src/pages/activity/pages/coupon/index";

    /**
     * 微信内网页-优惠券活动分享活动
     */
    public static final String H5_COUPON_ACT_PATH = "https://www.baidu.com/";

//    /**
//     * 小程序管理服务 baseurl
//     */
//    public static final String RS_CENTE_BASE_URL = "https://rs-center-test.huafacrm.com";

    /**
     * 创建微信小程序码
     */
    public static final String CREATE_WX_CODE_URL = "/rs-mini-program/code/createWxCode";

    /**
     * 生成场景码URL
     */
    public static final String CREATE_ONE_CODE_SCENE_URL = "/rs-mini-program/open/oneCodeScene/add";

    /**
     * 修改场景码URL
     */
    public static final String UPDATE_ONE_CODE_SCENE_URL = "/rs-mini-program/open/oneCodeScene/update";

    /**
     * 生成一物一码地址
     */
    public static final String CREATE_ONE_CODE_URL = "/rs-mini-program/open/oneCode/add";

    /**
     *删除一物一码
     */
    public static final String DELETE_ONE_CODE_URL = "/rs-mini-program/oneCode/deleteById";

    /**
     * 一物一码更新
     */
    public static final String UPDATE_ONE_CODE_URL = "/rs-mini-program/oneCode/update";
    /**
     * 根据公众号获取个人账号号列表
     */
    public static final String SHORT_VIDEO_GETACCOUNTS_URL = "/rs-mini-program/open/shortVideoFile/getAccounts";
    /**
     * 获取区域和公众号列表
     */
    public static final String SHORT_VIDEO_GETAREAS_URL = "/rs-mini-program/open/shortVideoFile/getAreas";
    /**
     * 分页获取个人视频列表列表
     */
    public static final String SHORT_VIDEO_PAGE_URL = "/rs-mini-program/open/shortVideoFile/page";
    public static final String ONE_CODE_AUTH_URL = "/rs-mini-program/auth/page";

    /**
     * 潜客池2.0 用户信息登记，上报信息登记
     */
    public static final String AD_OPT_USER_LOG_URL = "/rs-system/sys/customer/adoptUserLog";

    /**
     * 潜客池3.0 用户信息登记，上报信息登记
     */
    public static final String AD_OPT_USER_LOG_URL_VERSION3 = "/sys/customer/adoptUserLog";

    /**
     * 获取用户授权信息
     */
    public static final String BASE_AUTH_GET_USERINFO = "/auth/user/getByCondition/{}/{}/wx";
}
