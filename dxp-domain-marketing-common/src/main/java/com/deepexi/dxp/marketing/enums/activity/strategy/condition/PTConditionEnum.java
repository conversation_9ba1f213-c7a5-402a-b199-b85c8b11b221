package com.deepexi.dxp.marketing.enums.activity.strategy.condition;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 拼团条件枚举
 *
 * <AUTHOR> xianfeng.cai
 * @date created in 21:07 2019/11/27
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PTConditionEnum {
    //    WMK ("no", "无门槛"),
    PTPT("1", "普通拼团"),
    LDXPT("2", "老带新拼团");
    private String id;
    private String value;
}
