package com.deepexi.dxp.marketing.enums.resource;

import lombok.Getter;

/**
 * 领券的限制类型枚举
 *
 * <AUTHOR> ming.zhong
 * @date created in 21:11 2019/12/2
 */
@Getter
public enum CouponConditionEnum {
    AUTO("auto", "活动开始系统自动发放"),
    HANDLE("handle", "手动领取"),
    INVITE("invite", "邀请他人注册后领取");

    private final String id;
    private final String value;

    CouponConditionEnum(String id, String value) {
        this.id = id;
        this.value = value;
    }

    public static String getValueById(String id) {
        CouponConditionEnum[] businessModeEnums = values();
        for (CouponConditionEnum businessModeEnum : businessModeEnums) {
            if (businessModeEnum.getId().equals(id)) {
                return businessModeEnum.getValue();
            }
        }
        return null;
    }
}
