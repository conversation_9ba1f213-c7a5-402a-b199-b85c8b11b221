package com.deepexi.dxp.marketing.enums.marketing;

import lombok.Getter;

/**
 * 任务执行状态
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-23 11:15
 */
@Getter
public enum MarketingTaskExecuteStatusEnum {
    /**
     * 执行中
     */
    WAIT(0,"执行中"),

    /**
     * 执行成功
     */
    SUCCESS(1, "执行成功"),

    /**
     * 执行失败
     */
    FAILURE(2, "执行失败");

    private final Integer value;

    private final String label;

    MarketingTaskExecuteStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public static MarketingTaskExecuteStatusEnum getEnumByValue(Integer value) {
        MarketingTaskExecuteStatusEnum[] items = values();
        for (MarketingTaskExecuteStatusEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }
}
