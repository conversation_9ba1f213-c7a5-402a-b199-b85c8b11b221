package com.deepexi.dxp.marketing.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public final class ModelConstant {
    /**
     * es索引字段
     */
    public static final String ES_INDEX = "es_index";

    public static final String TARGET_FIELD = "target_field";

    public static final String DIMENSIONS = "dimensions";

    public static final String TYPE = "type";

    public static final String TAG_FIELD = "tag_field";

    public static final String FIELD_TYPE = "field_type";

    public static final String RATE = "rate";

    public static final String FORMULA = "formula";

    public static final String OPERATOR = "operator";

    public static final String COMPARE = "compare";

    public static final String COMPARE1 = "compare1";

    public static final String SCORE = "score";

    public static final String BEGIN_AT = "begin_at";

    public static final String END_AT = "end_at";

    public static final String EVENT_TYPE = "event_type";

    public static final String EVENT_PARAMS = "event_params";

    public static final String BASIC_TAG = "basic_tag";

    public static final String EVENT = "event";

    public static final String MODELS = "models";

    public static final int ACTION = 1;

    public static final int RESULT = 2;

    public static final int BETWEEN = 5;

    protected static final Map<Integer, String> OPERATION_MAP = new HashMap<>();

    static {
        OPERATION_MAP.put(0, "=");
        OPERATION_MAP.put(1, ">");
        OPERATION_MAP.put(2, ">=");
        OPERATION_MAP.put(3, "<");
        OPERATION_MAP.put(4, "<=");
        OPERATION_MAP.put(5, "in");
    }

    public static Map<Integer, String> getOperationMap() {
        return OPERATION_MAP;
    }
}
