package com.deepexi.dxp.marketing.extension;


public class HuafaRuntimeEnv {
    private static final ThreadLocal<String> PHONE = new ThreadLocal();

    private static final ThreadLocal<String> USERID = new ThreadLocal();

    private static final ThreadLocal<String> POSITIONID = new ThreadLocal();

    private static final ThreadLocal<String> CREATEDBY = new ThreadLocal();

    public HuafaRuntimeEnv() {
    }

    public static void setPhone(String phone) {
        PHONE.set(phone);
    }

    public static String getPhone() {
        return (String)PHONE.get();
    }

    public static void clear() {
        PHONE.remove();
        USERID.remove();
        POSITIONID.remove();;
        CREATEDBY.remove();
    }


    public static void setCreatedBy(String createdBy) {
        CREATEDBY.set(createdBy);
    }

    public static String getCreatedBy() {
        return (String)CREATEDBY.get();
    }

    public static void setUserId(String userId) {
        USERID.set(userId);
    }

    public static String getUserId() {
        return (String)USERID.get();
    }

    public static void setPositionId(String positionId) {
        POSITIONID.set(positionId);
    }

    public static String getPositionId() {
        return (String)POSITIONID.get();
    }
}
