package com.deepexi.dxp.marketing.enums.activity.result;

import lombok.Getter;

/**
 * 商品没活动原因
 * <AUTHOR>
 * @date 2020/09/30
 */
@Getter
public enum NoActivityMsgEnum {


    /**
     * 0=校验通过
     */
    PASS(0, "校验通过"),
    LIMIT_ERROR(-1, "活动限制校验不通过"),
    NO_MATCH(-2, "指定活动不在商品可参与活动列表");


    private int id;
    private String msg;

    NoActivityMsgEnum(int id, String msg) {
        this.id = id;
        this.msg = msg;
    }
}
