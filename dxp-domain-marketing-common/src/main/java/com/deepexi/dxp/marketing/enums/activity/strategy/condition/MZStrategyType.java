package com.deepexi.dxp.marketing.enums.activity.strategy.condition;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> xianfeng.cai
 * @date created in 21:07 2019/11/27
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MZStrategyType {
    //    WMK ("no", "无门槛"),
    MJE("money", "满金额"),
    MJS("number", "满件数"),
    AB_C("abc", "买A+B送C");
    private String id;
    private String value;
}
