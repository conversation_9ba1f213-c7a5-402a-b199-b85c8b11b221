package com.deepexi.dxp.marketing.enums.activity.strategy.condition;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 领券活动的策略类型枚举
 * <AUTHOR> ming.zhong
 * @date created in 21:11 2019/12/2
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CouponConditionEnum {
    AUTO("auto", "活动开始系统自动发放"),
    HANDLE("handle", "手动领取"),
    INVITE("invite", "邀请他人注册后领取");
    private String id;
    private String value;

    public static String getValueById(String id) {
        CouponConditionEnum[] businessModeEnums = values();
        for (CouponConditionEnum businessModeEnum : businessModeEnums) {
            if (businessModeEnum.getId().equals(id)) {
                return businessModeEnum.getValue();
            }
        }
        return null;
    }
}
