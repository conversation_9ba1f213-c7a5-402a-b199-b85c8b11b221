package com.deepexi.dxp.marketing.enums.marketing;

import com.deepexi.util.exception.ApplicationException;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/15
 */
@Getter
public enum MarketingTaskOperationEnum {

    RELEASE(1, "发布"),
    SUSPEND(2, "暂停"),
    RECOVER(3, "恢复"),
    BREAK_OFF(4, "终止");


    private Integer index;
    private String name;

    MarketingTaskOperationEnum(Integer index, String name){
        this.index = index;
        this.name = name;
    }

    /**
     * 判断操作是否存在
     * @param index 操作
     */
    public static void judge(Integer index) {
        MarketingTaskOperationEnum[] values = values();
        boolean f = Boolean.TRUE;
        for (MarketingTaskOperationEnum value : values) {
            if (value.getIndex().equals(index)) {
                f = Boolean.FALSE;
                break;
            }
        }
        if (f) {
            throw new ApplicationException("operationType is error：{}", index);
        }
    }
}
