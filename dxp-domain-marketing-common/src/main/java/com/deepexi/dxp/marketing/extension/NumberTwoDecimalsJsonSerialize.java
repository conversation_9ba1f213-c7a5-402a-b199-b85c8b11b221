package com.deepexi.dxp.marketing.extension;


import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * 小数点保留两位序列化器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-01-16 10:29
 */
public class NumberTwoDecimalsJsonSerialize extends JsonSerializer<Number> {

    /**
     * 保留两位小数format
     */
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("0.00");

    @Override
    public void serialize(Number number, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (number == null) {
            jsonGenerator.writeString(DECIMAL_FORMAT.format(BigDecimal.ZERO));
        } else {
            jsonGenerator.writeString(DECIMAL_FORMAT.format(number));
        }
    }
}
