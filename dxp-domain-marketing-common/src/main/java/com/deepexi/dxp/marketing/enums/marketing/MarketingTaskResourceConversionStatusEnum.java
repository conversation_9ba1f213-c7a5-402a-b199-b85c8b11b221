package com.deepexi.dxp.marketing.enums.marketing;

import lombok.Getter;

/**
 * 营销资源使用枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-26 15:05
 */
@Getter
public enum MarketingTaskResourceConversionStatusEnum {
    /**
     * 未拥有
     */
    NOT_HAVE(1, "未拥有"),

    /**
     * 已拥有
     */
    HAVE(2, "已拥有"),

    /**
     * 已使用
     */
    CONVERSION(3, "已使用");

    private final Integer value;

    private final String label;

    MarketingTaskResourceConversionStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public static MarketingTaskResourceConversionStatusEnum getEnumByValue(Integer value) {
        MarketingTaskResourceConversionStatusEnum[] items = values();
        for (MarketingTaskResourceConversionStatusEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }

}
