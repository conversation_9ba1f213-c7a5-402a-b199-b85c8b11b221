package com.deepexi.dxp.marketing.enums.activity.strategy.operation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2020/3/9 16:34
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
public enum PreSalesStrategyType {


    /**
     * 预售活动策略类型
     */
    DJ_AND_WK("depositPayment", "定金加尾款"),
    QK("allMoney", "全款");
    private String id;
    private String value;
}
