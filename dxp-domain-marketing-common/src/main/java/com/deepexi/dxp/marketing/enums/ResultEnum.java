package com.deepexi.dxp.marketing.enums;

import com.deepexi.util.constant.BaseEnumType;
import com.deepexi.util.exception.CommonExceptionCode;

import static com.deepexi.dxp.marketing.constant.BaseServiceErrorCode.CLINET_BASE;
import static com.deepexi.dxp.marketing.constant.BaseServiceErrorCode.SERVICE_BASE;

/**
 * 错误码定义格式：
 * <p>
 * 统一格式（由9位数字组成）：
 * a-cccc-eeee
 * a 表示业务异常类型，1 表示客户端类异常，2 表示服务端类异常，3 表示其它未知类异常。
 * cccc 表示{中心（1xxx）\域（2xxx）}
 * eeee 表示业务异常：1001-1999属于公用异常2001-9999根据自己业务进行自
 * <p>
 * 供应链域 2001
 * 会员域 2002
 * 商品域 2003
 * 交易域 2004
 * 促销域2005
 * 商户域2006
 * 仓储域2007
 * 调度域2008
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020-06-29 12:46
 */
public enum ResultEnum implements BaseEnumType, CommonExceptionCode {

    /**
     * 客户端错误码定义
     **/
    CLINT_INVALIDATION(CLINET_BASE + INVALIDATION, "输入的数据错误！"),
    CLINT_DATE_TYPE_ERROR(CLINET_BASE + DATE_TYPE_ERROR, "数据类型不对！"),
    CLINT_NOT_BODY(CLINET_BASE + NOT_BODY, "没有请求体！"),
    CLINT_OPERATION(CLINET_BASE + OPERATION, "操作不支持！"),

    TENANT_CAN_NOT_BE_NULL(CLINET_BASE + "1800", "租户信息不能为空"),
    APP_ID_CAN_NOT_BE_NULL(CLINET_BASE + "1801", "接入方信息不能为空"),
    REQUEST_ID_NOT_FOUND(CLINET_BASE + "1802", "请求ID不能为空"),
    TOKEN_CAN_NOT_BE_NULL(CLINET_BASE + "1803", "TOKEN不能为空"),
    SERVICE_SALE_2_ACTIVITY(SERVICE_BASE + "1804", "销售库存转活动库存异常"),
    SERVICE_ACTIVITY_2_SALE(SERVICE_BASE + "1805", "活动库存转销售库存异常"),

    /*  客户端错误，从这里开始定义2002开始   */
    // 原来会员域：2002 - 3999
    CLINT_NAME(CLINET_BASE + "2002", "用户名不能为空！"),


    // 原来促销域：4000 - 5999


    // 原来分销域：6000 - 7999,

    /**
     * 服务端错误码定义
     **/
    SERVICE_INVALIDATION(SERVICE_BASE + INVALIDATION, "输入的数据错误！"),
    SERVICE_DATE_TYPE_ERROR(SERVICE_BASE + DATE_TYPE_ERROR, "数据类型不对！"),
    SERVICE_NOT_BODY(SERVICE_BASE + NOT_BODY, "没有请求体！"),
    SERVICE_OPERATION(SERVICE_BASE + OPERATION, "操作不支持！"),

    /*  服务端错误，从这里开始定义2002开始   */
    // 原来会员域：2002 - 3999


    /**
     * 其他错误码定义
     **/
    /*  服务端错误，从这里开始定义2002开始   */;


    private String code;

    private String msg;

    ResultEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

}
