package com.deepexi.dxp.marketing.enums.activity.strategy.condition;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ming.zhong
 * @date created in 14:52 2019/11/27
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MJYHConditionEnum {
    /**
     * 满减优惠活动的策略条件枚举
     */
    JE("money", "订单金额");
    private String id;
    private String value;
}
