package com.deepexi.dxp.marketing.enums.activity.limit;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2020/3/9 16:51
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PATemplateShopEnum {

    /**
     * 活动模板门店限制
     */
    CHANNEL("shopId", "门店限制"),
    ALL_SHOP("allShop", "所有门店");
    private String id;
    private String value;


}
