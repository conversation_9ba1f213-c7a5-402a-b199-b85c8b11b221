package com.deepexi.dxp.marketing.enums.activity.strategy.condition;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2020/3/9 16:41
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum YSConditionEnum {
    /**
     * 预售活动的参数信息
     */
    RATIO_MONEY("ratioMoney", "定金比例(预售价格的比例)"),
    ORDER_TIME("money", "定金钱（多少钱）"),
    END_TIME("endTime", "定金结束时间"),
    END_HOUSE("endHouse", "尾款支付时间(定金结束时间后几个小时)"),
    START_TIME("startTime", "定金开始时间");


    private String id;
    private String value;
}
