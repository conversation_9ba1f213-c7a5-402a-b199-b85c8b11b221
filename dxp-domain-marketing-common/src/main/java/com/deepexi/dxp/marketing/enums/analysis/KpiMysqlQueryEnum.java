package com.deepexi.dxp.marketing.enums.analysis;

import lombok.Getter;

/**
 * KPI指标-mysql特殊查询
 *
 * <AUTHOR>
 * @since 2020年05月19日 15:39
 */
@Getter
public enum KpiMysqlQueryEnum {

    /**
     * 活动推送用户数
     */
    ACTIVITY_PUSH_USER_NUM("activityPushUserNum", "活动推送用户数");

    private final String code;

    private final String msg;

    KpiMysqlQueryEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static KpiMysqlQueryEnum getEnumByState(String code) {
        KpiMysqlQueryEnum[] items = values();
        for (KpiMysqlQueryEnum item : items) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }
}
