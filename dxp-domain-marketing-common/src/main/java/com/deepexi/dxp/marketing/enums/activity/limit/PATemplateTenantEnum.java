package com.deepexi.dxp.marketing.enums.activity.limit;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ming.zhong
 * @date created in 17:29 2019/11/25
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PATemplateTenantEnum {

    /**
     * 活动模板渠道限制
     */
    H5("h5", "H5") {},
    MD("MD", "门店"),
    CHANNEL("clientTenant", "渠道限制");
    private String id;
    private String value;
}
