package com.deepexi.dxp.marketing.enums.marketing;

import lombok.Getter;

/**
 * 任务简称枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-24 10:08
 */
@Getter
public enum TaskShortNameEnum {
    /**
     * 短信任务
     */
    SMS(2, "sms"),
    /**
     * 微信任务
     */
    WX(1, "wx");
    private final Integer id;

    private final String value;

    TaskShortNameEnum(Integer id, String value) {
        this.id = id;
        this.value = value;
    }

    public static String getValueById(Integer id) {
        TaskShortNameEnum[] businessModeEnums = values();
        for (TaskShortNameEnum businessModeEnum : businessModeEnums) {
            if (businessModeEnum.getId().equals(id)) {
                return businessModeEnum.getValue();
            }
        }
        return null;
    }

}