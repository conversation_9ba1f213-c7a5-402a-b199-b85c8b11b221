package com.deepexi.dxp.marketing.enums.activity.strategy.operation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2020/3/10 18:22
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CZOperationEnum {

    /**
     * 充值活动的行为
     */
    ZS_MONEY("zs_money", "赠送余额"),
    ZS_POINT("zs_point", "赠送积分");

    private String id;
    private String value;
}
