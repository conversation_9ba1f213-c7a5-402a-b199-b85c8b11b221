package com.deepexi.dxp.marketing.enums.marketing;

import lombok.Getter;

/**
 * @Enum: MessageParamEnum
 * @Description: 消息参数占位符枚举
 * @Author: mou
 * @Date: 2020/3/25
 */
@Getter
public enum MessageParamEnum {

    /**
     * 活动名称
     */
    ACTIVITY_NAME("activityName", "活动名称"),

    /**
     * 活动规则
     */
    ACTIVITY_RULE("activityRule", "活动规则"),

    /**
     * 优惠券类型
     */
    COUPON_TYPE("couponType", "优惠券类型"),

    /**
     * 额度
     */
    AMOUNT("amount", "额度"),

    /**
     * 领券条件
     */
    COUPON_CONDITION("couponCondition", "领取条件"),

    /**
     * 优惠券名称
     */
    COUPON_NAME("couponName", "优惠券名称"),

    /**
     * 链接路径
     */
    URI("uri", "路径"),

    /**
     *用户昵称
     */
    USER_NAME("userName","用户昵称"),

    /**
     * 活动时间
     */
    ACTIVITY_TIME("activityTime","活动时间"),

    /**
     * 验证码
     */
    CODE("code", "验证码");

    private final String name;

    private final String value;

    MessageParamEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public static MessageParamEnum getEnumByName(String name) {
        MessageParamEnum[] items = values();
        for (MessageParamEnum item : items) {
            if (item.name.equals(name)) {
                return item;
            }
        }
        return null;
    }

    public static MessageParamEnum getEnumByValue(String value) {
        MessageParamEnum[] items = values();
        for (MessageParamEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

}
