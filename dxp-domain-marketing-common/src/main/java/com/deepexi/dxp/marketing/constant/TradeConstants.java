package com.deepexi.dxp.marketing.constant;

/**
 * 常量
 *
 * <AUTHOR>
 * @date 2019-10-07 15:35
 */
public class TradeConstants {

    /**
     * httpGet的请求
     */
    public static final String HTTP_GET_METHOD = "GET";

    /**
     * 租户编码
     */
    public static final String TENANT_KEY = "tenantId";

    /**
     * token信息
     */
    public static final String TOKEN_KEY = "userToken";

    /**
     * appId key
     */
    public static final String APP_ID_KEY = "appId";

    /**
     * error
     */
    public static final String ERROR_DECODER = "^[0-9]+$";

    /**
     * createdTime key
     */
    public static final String CREATE_TIME_KEY = "createdTime";

    /**
     * updatedTime key
     */
    public static final String UPDATED_TIME_KEY = "updatedTime";

    /**
     * orderUpdatedTime key
     */
    public static final String ORDER_UPDATED_TIME_KEY = "orderUpdatedTime";

    /**
     * TRACE_ID key
     */
    public static final String TRACE_ID_KEY = "TRACE_ID";

    /**
     * Time format : yyyyMMddHHmmssSSS
     */
    public static final String DATE_TIME_FORMAT = "yyyyMMddHHmmssSSS";

    public static final String DOT = ",";

    /**
     * 角色组
     */
    public static final Long ROLE_GROUP_ID = 1000038L;

    /**
     * 账号类型ID
     */
    public static final String ACCOUNT_TYPE_ID = "0";

    /**
     * 调度器单次处理的数据
     */
    public static final int JOBHANDLER_PROCESS_SIZE = 100;

    /**
     * Time format : yyyyMMddHHmmssSSS
     */
    public static final String DATE_TIME_MS_FORMAT = "yyyyMMddHHmmssSSS";

    /**
     * Time format : yyyyMMddHHmm
     */
    public static final String DATE_TIME_MINUTE_FORMAT = "yyyyMMddHHmm";
}
