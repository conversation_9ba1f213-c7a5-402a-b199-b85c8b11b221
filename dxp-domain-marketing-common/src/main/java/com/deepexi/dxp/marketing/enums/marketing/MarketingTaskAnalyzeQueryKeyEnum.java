package com.deepexi.dxp.marketing.enums.marketing;

import lombok.Getter;

/**
 * 主动营销分析查询key值枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2020-06-18 16:35
 */
@Getter
public enum MarketingTaskAnalyzeQueryKeyEnum {
    /**
     * 数量
     */
    QUANTITY("qty", "数量"),

    /**
     * 平均时间
     */
    AVG_TIME("avgtime", "平均时间");

    private final String value;

    private final String label;

    MarketingTaskAnalyzeQueryKeyEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static MarketingTaskAnalyzeQueryKeyEnum getEnumByValue(String value) {
        MarketingTaskAnalyzeQueryKeyEnum[] items = values();
        for (MarketingTaskAnalyzeQueryKeyEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }

}
