package com.deepexi.dxp.marketing.enums.specify;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * ActivityTopicArrangementEnum 枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/30 11:15
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
public enum ActivityTopicArrangementEnum {

    LIST(1,"活动专题-列表展示"),
    AREA(0,"活动专题-区域展示"),
    ;

    private Integer code;
    private String name;

}
