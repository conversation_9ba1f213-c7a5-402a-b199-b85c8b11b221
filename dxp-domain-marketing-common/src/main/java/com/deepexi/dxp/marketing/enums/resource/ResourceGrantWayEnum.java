package com.deepexi.dxp.marketing.enums.resource;

import lombok.Getter;

/**
 * 资源发放方式枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2020-06-02 11:27
 */
@Getter
public enum ResourceGrantWayEnum {
    /**
     * 资源类型
     */
    MAIL_ONLINE(0,"线上邮寄"),
    OFFLINE_VERIFY(1,"线下核销");
    private final Integer id;
    private final String value;

    ResourceGrantWayEnum(Integer id, String value){
        this.id = id;
        this.value = value;
    }

    public static String getValueById(Integer id) {
        ResourceGrantWayEnum[] grantWayEnums = values();
        for (ResourceGrantWayEnum grantWayEnum : grantWayEnums) {
            if (grantWayEnum.getId().equals(id)) {
                return grantWayEnum.getValue();
            }
        }
        return null;
    }
}
