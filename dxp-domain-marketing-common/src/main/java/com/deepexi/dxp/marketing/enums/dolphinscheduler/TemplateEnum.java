package com.deepexi.dxp.marketing.enums.dolphinscheduler;


import lombok.Getter;

/**
 * 流程定义模板类型枚举
 * <AUTHOR>
 * @Date 2020/3/26
 */
@Getter
public enum TemplateEnum {

    /**
     * 自动营销
     */
    AUTOMATIC_MARKETING("AUTOMATIC_MARKETING", "自动营销"),

    /**
     * 模型
     */
    MODEL("MODEL", "模型"),

    /**
     * 流程画布
     */
    FLOW_CANVAS("FLOW_CANVAS","流程画布");

    private final String value;

    private final String label;

    TemplateEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static TemplateEnum getEnumByValue(String value) {
        TemplateEnum[] items = values();
        for (TemplateEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }
}
