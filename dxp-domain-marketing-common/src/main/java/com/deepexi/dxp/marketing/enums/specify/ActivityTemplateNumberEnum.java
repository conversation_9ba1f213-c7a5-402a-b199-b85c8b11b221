package com.deepexi.dxp.marketing.enums.specify;


import lombok.Getter;

/**
 * 活动数量限制枚举类
 *
 * <AUTHOR> xianfeng.cai
 * @date created in 17:29 2019/11/26
 */
@Getter
public enum ActivityTemplateNumberEnum {

    /**
     * 参与类型
     */
    JOIN_TYPE("joinType", "joinType"),
    /**
     * 每日/每人抽奖次数
     */
    DRAW_NUMBER("dailyDrawNumber", "dailyDrawNumber"),
    /**
     * 分享可额外抽奖次数
     */
    SHARE_DRAW_NUMBER("shareDrawNumber", "shareDrawNumber"),
    /**
     * 每天/每人可分享次数
     */
    DAILY_SHARE_NUMBER("dailyShareNumber", "dailyShareNumber"),
    /**
     * 信息登记
     */
    FEEDBACK_INFO("feedbackInfo", "feedbackInfo"),
    /**
     * 分享次数
     */
    SHARE_NUMBER("shareNumber", "shareNumber"),
    /**
     * 切换项目次数
     */
    SWITCH_NUMBER("switchNumber", "shareNumber"),

    /**
     * 砍价最低价
     */
    LOWEST_PRICE("lowestPrice", "lowestPrice"),
    /**
     * 砍价有效期(助力有效期/签到总天数)
     */
    VALID_PERIOD("validPeriod", "validPeriod"),
    /**
     * 帮砍人数
     */
    HELPER_NUMBER("helperNumber", "helperNumber"),
    /**
     * 发起砍价次数(发起助力次数)
     */
    LAUNCH_TIMES("launchTimes", "launchTimes"),
    /**
     * 用户帮砍次数(用户参与助力次数)
     */
    HELP_TIMES("helpTimes", "helpTimes"),
    /**
     * 助力数值
     */
    HELP_NUM_SET("helpNumSet", "helpNumSet"),

    /**
     * 助力领取奖品方式
     */
    HELP_RECEIVE_MODE("receiveMode","receiveMode"),

    /**
     * 助力获取数值定义
     */
    HELP_ASSIST_NUM_DEFINITION("assistNumDefinition","assistNumDefinition"),

    /**
     * 分享获奖
     */
    HELP_ISOPEN_SHARE_AWARDS("isOpenShareAwards","isOpenShareAwards"),

    /**
     * 助力获奖
     */
    HELP_ISOPEN_HELP_AWARDS("isOpenHelpAwards","isOpenHelpAwards"),

    /**
     * 初始赠送卡片总数
     */
    INIT_CARD_COUNT("initCardCount","initCardCount"),

    /**
     * 单人红包激励金额上限
     */
    MONEY_LIMIT("moneyLimit","moneyLimit"),

    RADIUS_LIMIT("radius","打卡误差半径"),

    LOCATIONS_LIMIT("locations","打卡地点配置"),

    RULES_LIMIT("rules","规则配置"),

    TOTAL_GET_NUMBER("totalGetNumber","用户每个规则已获得的次数"),
    ;


    private final String id;
    private final String value;

    ActivityTemplateNumberEnum(String id, String value) {
        this.id = id;
        this.value = value;
    }
}
