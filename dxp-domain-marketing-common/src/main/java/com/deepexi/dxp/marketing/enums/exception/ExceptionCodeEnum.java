package com.deepexi.dxp.marketing.enums.exception;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/11/21 20:14
 */
public enum ExceptionCodeEnum {

    /**
     *
     */
    TENANT_ID("10241000", "住户id为空"),
    ACTIVITY_NAME("10241001", "活动名称不可为空"),
    ACTIVITY_STATUS("10241002", "活动状态不可为空"),
    APP_ID("10241003", "应用id为空"),
    ACTIVITY_ID("10241004", "活动id不可为空"),
    ACTIVITY_LIMIT_TYPE("10241005", "活动限制类型不可为空"),
    ACTIVITY_LIMIT_LIMIT("10241006", "活动限制类型JSON不可为空"),
    STRATEGY_TYPE("10241007", "策略类型不可为空"),
    RULES("10241008", "条件结果json数据不可为空");

    private String code;
    private String message;

    ExceptionCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }


    public String getMessage() {
        return message;
    }

}
