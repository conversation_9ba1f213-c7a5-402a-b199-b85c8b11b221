package com.deepexi.dxp.marketing.enums.marketing;

import lombok.Getter;

/**
 * @Enum: FlinkTypeEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/7/22
 */
@Getter
public enum MarketingFlinkTypeEnum {
    AUTO_TASK(1, "自动任务"),
    FLINK_CEP(2, "flinkcep 圈人"),
    STATUS_FUL_FUNCTION(3, "画布引擎"),
    CON_CEP(4, "flink 执行条件cep");

    private final Integer value;

    private final String describe;

    MarketingFlinkTypeEnum(Integer value, String describe){
        this.value = value;
        this.describe = describe;
    }
}
