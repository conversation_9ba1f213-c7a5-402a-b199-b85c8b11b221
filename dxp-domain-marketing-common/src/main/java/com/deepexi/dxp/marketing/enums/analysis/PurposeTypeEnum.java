package com.deepexi.dxp.marketing.enums.analysis;

import lombok.Getter;

/**
 * 任务目的类型
 *
 * <AUTHOR>
 * @since 2020年04月24日 15:22
 */
@Getter
public enum PurposeTypeEnum {

    /**
     * 营收
     */
    REVENUE(1, "营收"),
    /**
     * 商品
     */
    COMMODITY(2, "商品"),
    /**
     * 用户
     */
    USER(3, "用户"),
    ;

    private final Integer state;

    private final String msg;

    PurposeTypeEnum(Integer state, String msg) {
        this.state = state;
        this.msg = msg;
    }

    public static PurposeTypeEnum getEnumByState(Integer state) {
        PurposeTypeEnum[] items = values();
        for (PurposeTypeEnum item : items) {
            if (item.state.equals(state)) {
                return item;
            }
        }
        return null;
    }
}
