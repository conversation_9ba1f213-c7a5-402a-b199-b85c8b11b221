package com.deepexi.dxp.marketing.annotation;

import java.lang.annotation.*;

/**
 * @Class: GetCacheable
 *  使用时要重写入参bean的toString方法(如果使用lombok的@Data重写的toString方法要注意它不会包含父类的字段)
 * <AUTHOR> zht
 * @date : 2020/8/29
 */
//自定义缓存注解 获取缓存

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface GetCacheable {
    /**
     * 保存方法缓存key的key
     * @return
     */
    String cacheKey();

    /**
     * 返回类型
     * @return
     */
    Class<?> returnType();

    /**
     * 过期时间 默认30分钟
     * @return
     */
    int expireTime() default 60*30;
}
