package com.deepexi.dxp.marketing.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

/**
 * swagger ui 2.9.2版本的配置
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
@EnableSwagger2
public class SwaggerConfig {

    @Value("${spring.application.name}")
    private String appName;

    /**
     * adminapi接口文档appName
     *
     * @return
     */
//    @Bean
//    public Docket adminApiDoc() {
//
//        return new Docket(DocumentationType.SWAGGER_2)
//                .groupName("admin-api")
//                .pathMapping("/")
//                .enable(true)
//                //配置文档的元信息
//                .apiInfo(apiInfo())
//                .select()
//                .apis(RequestHandlerSelectors.basePackage("com.deepexi"))
//                .paths(PathSelectors.ant("/admin-api/**"))
//                .build();
//
//
//    }


    /**
     * middleapi接口文档
     *
     * @return
     */
//    @Bean
//    public Docket middleApiDoc() {
//
//        return new Docket(DocumentationType.SWAGGER_2)
//                .groupName("marketing-api")
//                .pathMapping("/")
//                .enable(true)
//                .apiInfo(apiInfo())
//                .select()
//                .apis(RequestHandlerSelectors.basePackage("com.deepexi.dxp.marketing.controller.specify.middleapi"))
//                .paths(PathSelectors.ant("/marketing-api/**"))
//                .build();
//    }


    /**
     * 用于生成sdk
     *
     * @return
     */
    @Bean
    public Docket sdkApiDoc() {
        return new Docket(DocumentationType.SWAGGER_2)
//                .pathMapping("/" + appName)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.deepexi"))
                //正则匹配请求路径，并分配到当前项目组
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(globalOperationParameters());
    }
//
//    /**
//     * openapi接口文档
//     *
//     * @return
//     */
//    @Bean
//    public Docket openapiApiDoc() {
//        return new Docket(DocumentationType.SWAGGER_2)
//                .groupName("specify")
//                .select()
//                .apis(RequestHandlerSelectors.basePackage("com.deepexi.dxp.marketing.controller.specify.middleapi"))
//                //正则匹配请求路径，并分配到当前项目组
//                .paths(PathSelectors.any())
//                .build()
//                .globalOperationParameters(globalOperationParameters());
//    }
//
//
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("DXP平台")
                .description("DXP平台-营销域接口文档")
                .contact(new Contact("admin", "https://www.deepexi.com", "<EMAIL>"))
                .version("v1.0")
                .build();
    }

    /**
     * 配置全局通用参数
     *
     * @return
     */
    private List<Parameter> globalOperationParameters() {

        List<Parameter> parameters = new ArrayList<>();
        parameters.add(new ParameterBuilder()
                .name("tenantId")
                .description("租户ID")
                .modelRef(new ModelRef("string")).parameterType("header")
                .required(false)
                .build());

        parameters.add(new ParameterBuilder()
                .name("appId")
                .description("接入方应用ID")
                .modelRef(new ModelRef("long")).parameterType("header")
                .required(false)
                .build());

        return parameters;

    }

}