package com.deepexi.dxp.marketing.constant;

import com.google.common.collect.ImmutableMap;

import java.util.Map;

/**
 * @Class: SceneConstant
 * @Description:
 * @Author: zht
 * @Date: 2020/3/28
 */
public final class SceneConstant {

    /**
     * 渠道范围字段
     */
    public static final String CHANNEL = "channels";

    /**
     * 人群范围
     */
    public static final String MEMBER_TARGET = "member_target";

    /**
     * 人群类型 all:所有人群、spec:指定人群、tag:指定标签
     */
    public static final String TYPE = "type";

    /**
     * 人群值，如果type=all 该值为空，type=spec 该值为会员id拼接英文逗号的字符串，
     *     // 如果type=tag 该值为标签id
     */
    public static final String VALUE = "value";

    public static final String EVENT_TYPE = "event_type";

    public static final String EVENT_TIME = "event_time";

    public static final String RULES = "rules";

    public static final String TAGS = "tags";

    public static final String EVENT = "event";

    public static final String TIMES_OP = "times_op";

    public static final String TIMES_COMPARE1 = "times_compare1";

    public static final String TIMES_COMPARE2 = "times_compare2";

    public static final String NEXT_RULE_RELATION = "next_rule_relation";

    public static final String GE = ">=";

    /**
     * 我的场景
     */
    public static final Integer MY_SCENE = 2;

    /**
     * 行业场景
     */
    public static final Integer PROFESSION_SCENE = 1;

    /**
     * 美妆行业
     */
    public static final Integer BEAUTY_PROFESSION = 1;

    /**
     * 认知
     */
    public static final Integer COGNITION = 1;

    /**
     * 交易
     */
    public static final Integer TRANSACTION = 2;

    /**
     * 售后
     */
    public static final Integer AFTER_SALES = 3;

    /**
     * 权益
     */
    public static final Integer RIGHTS = 4;

    /**
     * 必须在指定事件内完成事件, -1没有约束
     */
    public static final String TIME_CONSTRAINT = "time_constraint";

    public static final Map<Integer, String> DOMAP = ImmutableMap.of(1, "did", 2, "undo");

    public static final Map<Integer, String> MEMBERMAP = ImmutableMap.of(1, "all", 2, "spec", 3, "tag");

    public static final Map<Integer, String> STRUCTUREMAP = ImmutableMap.of(1, "compose", 2, "ordered");

    public static final Map<Integer, String> ANDORMAP = ImmutableMap.of(0, "or", 1, "and");

    public static final Map<Integer, String> OPERATIONMAP = ImmutableMap.of(1, ">", 2, ">=", 3, "<", 4, "<=", 5, "in");
}
