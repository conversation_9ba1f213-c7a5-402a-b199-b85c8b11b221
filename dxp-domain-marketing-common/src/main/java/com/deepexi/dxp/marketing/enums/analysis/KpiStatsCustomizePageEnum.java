package com.deepexi.dxp.marketing.enums.analysis;

import lombok.Getter;

/**
 * KPI指标统计定制页面类型
 *
 * <AUTHOR>
 * @since 2020年06月10日 14:26
 */
@Getter
public enum KpiStatsCustomizePageEnum {

    /**
     * 首页
     */
    HOME(1, "首页"),
    /**
     * 运营洞察
     */
    OPERATIONAL_INSIGHT(2, "运营洞察");

    private final Integer state;

    private final String msg;

    KpiStatsCustomizePageEnum(Integer state, String msg) {
        this.state = state;
        this.msg = msg;
    }

    public static KpiStatsCustomizePageEnum getEnumByState(Integer state) {
        KpiStatsCustomizePageEnum[] items = values();
        for (KpiStatsCustomizePageEnum item : items) {
            if (item.state.equals(state)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 校验是否枚举类型
     *
     * @param state 数据
     * @return boolean
     */
    public static boolean checkState(Integer state) {
        KpiStatsCustomizePageEnum[] items = values();
        for (KpiStatsCustomizePageEnum item : items) {
            if (item.state.equals(state)) {
                return true;
            }
        }
        return false;
    }
}
