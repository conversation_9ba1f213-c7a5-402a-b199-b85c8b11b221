package com.deepexi.dxp.marketing.enums.activity.strategy.condition;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2020/3/10 18:22
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CZConditionEnum {
    /**
     * 充值活动条件
     */
    MJE("money", "满金额"),
    RATIO_MONEY("recharge", "充值活动的充值条件数");


    private String id;
    private String value;
}
