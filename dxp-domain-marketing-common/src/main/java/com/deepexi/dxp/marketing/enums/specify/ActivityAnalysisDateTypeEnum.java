package com.deepexi.dxp.marketing.enums.specify;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum ActivityAnalysisDateTypeEnum {
    /**
     * 活动分析日期类型
     */
    BY_DAY(1,"按日统计"),
    BY_MONTH(2,"按月统计"),

    BY_TIMES(3,"按次数统计"),
    BY_PEOPLE(4,"按人统计")

    ;

    private final Integer id;
    private final String value;

    ActivityAnalysisDateTypeEnum(Integer id, String value) {
        this.id = id;
        this.value = value;
    }

}
