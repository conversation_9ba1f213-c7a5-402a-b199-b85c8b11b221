package com.deepexi.dxp.marketing.enums.activity.limit;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/10/28 16:33
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PATemplateCommodityEnum {

    /**
     * 活动模板品牌限制
     */
    PP("brandCode", "品牌"),
    /**
     * 活动模板品牌限制
     */
    PL("frontCategory", "品类"),

    /**
     * 活动模板sku限制
     */
    SKU("sku", "sku"),
    ALL_COMMODITY("allCommodity", "所有商品");


    private String id;
    private String value;
}