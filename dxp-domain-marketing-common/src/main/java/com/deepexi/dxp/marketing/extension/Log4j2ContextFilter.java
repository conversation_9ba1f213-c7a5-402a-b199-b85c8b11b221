//package com.deepexi.dxp.marketing.extension;
//
//import cn.hutool.core.date.DateTime;
//import cn.hutool.core.util.RandomUtil;
//import com.deepexi.dxp.marketing.constant.CommonConstant;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.logging.log4j.ThreadContext;
//import org.slf4j.MDC;
//import org.springframework.stereotype.Component;
//
//import javax.servlet.*;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//
//
///**
// * 日志链路id设置-过滤器
// * <AUTHOR>
// * @date 2020/9/24
// **/
//@Component
//public class Log4j2ContextFilter implements Filter {
//
//	@Override
//	public void init(FilterConfig filterConfig) throws ServletException {
//
//	}
//
//	@Override
//	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
//			throws IOException, ServletException {
//		try {
//			HttpServletRequest req = (HttpServletRequest) request;
//			HttpServletResponse resp = (HttpServletResponse) response;
//			String traceId = req.getHeader(CommonConstant.HEADER_TRACE_ID);
//			ThreadContext.remove(CommonConstant.TRACE_ID);
//			if (StringUtils.isEmpty(traceId)) {
//				//日志链路id = 3毫秒数+6位随机数
//				traceId = "T" + DateTime.now().getTime() + "" + RandomUtil.randomInt(100000, 999999);
//			}
//			ThreadContext.put(CommonConstant.TRACE_ID, traceId);
//			resp.setHeader(CommonConstant.HEADER_TRACE_ID, traceId);
//			MDC.put(CommonConstant.TRACE_ID, traceId);
//			chain.doFilter(request, response);
//		}
//		finally {
//			ThreadContext.remove(CommonConstant.TRACE_ID);
//			MDC.remove(CommonConstant.TRACE_ID);
//		}
//	}
//
//	@Override
//	public void destroy() {
//
//	}
//
//}
