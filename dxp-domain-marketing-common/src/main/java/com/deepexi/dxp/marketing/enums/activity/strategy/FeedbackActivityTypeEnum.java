package com.deepexi.dxp.marketing.enums.activity.strategy;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
public enum FeedbackActivityTypeEnum {

    /**
     * 用户反馈活动类型0、表单活动,1、其它活动
     */
    FORM(0, "表单活动"),
    OTHER(1, "其它活动");
    private Integer id;
    private String value;
}
