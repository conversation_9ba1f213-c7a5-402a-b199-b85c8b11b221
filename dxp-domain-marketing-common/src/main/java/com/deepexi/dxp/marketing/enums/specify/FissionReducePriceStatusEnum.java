package com.deepexi.dxp.marketing.enums.specify;

import lombok.Getter;

/**
 * 砍价活动状态枚举
 * <AUTHOR>
 */

@Getter
public enum FissionReducePriceStatusEnum {
    /**
     * 砍价状态  0、砍价中，1、砍价成功,2、砍价失败
     */
    BARGAINING(0,"砍价中"),
    BARGAIN_SUCCESS(1,"砍价成功"),
    BARGAIN_FAILED(2,"砍价失败"),
    ;

    private final Integer id;
    private final String value;

    FissionReducePriceStatusEnum(Integer id, String value) {
        this.id = id;
        this.value = value;
    }
}
