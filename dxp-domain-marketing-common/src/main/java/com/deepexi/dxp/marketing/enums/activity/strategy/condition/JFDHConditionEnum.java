package com.deepexi.dxp.marketing.enums.activity.strategy.condition;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @description 积分兑换的限制类型枚举
 * <AUTHOR> ming.zhong
 * @date created in 14:52 2019/11/27
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum JFDHConditionEnum {
    /**
     *
     */
    NL("numberLimit", "限制的个数"),
    JF("integral", "积分限制"),
    JE("money", "金额限制");
    private String id;
    private String value;
}
