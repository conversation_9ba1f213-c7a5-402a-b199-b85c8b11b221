package com.deepexi.dxp.marketing.enums.marketing;

public enum StatusEnum {
    ENABLE (0, "启用"),
    PROHIBIT (1, "禁用");

    public Integer getCode() {
        return code;
    }



    private final Integer code;
    private final String name;

    public String getName() {
        return name;
    }



    StatusEnum(Integer code, String name){
        this.code = code;
        this.name = name;
    }
}
