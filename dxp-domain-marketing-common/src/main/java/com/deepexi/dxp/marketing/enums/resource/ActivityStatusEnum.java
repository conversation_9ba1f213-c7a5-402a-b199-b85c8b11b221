package com.deepexi.dxp.marketing.enums.resource;

import lombok.Getter;

/**
 * 活动状态
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/12/9 10:33
 */
@Getter
public enum ActivityStatusEnum {
    /**
     * 活动状态
     */
    DRAFT("0","草稿"),
    UN_START("1","未开始"),
    IN_PROGRESS("2","进行中"),
    FINISH("3","已完成"),
    STOP("4","已终止"), //该终止状态废弃，不要使用
    OFF_SHELF("5", "已下架"),
    ;
    private final String id;
    private final String value;

    ActivityStatusEnum(String id, String value) {
        this.id = id;
        this.value = value;
    }


}
