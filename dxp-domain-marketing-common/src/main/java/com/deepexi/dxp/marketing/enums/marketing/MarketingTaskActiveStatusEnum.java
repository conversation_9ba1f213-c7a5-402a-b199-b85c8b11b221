package com.deepexi.dxp.marketing.enums.marketing;


import lombok.Getter;

/**
 * 任务状态 枚举（1:草稿，2:未开始，3:运行中，4:暂停，5:结束）
 * @Author: HuangBo.
 * @Date: 2020/3/11 14:48
 */
@Getter
public enum MarketingTaskActiveStatusEnum {
    /**
     * 草稿
     */
    DRAFT(1, "草稿"),

    /**
     * 未开始
     */
    NOT_STARTED(2, "未开始"),

    /**
     * 运行中
     */
    IN_OPERATION(3, "运行中"),

    /**
     * 暂停中
     */
    SUSPEND(4,"已暂停"),

    FINISHED(5,"已完成"),

    BREAK_OFF(6,"已终止");

    private final Integer value;

    private final String label;

    MarketingTaskActiveStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public static MarketingTaskActiveStatusEnum getEnumByValue(Integer value) {
        MarketingTaskActiveStatusEnum[] items = values();
        for (MarketingTaskActiveStatusEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }

}
