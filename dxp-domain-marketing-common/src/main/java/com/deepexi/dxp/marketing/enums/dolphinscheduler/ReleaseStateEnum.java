package com.deepexi.dxp.marketing.enums.dolphinscheduler;


import lombok.Getter;

/**
 * DolphinScheduler流程定义发布状态 枚举
 * <AUTHOR>
 * @Date 2020/3/28
 */
@Getter
public enum ReleaseStateEnum {

    /**
     * 上线
     */
    ONLINE(1, "ONLINE", "上线"),

    /**
     * 下线
     */
    OFFLINE(0, "OFFLINE", "下线");

    private final Integer code;

    private final String value;

    private final String label;

    ReleaseStateEnum(Integer code, String value, String label) {
        this.code = code;
        this.value = value;
        this.label = label;
    }

    public static ReleaseStateEnum getEnumByValue(String value) {
        ReleaseStateEnum[] items = values();
        for (ReleaseStateEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }

}
