package com.deepexi.dxp.marketing.enums.marketing;

import lombok.Getter;

/**
 * @Enum: MarketingWhiteBlackListEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/5/18
 */
@Getter
public enum MarketingTaskWhiteBlackEnum {
    WHITE(0,"白名单"),
    BLACK(1,"黑名单");

    private final Integer code;
    private final String name;

    MarketingTaskWhiteBlackEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
