<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dxp-domain-marketing</artifactId>
        <groupId>com.deepexi.dxp</groupId>
        <version>1.0.0-BASE-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <packaging>jar</packaging>
    <artifactId>dxp-domain-marketing-common</artifactId>
    <version>${dxp.marketing.common.version}</version>

    <dependencies>

        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>

        <dependency>
            <groupId>com.deepexi.dxp</groupId>
            <artifactId>dxp-domain-marketing-api</artifactId>
        </dependency>

        <!-- deepexi 依赖 == -->
        <dependency>
            <groupId>com.deepexi.cloud</groupId>
            <artifactId>deepexi-cloud-common-domain</artifactId>
            <scope>system</scope>
<!--            <systemPath>${project.basedir}/lib/deepexi-cloud-common-domain-1.1.0-release.jar</systemPath>-->
            <systemPath>${project.basedir}/lib/deepexi-cloud-common-domain-1.1.1-release.jar</systemPath>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.deepexi.cloud</groupId>
            <artifactId>deepexi-cloud-common-utils</artifactId>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/deepexi-cloud-common-utils-1.1.0-release.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.deepexi.cloud</groupId>
            <artifactId>deepexi-cloud-common-ext</artifactId>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/deepexi-cloud-common-ext-1.1.0-release.jar</systemPath>
        </dependency>
        <!-- deepexi 依赖 == -->

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.2.RELEASE</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>