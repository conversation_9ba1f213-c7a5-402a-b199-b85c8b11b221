---
inclusion: manual
---
# 华发活动服务快速参考

## 常用命令

### Maven命令
```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests

# 安装到本地仓库
mvn clean install
```

### 启动应用
```bash
# 开发环境启动
java -jar huafa-actvity-provider/target/huafa-actvity-provider-1.0.0-BASE-SNAPSHOT.jar

# 指定配置文件启动
java -jar huafa-actvity-provider/target/huafa-actvity-provider-1.0.0-BASE-SNAPSHOT.jar --spring.profiles.active=dev
```

### Docker命令
```bash
# 构建镜像
docker build -t huafa-activity-service .

# 运行容器
docker run -p 38007:38007 huafa-activity-service
```

## 关键文件位置

### 配置文件
- [pom.xml](mdc:pom.xml) - Maven配置
- [application.yml](mdc:huafa-actvity-provider/src/main/resources/application.yml) - 应用配置
- [logback-spring.xml](mdc:huafa-actvity-provider/src/main/resources/logback-spring.xml) - 日志配置

### 启动类
- [MarketingApplication.java](mdc:huafa-actvity-provider/src/main/java/com/deepexi/dxp/marketing/MarketingApplication.java) - 主启动类

### 数据库脚本
- [docs/db/](mdc:docs/db) - 数据库脚本目录
- [docs/db/alter/](mdc:docs/db/alter) - 数据库变更脚本
- [docs/db/specify/](mdc:docs/db/specify) - 特定数据库脚本

### 部署文件
- [Dockerfile](mdc:Dockerfile) - Docker配置
- [createSdk.sh](mdc:createSdk.sh) - SDK生成脚本
- [sonar-project.properties](mdc:sonar-project.properties) - 代码质量配置

## 开发环境

### 端口配置
- 应用端口：38007
- Swagger文档：http://localhost:38007/swagger-ui.html

### 常用注解
- `@SpringBootApplication` - 启动类注解
- `@EnableEurekaClient` - 服务发现
- `@EnableScheduling` - 定时任务
- `@EnableAspectJAutoProxy` - AOP切面
- `@RestController` - REST控制器
- `@Service` - 服务层
- `@Mapper` - 数据访问层
- `@LogPrint` - 日志打印
- `@GetCacheable` - 缓存获取
- `@ClearCache` - 缓存清除

### 常用工具类
- `ListUtils` - 列表工具类
- `AuthUtil` - 认证工具类
- `CheckUtils` - 校验工具类
- `AgeUtil` - 年龄计算工具类

## 模块说明

### dxp-domain-marketing-api
- 定义对外API接口
- 包含DTO和请求响应对象
- 其他服务调用此模块的接口

### dxp-domain-marketing-common
- 公共组件和工具类
- 常量定义
- 枚举类
- 配置类
- 异常处理

### dxp-middle-promotion
- 促销活动相关功能
- 活动管理
- 促销规则
- 活动计算

### dxp-middle-marketing
- 营销相关功能
- 营销策略
- 数据分析
- KPI计算

### huafa-actvity-provider
- 主服务提供者
- 控制器层
- 服务层
- 业务逻辑处理

## 调试技巧

### 日志查看
```bash
# 查看应用日志
tail -f logs/marketing.log

# 查看错误日志
grep "ERROR" logs/marketing.log
```

### 性能监控
- 使用Arthas进行性能分析
- 查看JVM内存使用情况
- 监控数据库连接池状态

### 常见问题
1. 端口被占用：修改application.yml中的端口配置
2. 数据库连接失败：检查数据库配置和网络连接
3. 缓存问题：清除Redis缓存或重启应用
4. 依赖冲突：检查pom.xml中的依赖版本

