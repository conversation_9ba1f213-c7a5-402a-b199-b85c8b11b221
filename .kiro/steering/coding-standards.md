---
inclusion: manual
---
# 华发活动服务代码规范

## 代码组织规范

### 包命名规范
- 所有包以 `com.deepexi.dxp.marketing` 开头
- 按功能模块分包：promotion（促销）、specify（特定功能）、assist（辅助）
- 按层级分包：controller、service、manager、dao、domain

### 类命名规范
- Controller类：以`Controller`结尾，如`ActivityController`
- Service类：以`Service`结尾，如`ActivityService`
- Manager类：以`Manager`结尾，如`ActivityManager`
- DAO类：以`Mapper`结尾，如`ActivityMapper`
- 实体类：使用领域名称，如`Activity`
- DTO类：以`Dto`结尾，如`ActivityDto`
- 枚举类：以`Enum`结尾，如`ActivityStatusEnum`

### 方法命名规范
- 查询方法：get、find、query、list
- 保存方法：save、create、insert
- 更新方法：update、modify
- 删除方法：delete、remove
- 业务方法：使用动词+名词，如`calculateActivityResult`

## 开发最佳实践

### 分层架构
1. **Controller层**：处理HTTP请求，参数校验，返回响应
2. **Service层**：业务逻辑处理，事务管理
3. **Manager层**：复杂业务逻辑，多Service协调
4. **DAO层**：数据访问，SQL操作

### 依赖规范
- 使用@Resource注入依赖

### 异常处理
- 使用统一的异常处理机制
- 自定义异常继承`RuntimeException`
- 在Controller层捕获异常并返回统一格式

### 日志规范
- 使用`@LogPrint`注解记录关键操作
- 日志级别：ERROR > WARN > INFO > DEBUG
- 敏感信息不记录到日志中

### 缓存使用
- 使用`@GetCacheable`注解进行缓存
- 使用`@ClearCache`注解清除缓存
- 缓存键使用业务含义明确的命名

### 数据库操作
- 使用MyBatis-Plus进行数据库操作
- 实体类使用`@TableName`注解
- 查询使用`QueryWrapper`构建条件
- 分页查询使用`Page`对象

### 配置管理
- 配置类放在`config`包下
- 使用`@ConfigurationProperties`绑定配置
- 敏感配置使用环境变量或配置中心

### 工具类使用
- 通用工具类放在`utils`包下
- 使用静态方法，避免实例化
- 工具类方法应该是无状态的

### 转换器使用
- 使用`converter`包下的转换器进行对象转换
- 避免在Service层直接进行对象转换
- 转换器应该是无状态的

## 代码质量要求

### 代码风格
- 使用Lombok简化代码
- 方法长度不超过50行
- 类长度不超过500行
- 使用有意义的变量名和方法名

### 注释规范
- 类和方法必须有JavaDoc注释
- 复杂业务逻辑需要行内注释
- 注释使用中文，便于团队理解

### 测试要求
- 核心业务逻辑必须有单元测试
- 测试类以`Test`结尾
- 测试方法名清晰表达测试意图

### 性能考虑
- 避免N+1查询问题
- 合理使用缓存
- 大数据量操作使用分页
- 异步操作使用`@Async`注解

