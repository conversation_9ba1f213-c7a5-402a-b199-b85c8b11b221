---
inclusion: manual
---
# API设计规范

## RESTful API设计原则

### URL设计规范
- 使用名词而非动词：`/activities` 而不是 `/getActivities`
- 使用复数形式：`/activities` 而不是 `/activity`
- 使用层级结构：`/activities/{id}/participants`
- 使用查询参数进行过滤：`/activities?status=active&type=promotion`

### HTTP方法使用
- GET：查询资源
- POST：创建资源
- PUT：更新整个资源
- PATCH：部分更新资源
- DELETE：删除资源

### 状态码使用
- 200：成功
- 201：创建成功
- 400：请求参数错误
- 401：未授权
- 403：禁止访问
- 404：资源不存在
- 500：服务器内部错误

## 响应格式规范

### 统一响应结构
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 分页响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 接口设计规范

### 查询接口
- 支持分页：`page`、`size`参数
- 支持排序：`sort`、`order`参数
- 支持过滤：动态查询参数
- 返回分页信息

### 创建接口
- 使用POST方法
- 返回创建的资源信息
- 包含资源ID

### 更新接口
- 使用PUT或PATCH方法
- PUT：更新整个资源
- PATCH：部分更新
- 返回更新后的资源信息

### 删除接口
- 使用DELETE方法
- 支持软删除和硬删除
- 返回操作结果

## 参数验证规范

### 请求参数验证
- 使用`@Valid`注解进行参数校验
- 定义详细的校验规则
- 提供清晰的错误信息

### 常见校验注解
- `@NotNull`：非空校验
- `@Size`：长度校验
- `@Min`、`@Max`：数值范围校验
- `@Email`：邮箱格式校验
- `@Pattern`：正则表达式校验

### 自定义校验
- 创建自定义校验注解
- 实现`ConstraintValidator`接口
- 在DTO类中使用自定义注解

## 错误处理规范

### 异常分类
- 业务异常：`BusinessException`
- 参数异常：`ParameterException`
- 系统异常：`SystemException`

### 异常信息
- 提供错误码
- 提供错误描述
- 提供解决方案建议

### 全局异常处理
- 使用`@ControllerAdvice`统一处理异常
- 根据异常类型返回不同的响应格式
- 记录异常日志

## 文档规范

### Swagger文档
- 使用`@Api`注解描述接口
- 使用`@ApiOperation`描述操作
- 使用`@ApiParam`描述参数
- 使用`@ApiModel`描述模型

### 接口文档要求
- 详细的接口描述
- 完整的参数说明
- 响应格式示例
- 错误码说明

## 安全规范

### 认证授权
- 使用JWT进行身份认证
- 实现基于角色的访问控制
- 敏感接口需要权限验证

### 数据安全
- 敏感数据加密传输
- 防止SQL注入
- 防止XSS攻击
- 输入参数过滤

## 性能优化

### 接口性能
- 合理使用缓存
- 避免N+1查询
- 使用分页查询
- 异步处理耗时操作

### 监控指标
- 接口响应时间
- 并发处理能力
- 错误率统计
- 资源使用情况

